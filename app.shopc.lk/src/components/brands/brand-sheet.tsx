"use client";

import * as React from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { Info, Image as ImageIcon, Check, X, Loader2 } from "lucide-react";
import { type z } from "zod";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { AvailabilityInput } from "@/components/ui/availability-input";
import { Textarea } from "@/components/ui/textarea";
import { AccordionTrigger, AccordionContent } from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import { brandFormSchema } from "@/lib/brands/validations";
import { ApiStatus } from "@/types/common";
import { BaseSheet } from "@/components/shared/base-sheet";
import { BrandTableData, BrandStatus, UpdateBrandDto } from "@/types/brand";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import {
  FileUploader,
  type UploadedFile,
} from "@/components/shared/file-uploader";
import {
  useBrandData,
  useBrandNameAvailability,
  useBrandSlugAvailability,
  useCreateBrand,
  useUpdateBrand,
} from "@/lib/brands/hooks";
import { useEffect } from "react";

interface BrandSheetProps {
  brand: BrandTableData | null;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: (brand?: BrandTableData) => void;
  isUpdate?: boolean;
  isDemo?: boolean;
}

type FormData = z.infer<typeof brandFormSchema>;

export function BrandSheet({
  brand,
  open,
  onOpenChange,
  onSuccess,
  isUpdate = false,
  isDemo = false,
}: BrandSheetProps) {
  const formRef = React.useRef<HTMLFormElement>(
    null
  ) as React.RefObject<HTMLFormElement>;
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [uploadedFiles, setUploadedFiles] = React.useState<UploadedFile[]>([]);
  const [ogImageFiles, setOgImageFiles] = React.useState<UploadedFile[]>([]);
  const [keywords, setKeywords] = React.useState<string>("");

  // Values for availability checking
  const [checkName, setCheckName] = React.useState<string>("");
  const [checkSlug, setCheckSlug] = React.useState<string>("");

  // Fetch complete brand data if updating
  const { data: fullBrandResponse, isLoading: isLoadingBrand } = useBrandData(
    brand?.id || "",
    isDemo
  );
  const fullBrand = fullBrandResponse?.data;

  useEffect(() => {
    if (fullBrandResponse) {
      console.log("fullBrandResponse", fullBrandResponse);
    }
  }, [fullBrandResponse]);

  // Mutation hooks for create and update operations
  const createBrandMutation = useCreateBrand(isDemo);
  const updateBrandMutation = useUpdateBrand(isDemo);

  // Availability checks (only check if not updating the same brand)
  const shouldCheckNameAvailability =
    checkName.length > 0 &&
    (!isUpdate ||
      (brand && checkName.toLowerCase() !== brand.name.toLowerCase()));
  const shouldCheckSlugAvailability =
    checkSlug.length > 0 &&
    (!isUpdate ||
      (brand && checkSlug.toLowerCase() !== brand.slug?.toLowerCase()));

  const { data: nameAvailabilityResponse, isLoading: isCheckingName } =
    useBrandNameAvailability(checkName, isDemo);
  const { data: slugAvailabilityResponse, isLoading: isCheckingSlug } =
    useBrandSlugAvailability(checkSlug, isDemo);

  const isNameAvailable = shouldCheckNameAvailability
    ? nameAvailabilityResponse?.data?.available ?? true
    : true;
  const isSlugAvailable = shouldCheckSlugAvailability
    ? slugAvailabilityResponse?.data?.available ?? true
    : true;

  const form = useForm<FormData>({
    resolver: zodResolver(brandFormSchema),
    defaultValues: {
      name: "",
      slug: "",
      availableOnline: true,
      status: BrandStatus.ACTIVE,
      // SEO fields
      seoTitle: "",
      seoDescription: "",
      seoKeywords: [],
    },
  });

  // Get the selected logo file for API calls (only if it's a real file, not the mock one for existing logos)
  const selectedLogo =
    uploadedFiles.length > 0 && !uploadedFiles[0].id.startsWith("existing-")
      ? uploadedFiles[0].file
      : null;

  // Get the selected OG image file for API calls
  const selectedOgImage =
    ogImageFiles.length > 0 && !ogImageFiles[0].id.startsWith("existing-og-")
      ? ogImageFiles[0].file
      : null;

  // Helper function to parse keywords
  const parseKeywords = (keywordsString: string): string[] => {
    return keywordsString
      .split(",")
      .map((k) => k.trim())
      .filter((k) => k.length > 0);
  };

  React.useEffect(() => {
    if (brand && fullBrand) {
      // Use full brand data for populating the form
      form.reset({
        name: fullBrand.name,
        slug: fullBrand.slug || "",
        availableOnline: fullBrand.availableOnline,
        status: fullBrand.status as BrandStatus,
        // SEO fields
        seoTitle: fullBrand.seoTitle || "",
        seoDescription: fullBrand.seoDescription || "",
        seoKeywords: fullBrand.seoKeywords || [],
      });

      // Set keywords state for UI
      setKeywords(fullBrand.seoKeywords?.join(", ") || "");

      // Trigger validation to show errors for required fields based on availableOnline status
      setTimeout(() => {
        form.trigger(["slug", "seoTitle", "seoDescription"]);
      }, 100);

      // Set existing logo as uploaded file for preview
      if (fullBrand.logo) {
        // Create a mock UploadedFile for existing logo
        setUploadedFiles([
          {
            id: `existing-${fullBrand.id}`,
            file: new File([], fullBrand.name + "-logo", {
              type: "image/jpeg",
            }),
            preview: fullBrand.logo,
          },
        ]);
      } else {
        setUploadedFiles([]);
      }

      // Set existing OG image as uploaded file for preview
      if (fullBrand.ogImage) {
        setOgImageFiles([
          {
            id: `existing-og-${fullBrand.id}`,
            file: new File([], fullBrand.name + "-og-image", {
              type: "image/jpeg",
            }),
            preview: fullBrand.ogImage,
          },
        ]);
      } else {
        setOgImageFiles([]);
      }
    } else if (!brand) {
      // Reset form for new brand
      form.reset({
        name: "",
        slug: "",
        availableOnline: true,
        status: BrandStatus.ACTIVE,
        // SEO fields
        seoTitle: "",
        seoDescription: "",
        seoKeywords: [],
      });
      setKeywords("");
      setUploadedFiles([]);
      setOgImageFiles([]);

      // Trigger validation for new brand (availableOnline defaults to true)
      setTimeout(() => {
        form.trigger(["slug", "seoTitle", "seoDescription"]);
      }, 100);
    }
  }, [brand, fullBrand]);

  // Reset form when sheet closes and trigger validation when opens
  React.useEffect(() => {
    if (!open) {
      form.reset();
      setIsSubmitting(false);
      setUploadedFiles([]);
      setOgImageFiles([]);
      setKeywords("");
    } else {
      // When sheet opens, trigger validation to show required field errors
      setTimeout(() => {
        form.trigger(["slug", "seoTitle", "seoDescription"]);
      }, 200);
    }
  }, [open]);

  const {
    formState: { errors },
  } = form;

  // Check if sections have errors
  const hasBasicInfoErrors = !!(
    errors.name ||
    errors.availableOnline ||
    errors.status ||
    (shouldCheckNameAvailability && isNameAvailable === false)
  );

  const hasDetailsErrors = !!(
    errors.slug ||
    (shouldCheckSlugAvailability && isSlugAvailable === false)
  );

  const hasSEOErrors = !!(
    errors.seoTitle ||
    errors.seoDescription ||
    errors.seoKeywords ||
    errors.slug
  );

  const handleSubmit = async () => {
    const data = form.getValues();

    // Check availability before submitting
    if (shouldCheckNameAvailability && isNameAvailable === false) {
      toast.error(
        "Brand name is already taken. Please choose a different name."
      );
      return;
    }

    if (shouldCheckSlugAvailability && isSlugAvailable === false) {
      toast.error(
        "Brand slug is already taken. Please choose a different slug."
      );
      return;
    }

    setIsSubmitting(true);

    try {
      if (brand) {
        // Update existing brand
        const updateData: UpdateBrandDto = {
          name: data.name,
          slug: data.slug || undefined,
          availableOnline: data.availableOnline,
          status: data.status,
          // SEO fields
          seoTitle: data.seoTitle || undefined,
          seoDescription: data.seoDescription || undefined,
          seoKeywords: parseKeywords(keywords),
        };

        const response = await updateBrandMutation.mutateAsync({
          id: brand.id,
          data: updateData,
          logo: selectedLogo || undefined,
          ogImage: selectedOgImage || undefined,
        });

        if (response.status === ApiStatus.SUCCESS && response.data) {
          toast.success("Brand updated successfully");
          onOpenChange?.(false);
          onSuccess?.(response.data as BrandTableData);
          return;
        }
        toast.error(response.message || "Failed to update brand");
        return; // Don't close the sheet if there was an error
      } else {
        // Create new brand
        const createData = {
          name: data.name,
          slug: data.slug || undefined,
          availableOnline: data.availableOnline,
          status: data.status,
          // SEO fields
          seoTitle: data.seoTitle || undefined,
          seoDescription: data.seoDescription || undefined,
          seoKeywords: parseKeywords(keywords),
        };

        const response = await createBrandMutation.mutateAsync({
          data: createData,
          logo: selectedLogo || undefined,
          ogImage: selectedOgImage || undefined,
        });

        if (response.status === ApiStatus.SUCCESS && response.data) {
          toast.success("Brand created successfully");
          onOpenChange?.(false);
          onSuccess?.(response.data as BrandTableData);
          return;
        }
        toast.error(response.message || "Failed to create brand");
        return; // Don't close the sheet if there was an error
      }
    } catch (error) {
      console.error("Failed to save brand:", error);
      toast.error("Failed to save brand");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Generate slug from name
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, "") // Remove special characters
      .replace(/\s+/g, "-") // Replace spaces with hyphens
      .replace(/-+/g, "-") // Replace multiple hyphens with single
      .replace(/^-|-$/g, ""); // Remove leading/trailing hyphens
  };

  // Auto-generate slug when name changes
  React.useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === "name" && value.name) {
        form.setValue("slug", generateSlug(value.name));
      }
      // Trigger validation when availableOnline changes to show/hide required field errors
      if (name === "availableOnline") {
        // Force validation of SEO fields when availableOnline changes
        form.trigger(["slug", "seoTitle", "seoDescription"]);
      }
    });
    return () => subscription.unsubscribe();
  }, [form]);

  // Handle availability checks
  const handleCheckNameAvailability = (value: string) => {
    setCheckName(value);
  };

  const handleCheckSlugAvailability = (value: string) => {
    setCheckSlug(value);
  };

  const sections = [
    {
      id: "basic-info",
      title: "Basic Information",
      icon: <Info className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasBasicInfoErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Info className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Basic Information</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <AvailabilityInput
                name="name"
                label="Name"
                placeholder="Enter brand name (e.g., Apple, Samsung, Nike)"
                value={form.watch("name") || ""}
                onChange={(value) =>
                  form.setValue("name", value, { shouldValidate: true })
                }
                onCheckAvailability={handleCheckNameAvailability}
                isCheckingAvailability={isCheckingName}
                isAvailable={isNameAvailable}
                shouldCheck={shouldCheckNameAvailability}
                error={errors.name?.message}
                disabled={isSubmitting}
                fieldName="Name"
              />

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Available Online</Label>
                  <p className="text-sm text-muted-foreground">
                    Make this brand available in the online store
                    {form.watch("availableOnline") && (
                      <span className="block text-orange-600 font-medium mt-1">
                        ⚠️ When enabled, Slug and SEO fields become required
                      </span>
                    )}
                  </p>
                </div>
                <Switch
                  checked={form.watch("availableOnline")}
                  onCheckedChange={(checked) =>
                    form.setValue("availableOnline", checked, {
                      shouldValidate: true,
                    })
                  }
                  disabled={isSubmitting}
                />
              </div>
              {errors.availableOnline && (
                <p className="text-sm text-destructive mt-1">
                  {errors.availableOnline.message}
                </p>
              )}

              <div>
                <Label>Status</Label>
                <Select
                  value={form.watch("status")}
                  onValueChange={(value) =>
                    form.setValue(
                      "status",
                      value as BrandStatus.ACTIVE | BrandStatus.INACTIVE,
                      {
                        shouldValidate: true,
                      }
                    )
                  }
                  disabled={isSubmitting}
                >
                  <SelectTrigger
                    className={cn(errors.status && "border-destructive")}
                  >
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={BrandStatus.ACTIVE}>Active</SelectItem>
                    <SelectItem value={BrandStatus.INACTIVE}>
                      Inactive
                    </SelectItem>
                  </SelectContent>
                </Select>
                {errors.status && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.status.message}
                  </p>
                )}
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "details",
      title: "Additional Details",
      icon: <Info className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasDetailsErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Info className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Additional Details</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <AvailabilityInput
                  name="slug"
                  label={`Slug${form.watch("availableOnline") ? " *" : ""}`}
                  placeholder="URL-friendly version (auto-generated from name)"
                  value={form.watch("slug") || ""}
                  onChange={(value) =>
                    form.setValue("slug", value, { shouldValidate: true })
                  }
                  onCheckAvailability={handleCheckSlugAvailability}
                  isCheckingAvailability={isCheckingSlug}
                  isAvailable={isSlugAvailable}
                  shouldCheck={shouldCheckSlugAvailability}
                  error={errors.slug?.message}
                  disabled={isSubmitting}
                  fieldName="Slug"
                />
                <p className="text-sm text-muted-foreground mt-1">
                  Used for SEO-friendly URLs (automatically generated if left
                  empty)
                  {form.watch("availableOnline") && (
                    <span className="block text-orange-600 font-medium">
                      Required when brand is available online
                    </span>
                  )}
                </p>
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "brand-logo",
      title: "Brand Logo",
      icon: <ImageIcon className="h-5 w-5 text-muted-foreground" />,
      hasErrors: false,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <ImageIcon className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Brand Logo</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <FileUploader
              files={uploadedFiles}
              onFilesChange={setUploadedFiles}
              fileType="image"
              maxFiles={1}
              maxSize={5 * 1024 * 1024} // 5MB
              multiple={false}
              label="Logo"
              description="Upload a logo for this brand. Maximum file size is 5MB."
              placeholder="Click to upload or drag and drop"
              disabled={isSubmitting}
              showPreview={true}
              previewSize="md"
              onError={(error) => {
                toast.error(error);
              }}
            />
          </AccordionContent>
        </>
      ),
    },
  ];

  return (
    <BaseSheet<BrandTableData, FormData>
      isDemo={isDemo}
      onSuccess={onSuccess}
      data={brand}
      open={open}
      onOpenChange={onOpenChange}
      isUpdate={isUpdate}
      title="Brand"
      sections={sections}
      onSubmit={handleSubmit}
      formRef={formRef}
      isSubmitting={isSubmitting}
      isPending={isLoadingBrand}
      // SEO props
      isSEOEnabled={true}
      seoTitle={form.watch("seoTitle") || ""}
      onSeoTitleChange={(value) => {
        form.setValue("seoTitle", value, { shouldValidate: true });
      }}
      seoTitleError={errors.seoTitle?.message}
      seoDescription={form.watch("seoDescription") || ""}
      onSeoDescriptionChange={(value) => {
        form.setValue("seoDescription", value, { shouldValidate: true });
      }}
      seoDescriptionError={errors.seoDescription?.message}
      seoKeywords={keywords}
      onSeoKeywordsChange={(value) => {
        setKeywords(value);
        form.setValue("seoKeywords", parseKeywords(value), {
          shouldValidate: true,
        });
      }}
      seoKeywordsError={errors.seoKeywords?.message}
      ogImageFiles={ogImageFiles}
      onOgImageFilesChange={setOgImageFiles}
    />
  );
}
