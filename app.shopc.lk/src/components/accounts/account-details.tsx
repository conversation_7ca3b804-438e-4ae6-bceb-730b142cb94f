"use client";

import * as React from "react";
import {
  Info,
  DollarSign,
  Calendar,
  User,
  Hash,
  Building,
  CreditCard,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { useMediaQuery } from "@/hooks/use-media-query";
import { useAccountData, useAccountsSlim } from "@/lib/accounts/hooks";
import {
  AccountTableData,
  AccountStatus,
  AccountSlimDto,
} from "@/types/account";
import { format } from "date-fns";
import { cn } from "@/lib/utils";

interface AccountDetailsProps {
  account: AccountTableData | null;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  isDemo?: boolean;
}

export function AccountDetails({
  account,
  open,
  onOpenChange,
  isDemo = false,
}: AccountDetailsProps) {
  const isDesktop = useMediaQuery("(min-width: 768px)");

  // Fetch complete account data
  const { data: fullAccountResponse, isLoading: isLoadingAccount } =
    useAccountData(account?.id || "", isDemo);
  const fullAccount = fullAccountResponse?.data;

  // Fetch accounts for parent account display
  const { data: accountsResponse } = useAccountsSlim(isDemo);
  const accounts = accountsResponse?.data || [];

  // Helper function to get status color
  const getStatusColor = (status: AccountStatus) => {
    switch (status) {
      case AccountStatus.ACTIVE:
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case AccountStatus.INACTIVE:
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
      case AccountStatus.DELETED:
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  // Helper function to find parent account
  const getParentAccount = () => {
    if (!fullAccount?.parentAccountId) return null;
    return accounts.find((acc) => acc.id === fullAccount.parentAccountId);
  };

  const parentAccount = getParentAccount();

  // Content component
  const Content = () => {
    if (isLoadingAccount) {
      return (
        <div className="space-y-6 p-6">
          <div className="space-y-3">
            <Skeleton className="h-8 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <Card key={i}>
                <CardHeader>
                  <Skeleton className="h-6 w-1/3" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-16 w-full" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      );
    }

    if (!fullAccount) {
      return (
        <div className="p-6 text-center">
          <p className="text-muted-foreground">Account not found</p>
        </div>
      );
    }

    return (
      <ScrollArea className="h-full max-h-[80vh] overflow-y-auto">
        <div className="space-y-6 p-6">
          {/* Header */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold">{fullAccount.accountName}</h2>
              <Badge
                className={cn(
                  "font-medium",
                  getStatusColor(fullAccount.status)
                )}
              >
                {fullAccount.status}
              </Badge>
            </div>
            {fullAccount.description && (
              <p className="text-muted-foreground">{fullAccount.description}</p>
            )}
            {fullAccount.accountNumber && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Hash className="h-4 w-4" />
                <span>Account Number: {fullAccount.accountNumber}</span>
              </div>
            )}
          </div>

          <Separator />

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Info className="h-5 w-5 text-muted-foreground" />
                  Basic Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-3">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Account Name
                    </label>
                    <p className="text-sm">{fullAccount.accountName}</p>
                  </div>
                  {fullAccount.accountNumber && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Account Number
                      </label>
                      <p className="text-sm font-mono">
                        {fullAccount.accountNumber}
                      </p>
                    </div>
                  )}
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Category
                    </label>
                    <Badge variant="outline" className="mt-1">
                      {fullAccount.accountCategory}
                    </Badge>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Type
                    </label>
                    <p className="text-sm">{fullAccount.accountType}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Detail Type
                    </label>
                    <p className="text-sm text-muted-foreground">
                      {fullAccount.accountDetailType}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Account Structure */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building className="h-5 w-5 text-muted-foreground" />
                  Account Structure
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Parent Account
                  </label>
                  {parentAccount ? (
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant="outline">
                        {parentAccount.accountName}
                      </Badge>
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground mt-1">
                      No parent account
                    </p>
                  )}
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    System Account
                  </label>
                  <div className="flex items-center gap-2 mt-1">
                    {fullAccount.isSystemAccount ? (
                      <Badge variant="destructive">System Account</Badge>
                    ) : (
                      <Badge variant="outline">User Account</Badge>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Financial Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5 text-muted-foreground" />
                  Financial Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Opening Balance
                  </label>
                  <p className="text-sm font-mono">
                    {fullAccount.openingBalance ? (
                      `$${fullAccount.openingBalance}`
                    ) : (
                      <span className="text-muted-foreground italic">
                        No opening balance
                      </span>
                    )}
                  </p>
                </div>
                {fullAccount.openingBalanceDate && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Opening Balance Date
                    </label>
                    <p className="text-sm">
                      {format(new Date(fullAccount.openingBalanceDate), "PPP")}
                    </p>
                  </div>
                )}
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Use for Billable Expenses
                  </label>
                  <div className="flex items-center gap-2 mt-1">
                    {fullAccount.useForBillableExpenses ? (
                      <Badge variant="secondary">Yes</Badge>
                    ) : (
                      <Badge variant="outline">No</Badge>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Additional Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5 text-muted-foreground" />
                  Additional Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {fullAccount.defaultTaxId && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Default Tax ID
                    </label>
                    <p className="text-sm font-mono">
                      {fullAccount.defaultTaxId}
                    </p>
                  </div>
                )}
                {fullAccount.incomeAccountId && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Income Account ID
                    </label>
                    <p className="text-sm font-mono">
                      {fullAccount.incomeAccountId}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Metadata */}
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-muted-foreground" />
                  Metadata
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <label className="font-medium text-muted-foreground">
                      Created By
                    </label>
                    <p className="flex items-center gap-2 mt-1">
                      <User className="h-4 w-4 text-muted-foreground" />
                      {fullAccount.createdBy}
                    </p>
                  </div>
                  <div>
                    <label className="font-medium text-muted-foreground">
                      Created At
                    </label>
                    <p className="flex items-center gap-2 mt-1">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      {format(new Date(fullAccount.createdAt), "PPP")}
                    </p>
                  </div>
                  <div>
                    <label className="font-medium text-muted-foreground">
                      Last Updated
                    </label>
                    <p className="flex items-center gap-2 mt-1">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      {format(new Date(fullAccount.updatedAt), "PPP")}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </ScrollArea>
    );
  };

  if (isDesktop) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl max-h-[90vh] p-0">
          <DialogHeader className="px-6 py-4 border-b">
            <DialogTitle className="flex items-center justify-between">
              <span>Account Details</span>
              {isDemo && (
                <Badge variant="outline" className="ml-2">
                  Demo Mode
                </Badge>
              )}
            </DialogTitle>
          </DialogHeader>
          <Content />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={onOpenChange}>
      <DrawerContent className="max-h-[90vh]">
        <DrawerHeader className="border-b">
          <DrawerTitle className="flex items-center justify-between">
            <span>Account Details</span>
            {isDemo && (
              <Badge variant="outline" className="ml-2">
                Demo Mode
              </Badge>
            )}
          </DrawerTitle>
        </DrawerHeader>
        <Content />
      </DrawerContent>
    </Drawer>
  );
}
