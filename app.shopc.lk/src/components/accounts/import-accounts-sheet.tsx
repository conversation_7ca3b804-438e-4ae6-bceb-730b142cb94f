/**
 * ImportAccountsSheet Component
 *
 * This component allows importing accounts with the following features:
 * - Manual data entry or file upload (CSV/Excel)
 * - Field validation and error handling
 * - Integration with bulk import API
 * - Account name and number availability checking
 */

"use client";

import { useState, useEffect, useCallback } from "react";
import {
  ImportDataSheet,
  FieldMapping,
  FieldConfig,
} from "../data-import/import-data-sheet";
import {
  ImportAccountSchema,
  GetAccountsSchema,
  AccountStatus,
} from "@/lib/accounts/validations";
import {
  AccountCategory,
  ChartAccountType,
  AccountDetailType,
} from "@/types/account.enum";

import { ApiStatus } from "@/types/common";
import {
  useAccountsSlim,
  useAccountsData,
  useAccountNameAvailability,
  useAccountNumberAvailability,
  useBulkImportAccounts,
} from "@/lib/accounts/hooks";

// Define the account field types that can be imported
export type AccountImportFields =
  | "accountName"
  | "accountNumber"
  | "accountCategory"
  | "accountType"
  | "accountDetailType"
  | "parentAccountId"
  | "openingBalance"
  | "openingBalanceDate"
  | "description"
  | "defaultTaxId"
  | "useForBillableExpenses"
  | "incomeAccountId"
  | "isSystemAccount"
  | "status";

// All possible fields for account import
const ALL_ACCOUNT_FIELDS: AccountImportFields[] = [
  "accountName",
  "accountNumber",
  "accountCategory",
  "accountType",
  "accountDetailType",
  "parentAccountId",
  "openingBalance",
  "openingBalanceDate",
  "description",
  "defaultTaxId",
  "useForBillableExpenses",
  "incomeAccountId",
  "isSystemAccount",
  "status",
];

export type ImportAccountsSheetProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
  isDemo?: boolean;
};

export function ImportAccountsSheet({
  open,
  onOpenChange,
  onSuccess,
  isDemo = false,
}: ImportAccountsSheetProps) {
  // Hook for bulk importing accounts
  const bulkImportMutation = useBulkImportAccounts(isDemo);

  // Fetch accounts for parent selection (slim data)
  const { data: accountsResponse } = useAccountsSlim(isDemo);
  const accounts = accountsResponse?.data || [];

  // Fetch full accounts data for availability checking
  const { data: fullAccountsResponse } = useAccountsData(
    {
      page: 1,
      limit: 1000, // Get all accounts for availability checking
    } as GetAccountsSchema,
    isDemo
  );
  const fullAccounts = fullAccountsResponse?.data?.data || [];

  // Track validation state for availability checking
  const [currentValidatingName, setCurrentValidatingName] =
    useState<string>("");
  const [currentValidatingNumber, setCurrentValidatingNumber] =
    useState<string>("");

  // Debounced availability checking
  const debouncedCheckAvailability = useCallback(
    (field: string, value: string) => {
      if (!value || !value.trim()) return;

      const trimmedValue = value.trim();

      setTimeout(() => {
        switch (field) {
          case "accountName":
            if (currentValidatingName !== trimmedValue) {
              setCurrentValidatingName(trimmedValue);
            }
            break;
          case "accountNumber":
            if (currentValidatingNumber !== trimmedValue) {
              setCurrentValidatingNumber(trimmedValue);
            }
            break;
        }
      }, 300); // 300ms debounce
    },
    [currentValidatingName, currentValidatingNumber]
  );

  // Use availability hooks for current validating values
  const { data: nameAvailability, isLoading: isLoadingNameCheck } =
    useAccountNameAvailability(currentValidatingName, isDemo);
  const { data: numberAvailability, isLoading: isLoadingNumberCheck } =
    useAccountNumberAvailability(currentValidatingNumber, isDemo);

  // Build a cache of checked availability
  const [availabilityCache, setAvailabilityCache] = useState<{
    names: Map<string, boolean>;
    numbers: Map<string, boolean>;
  }>({
    names: new Map(),
    numbers: new Map(),
  });

  // Update cache when availability results come in
  useEffect(() => {
    if (
      currentValidatingName &&
      nameAvailability?.data?.available !== undefined
    ) {
      setAvailabilityCache((prev) => {
        const newCache = { ...prev };
        newCache.names.set(
          currentValidatingName.toLowerCase(),
          nameAvailability.data!.available
        );
        return newCache;
      });
    }
  }, [currentValidatingName, nameAvailability]);

  useEffect(() => {
    if (
      currentValidatingNumber &&
      numberAvailability?.data?.available !== undefined
    ) {
      setAvailabilityCache((prev) => {
        const newCache = { ...prev };
        newCache.numbers.set(
          currentValidatingNumber,
          numberAvailability.data!.available
        );
        return newCache;
      });
    }
  }, [currentValidatingNumber, numberAvailability]);

  // Helper function to check for duplicates within the dataset
  const checkForInternalDuplicates = (
    data: Record<string, any>[],
    currentIndex: number,
    field: string,
    value: string
  ): boolean => {
    if (!value || !value.trim()) return false;

    const normalizedValue = value.trim().toLowerCase();

    return data.some((row, index) => {
      if (index === currentIndex) return false; // Skip current row
      if (!row[field] || !row[field].trim()) return false;

      const otherValue = row[field].trim().toLowerCase();
      return otherValue === normalizedValue;
    });
  };

  // Helper function to check if a field is currently being validated (loading)
  const isFieldLoading = (field: string, value: string): boolean => {
    if (!value || !value.trim()) return false;

    const trimmedValue = value.trim();
    switch (field) {
      case "accountName":
        return currentValidatingName === trimmedValue && isLoadingNameCheck;
      case "accountNumber":
        return currentValidatingNumber === trimmedValue && isLoadingNumberCheck;
      default:
        return false;
    }
  };

  // Helper function to check availability against existing data
  const checkAvailabilityAgainstExisting = (
    field: string,
    value: string
  ): boolean => {
    if (!value || !value.trim()) return true;

    const normalizedValue = value.trim().toLowerCase();

    // First check the availability cache from hooks
    let cachedResult: boolean | undefined;
    switch (field) {
      case "accountName":
        cachedResult = availabilityCache.names.get(normalizedValue);
        break;
      case "accountNumber":
        cachedResult = availabilityCache.numbers.get(value.trim());
        break;
    }

    // If we have a cached result, use it
    if (cachedResult !== undefined) {
      return cachedResult;
    }

    // Check against existing accounts data as fallback
    return fullAccounts.every((account) => {
      if (!account[field as keyof typeof account]) return true;

      const existingValue = (
        account[field as keyof typeof account] as string
      ).toLowerCase();
      return existingValue !== normalizedValue;
    });
  };

  // Field configurations for accounts
  const ACCOUNT_FIELD_CONFIGS: FieldConfig[] = [
    {
      name: "accountName",
      type: "text",
      defaultValue: "",
      placeholder: "Enter account name (e.g., Cash, Accounts Receivable)",
      isLoading: (value: string) => isFieldLoading("accountName", value),
      onValueChange: (value: string) =>
        debouncedCheckAvailability("accountName", value),
    },
    {
      name: "accountNumber",
      type: "text",
      defaultValue: "",
      placeholder: "Enter account number (e.g., 1000, 1200)",
      isLoading: (value: string) => isFieldLoading("accountNumber", value),
      onValueChange: (value: string) =>
        debouncedCheckAvailability("accountNumber", value),
    },
    {
      name: "accountCategory",
      type: "select",
      options: Object.values(AccountCategory).map((category) => ({
        value: category,
        label: category,
      })),
      defaultValue: AccountCategory.ASSETS,
    },
    {
      name: "accountType",
      type: "select",
      options: Object.values(ChartAccountType).map((type) => ({
        value: type,
        label: type,
      })),
      defaultValue: ChartAccountType.CURRENT_ASSETS,
    },
    {
      name: "accountDetailType",
      type: "select",
      options: Object.values(AccountDetailType).map((detailType) => ({
        value: detailType,
        label: detailType,
      })),
      defaultValue: AccountDetailType.OTHER_CURRENT_ASSETS,
    },
    {
      name: "parentAccountId",
      type: "select",
      options: [
        { value: "none", label: "No Parent Account" },
        ...accounts.map((account) => ({
          value: account.id,
          label: `${account.accountName}${
            account.accountNumber ? ` (${account.accountNumber})` : ""
          }`,
        })),
      ],
      defaultValue: "none",
    },
    {
      name: "openingBalance",
      type: "text",
      defaultValue: "",
      placeholder: "Enter opening balance (e.g., 1000.00)",
    },
    {
      name: "openingBalanceDate",
      type: "text",
      defaultValue: "",
      placeholder: "Enter date (YYYY-MM-DD)",
    },
    {
      name: "description",
      type: "text",
      defaultValue: "",
      placeholder: "Enter account description",
    },
    {
      name: "useForBillableExpenses",
      type: "select",
      options: [
        { value: "false", label: "No" },
        { value: "true", label: "Yes" },
      ],
      defaultValue: "false",
    },
    {
      name: "isSystemAccount",
      type: "select",
      options: [
        { value: "false", label: "No" },
        { value: "true", label: "Yes" },
      ],
      defaultValue: "false",
    },
    {
      name: "status",
      type: "select",
      options: [
        { value: "ACTIVE", label: "Active" },
        { value: "INACTIVE", label: "Inactive" },
      ],
      defaultValue: "ACTIVE",
    },
  ];

  // Enhanced validation function for accounts with uniqueness checks
  const validateAccountRow = (
    row: Record<string, any>,
    rowIndex?: number,
    allData?: Record<string, any>[]
  ) => {
    const errors: Record<string, string> = {};
    let valid = true;

    // Validate required fields
    if (
      !row.accountName ||
      typeof row.accountName !== "string" ||
      row.accountName.trim() === ""
    ) {
      errors.accountName = "Account name is required";
      valid = false;
    } else {
      // Check uniqueness within dataset
      if (allData && rowIndex !== undefined) {
        if (
          checkForInternalDuplicates(
            allData,
            rowIndex,
            "accountName",
            row.accountName
          )
        ) {
          errors.accountName =
            "Account name must be unique within the import data";
          valid = false;
        }
      }

      // Check availability against existing data
      if (!checkAvailabilityAgainstExisting("accountName", row.accountName)) {
        errors.accountName = "Account name already exists";
        valid = false;
      }
    }

    // Validate account number if present
    if (
      row.accountNumber !== undefined &&
      row.accountNumber !== null &&
      row.accountNumber !== ""
    ) {
      if (typeof row.accountNumber !== "string") {
        errors.accountNumber = "Account number must be a string";
        valid = false;
      } else {
        // Check uniqueness within dataset
        if (allData && rowIndex !== undefined) {
          if (
            checkForInternalDuplicates(
              allData,
              rowIndex,
              "accountNumber",
              row.accountNumber
            )
          ) {
            errors.accountNumber =
              "Account number must be unique within the import data";
            valid = false;
          }
        }

        // Check availability against existing data
        if (
          !checkAvailabilityAgainstExisting("accountNumber", row.accountNumber)
        ) {
          errors.accountNumber = "Account number already exists";
          valid = false;
        }
      }
    }

    // Validate required enum fields
    if (!Object.values(AccountCategory).includes(row.accountCategory)) {
      errors.accountCategory = "Invalid account category";
      valid = false;
    }

    if (!Object.values(ChartAccountType).includes(row.accountType)) {
      errors.accountType = "Invalid account type";
      valid = false;
    }

    if (!Object.values(AccountDetailType).includes(row.accountDetailType)) {
      errors.accountDetailType = "Invalid account detail type";
      valid = false;
    }

    return { valid, errors };
  };

  // Transform function to convert form data to API format
  const transformRowToAccount = (
    row: Record<string, any>
  ): ImportAccountSchema => {
    const transformedRow: ImportAccountSchema = {
      accountName: row.accountName?.trim() || "",
      accountCategory: row.accountCategory || AccountCategory.ASSETS,
      accountType: row.accountType || ChartAccountType.CURRENT_ASSETS,
      accountDetailType:
        row.accountDetailType || AccountDetailType.OTHER_CURRENT_ASSETS,
      useForBillableExpenses: false,
      isSystemAccount: false,
      status: AccountStatus.ACTIVE,
    };

    // Add optional fields if they exist and are not empty
    if (row.accountNumber && row.accountNumber.trim()) {
      transformedRow.accountNumber = row.accountNumber.trim();
    }

    if (row.parentAccountId && row.parentAccountId !== "none") {
      transformedRow.parentAccountId = row.parentAccountId;
    }


    if (row.openingBalance && row.openingBalance.trim()) {
      transformedRow.openingBalance = row.openingBalance.trim();
    }

    if (row.openingBalanceDate && row.openingBalanceDate.trim()) {
      transformedRow.openingBalanceDate = row.openingBalanceDate.trim();
    }

    if (row.description && row.description.trim()) {
      transformedRow.description = row.description.trim();
    }

    if (row.defaultTaxId && row.defaultTaxId.trim()) {
      transformedRow.defaultTaxId = row.defaultTaxId.trim();
    }

    if (row.useForBillableExpenses !== undefined) {
      transformedRow.useForBillableExpenses =
        row.useForBillableExpenses === "true" ||
        row.useForBillableExpenses === true;
    }

    if (row.incomeAccountId && row.incomeAccountId.trim()) {
      transformedRow.incomeAccountId = row.incomeAccountId.trim();
    }

    if (row.isSystemAccount !== undefined) {
      transformedRow.isSystemAccount =
        row.isSystemAccount === "true" || row.isSystemAccount === true;
    }

    if (row.status && Object.values(AccountStatus).includes(row.status)) {
      transformedRow.status = row.status;
    }

    return transformedRow;
  };

  // Handle submission of account data
  const handleSubmitAccounts = async (
    data: any[],
    _mappings: FieldMapping[]
  ) => {
    try {
      // Transform data to ImportAccountSchema format
      const accountsData: ImportAccountSchema[] = data.map(
        transformRowToAccount
      );

      // Use the mutation hook to import accounts
      const result = await bulkImportMutation.mutateAsync(accountsData);

      if (result.status === ApiStatus.SUCCESS) {
        onSuccess();
        onOpenChange(false);
      } else {
        throw new Error(result.message || "Import failed");
      }
    } catch (error) {
      console.error("Import error:", error);
      throw error;
    }
  };

  return (
    <ImportDataSheet
      open={open}
      onOpenChange={onOpenChange}
      title="Import Accounts"
      description="Import accounts from a CSV or Excel file. Make sure your file includes the required fields."
      targetFields={ALL_ACCOUNT_FIELDS}
      fieldConfigs={ACCOUNT_FIELD_CONFIGS}
      validateRow={validateAccountRow}
      onSubmit={handleSubmitAccounts}
      requireAllFields={false}
      defaultRowCount={1}
    />
  );
}
