"use client";

import * as React from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import {
  Info,
  Receipt,
  DollarSign,
  Calendar,
  FileText,
  Paperclip,
  Check,
  X,
  Loader2,
} from "lucide-react";
import { type z } from "zod";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { DatePicker } from "@/components/ui/date-picker";
import { AccordionTrigger, AccordionContent } from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import { expenseFormSchema } from "@/lib/expenses/validations";
import { ApiStatus } from "@/types/common";
import { BaseSheet } from "@/components/shared/base-sheet";
import {
  ExpenseTableData,
  ExpenseStatus,
  PayeeType,
  AmountType,
  UpdateExpenseDto,
  ExpenseDto,
} from "@/types/expense";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  FileUploader,
  type UploadedFile,
} from "@/components/shared/file-uploader";
import {
  useExpense,
  useCreateExpense,
  useUpdateExpense,
} from "@/lib/expenses/hooks";
import { useEffect } from "react";

interface ExpenseSheetProps {
  expense: ExpenseTableData | null;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: (expense?: ExpenseTableData) => void;
  isUpdate?: boolean;
  isDemo?: boolean;
}

type FormData = z.infer<typeof expenseFormSchema>;

export function ExpenseSheet({
  expense,
  open,
  onOpenChange,
  onSuccess,
  isUpdate = false,
  isDemo = false,
}: ExpenseSheetProps) {
  const formRef = React.useRef<HTMLFormElement>(
    null
  ) as React.RefObject<HTMLFormElement>;
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [uploadedFiles, setUploadedFiles] = React.useState<UploadedFile[]>([]);

  // Fetch complete expense data if updating
  const { data: fullExpenseResponse, isLoading: isLoadingExpense } =
    useExpense(expense?.id || "", isDemo);
  const fullExpense = fullExpenseResponse?.data;

  useEffect(() => {
    if (fullExpenseResponse) {
      console.log("fullExpenseResponse", fullExpenseResponse);
    }
  }, [fullExpenseResponse]);

  // Mutation hooks for create and update operations
  const createExpenseMutation = useCreateExpense(isDemo);
  const updateExpenseMutation = useUpdateExpense(isDemo);

  const form = useForm<FormData>({
    resolver: zodResolver(expenseFormSchema),
    defaultValues: {
      payeeType: PayeeType.SUPPLIER,
      payeeId: "",
      paymentAccountId: "",
      paymentDate: new Date().toISOString().split('T')[0],
      paymentMethodId: "",
      referenceNumber: "",
      amountType: AmountType.EXCLUSIVE_OF_TAX,
      subtotal: 0,
      total: 0,
      memo: "",
      status: ExpenseStatus.DRAFT,
      expenseCategoryLineItems: [],
      expenseProductServiceLineItems: [],
    },
  });

  // Get the selected attachment files for API calls
  const selectedAttachments = uploadedFiles
    .filter(file => !file.id.startsWith("existing-"))
    .map(file => file.file)
    .filter((file): file is File => file !== null);

  React.useEffect(() => {
    if (expense && fullExpense) {
      // Use full expense data for populating the form
      form.reset({
        payeeType: fullExpense.payeeType,
        payeeId: fullExpense.payeeId,
        paymentAccountId: fullExpense.paymentAccountId || "",
        paymentDate: fullExpense.paymentDate,
        paymentMethodId: fullExpense.paymentMethodId || "",
        referenceNumber: fullExpense.referenceNumber || "",
        amountType: fullExpense.amountType,
        subtotal: fullExpense.subtotal,
        total: fullExpense.total,
        memo: fullExpense.memo || "",
        status: fullExpense.status,
        expenseCategoryLineItems: fullExpense.expenseCategoryLineItems || [],
        expenseProductServiceLineItems: (fullExpense.expenseProductServiceLineItems || []).map(item => ({
          itemType: item.itemType as "product" | "service",
          itemId: item.itemId,
          description: item.description,
          quantity: item.quantity,
          rate: item.rate,
          amount: item.amount,
          taxId: item.taxId,
          lineOrder: item.lineOrder,
        })),
      });

      // Set existing attachments as uploaded files for preview
      if (fullExpense.attachments && fullExpense.attachments.length > 0) {
        const existingFiles = fullExpense.attachments.map((attachment, index) => ({
          id: `existing-${fullExpense.id}-${index}`,
          file: new File([], `attachment-${index + 1}`, { type: "application/pdf" }),
          preview: attachment,
        }));
        setUploadedFiles(existingFiles);
      } else {
        setUploadedFiles([]);
      }
    } else if (!expense) {
      // Reset form for new expense
      form.reset({
        payeeType: PayeeType.SUPPLIER,
        payeeId: "",
        paymentAccountId: "",
        paymentDate: new Date().toISOString().split('T')[0],
        paymentMethodId: "",
        referenceNumber: "",
        amountType: AmountType.EXCLUSIVE_OF_TAX,
        subtotal: 0,
        total: 0,
        memo: "",
        status: ExpenseStatus.DRAFT,
        expenseCategoryLineItems: [],
        expenseProductServiceLineItems: [],
      });
      setUploadedFiles([]);
    }
  }, [expense, fullExpense]);

  // Reset form when sheet closes
  React.useEffect(() => {
    if (!open) {
      form.reset();
      setIsSubmitting(false);
      setUploadedFiles([]);
    }
  }, [open]);

  const {
    formState: { errors },
  } = form;

  // Check if sections have errors
  const hasBasicInfoErrors = !!(
    errors.payeeType ||
    errors.payeeId ||
    errors.paymentDate ||
    errors.status
  );

  const hasPaymentErrors = !!(
    errors.paymentAccountId ||
    errors.paymentMethodId ||
    errors.referenceNumber
  );

  const hasAmountErrors = !!(
    errors.amountType ||
    errors.subtotal ||
    errors.total
  );

  const hasDetailsErrors = !!(
    errors.memo ||
    errors.expenseCategoryLineItems ||
    errors.expenseProductServiceLineItems
  );

  const handleSubmit = async () => {
    const data = form.getValues();

    setIsSubmitting(true);

    try {
      if (expense) {
        // Update existing expense
        const updateData: UpdateExpenseDto = {
          payeeType: data.payeeType,
          payeeId: data.payeeId,
          paymentAccountId: data.paymentAccountId || undefined,
          paymentDate: data.paymentDate,
          paymentMethodId: data.paymentMethodId || undefined,
          referenceNumber: data.referenceNumber || undefined,
          amountType: data.amountType,
          subtotal: data.subtotal,
          total: data.total,
          memo: data.memo || undefined,
          status: data.status,
          expenseCategoryLineItems: data.expenseCategoryLineItems,
          expenseProductServiceLineItems: data.expenseProductServiceLineItems,
        };

        const response = await updateExpenseMutation.mutateAsync({
          id: expense.id,
          data: updateData,
          attachments: selectedAttachments.length > 0 ? selectedAttachments : undefined,
        });

        if (response.status === ApiStatus.SUCCESS && response.data) {
          toast.success("Expense updated successfully");
          onOpenChange?.(false);
          onSuccess?.(response.data as ExpenseTableData);
          return;
        }
        toast.error(response.message || "Failed to update expense");
        return; // Don't close the sheet if there was an error
      } else {
        // Create new expense
        const createData = {
          payeeType: data.payeeType,
          payeeId: data.payeeId,
          paymentAccountId: data.paymentAccountId || undefined,
          paymentDate: data.paymentDate,
          paymentMethodId: data.paymentMethodId || undefined,
          referenceNumber: data.referenceNumber || undefined,
          amountType: data.amountType,
          subtotal: data.subtotal,
          total: data.total,
          memo: data.memo || undefined,
          status: data.status,
          expenseCategoryLineItems: data.expenseCategoryLineItems,
          expenseProductServiceLineItems: data.expenseProductServiceLineItems,
        };

        const response = await createExpenseMutation.mutateAsync({
          data: createData,
          attachments: selectedAttachments.length > 0 ? selectedAttachments : undefined,
        });

        if (response.status === ApiStatus.SUCCESS && response.data) {
          toast.success("Expense created successfully");
          onOpenChange?.(false);
          onSuccess?.(response.data as ExpenseTableData);
          return;
        }
        toast.error(response.message || "Failed to create expense");
        return; // Don't close the sheet if there was an error
      }
    } catch (error) {
      console.error("Failed to save expense:", error);
      toast.error("Failed to save expense");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Auto-calculate total when subtotal changes
  React.useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === "subtotal" && value.subtotal !== undefined) {
        // For now, just set total equal to subtotal
        // In a real application, you might add tax calculations here
        form.setValue("total", value.subtotal);
      }
    });
    return () => subscription.unsubscribe();
  }, [form]);

  const sections = [
    {
      id: "basic-info",
      title: "Basic Information",
      icon: <Info className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasBasicInfoErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Info className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Basic Information</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Payee Type</Label>
                <Select
                  value={form.watch("payeeType")}
                  onValueChange={(value) =>
                    form.setValue("payeeType", value as PayeeType, {
                      shouldValidate: true,
                    })
                  }
                  disabled={isSubmitting}
                >
                  <SelectTrigger
                    className={cn(errors.payeeType && "border-destructive")}
                  >
                    <SelectValue placeholder="Select payee type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={PayeeType.SUPPLIER}>Supplier</SelectItem>
                    <SelectItem value={PayeeType.STAFF}>Staff</SelectItem>
                    <SelectItem value={PayeeType.CUSTOMER}>Customer</SelectItem>
                    <SelectItem value={PayeeType.PROJECT}>Project</SelectItem>
                  </SelectContent>
                </Select>
                {errors.payeeType && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.payeeType.message}
                  </p>
                )}
              </div>

              <div>
                <Label>Payee ID</Label>
                <Input
                  {...form.register("payeeId")}
                  name="payeeId"
                  placeholder="Enter payee identifier"
                  className={cn(
                    errors.payeeId && "border-destructive"
                  )}
                  disabled={isSubmitting}
                />
                {errors.payeeId && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.payeeId.message}
                  </p>
                )}
              </div>

              <div>
                <DatePicker
                  name="paymentDate"
                  label="Payment Date"
                  value={form.watch("paymentDate") ? new Date(form.watch("paymentDate")) : undefined}
                  onChange={(date) => {
                    const dateString = date ? date.toISOString().split('T')[0] : "";
                    form.setValue("paymentDate", dateString, { shouldValidate: true });
                  }}
                  placeholder="Select payment date"
                  disabled={isSubmitting}
                  error={errors.paymentDate?.message}
                />
              </div>

              <div>
                <Label>Status</Label>
                <Select
                  value={form.watch("status")}
                  onValueChange={(value) =>
                    form.setValue("status", value as ExpenseStatus, {
                      shouldValidate: true,
                    })
                  }
                  disabled={isSubmitting}
                >
                  <SelectTrigger
                    className={cn(errors.status && "border-destructive")}
                  >
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={ExpenseStatus.DRAFT}>Draft</SelectItem>
                    <SelectItem value={ExpenseStatus.SUBMITTED}>
                      Submitted
                    </SelectItem>
                    <SelectItem value={ExpenseStatus.APPROVED}>
                      Approved
                    </SelectItem>
                    <SelectItem value={ExpenseStatus.REJECTED}>
                      Rejected
                    </SelectItem>
                  </SelectContent>
                </Select>
                {errors.status && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.status.message}
                  </p>
                )}
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "payment-details",
      title: "Payment Details",
      icon: <Receipt className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasPaymentErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Receipt className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Payment Details</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Payment Account ID</Label>
                <Input
                  {...form.register("paymentAccountId")}
                  name="paymentAccountId"
                  placeholder="Enter payment account identifier"
                  className={cn(
                    errors.paymentAccountId && "border-destructive"
                  )}
                  disabled={isSubmitting}
                />
                {errors.paymentAccountId && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.paymentAccountId.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Optional payment account reference
                </p>
              </div>

              <div>
                <Label>Payment Method ID</Label>
                <Input
                  {...form.register("paymentMethodId")}
                  name="paymentMethodId"
                  placeholder="Enter payment method identifier"
                  className={cn(
                    errors.paymentMethodId && "border-destructive"
                  )}
                  disabled={isSubmitting}
                />
                {errors.paymentMethodId && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.paymentMethodId.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Optional payment method reference
                </p>
              </div>

              <div>
                <Label>Reference Number</Label>
                <Input
                  {...form.register("referenceNumber")}
                  name="referenceNumber"
                  placeholder="Enter reference number"
                  className={cn(
                    errors.referenceNumber && "border-destructive"
                  )}
                  disabled={isSubmitting}
                />
                {errors.referenceNumber && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.referenceNumber.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Optional reference number for tracking
                </p>
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "amounts",
      title: "Amounts",
      icon: <DollarSign className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasAmountErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <DollarSign className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Amounts</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Amount Type</Label>
                <Select
                  value={form.watch("amountType")}
                  onValueChange={(value) =>
                    form.setValue("amountType", value as AmountType, {
                      shouldValidate: true,
                    })
                  }
                  disabled={isSubmitting}
                >
                  <SelectTrigger
                    className={cn(errors.amountType && "border-destructive")}
                  >
                    <SelectValue placeholder="Select amount type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={AmountType.EXCLUSIVE_OF_TAX}>
                      Exclusive of Tax
                    </SelectItem>
                    <SelectItem value={AmountType.INCLUSIVE_OF_TAX}>
                      Inclusive of Tax
                    </SelectItem>
                    <SelectItem value={AmountType.OUT_OF_SCOPE}>
                      Out of Scope
                    </SelectItem>
                  </SelectContent>
                </Select>
                {errors.amountType && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.amountType.message}
                  </p>
                )}
              </div>

              <div>
                <Label>Subtotal</Label>
                <Input
                  {...form.register("subtotal", { valueAsNumber: true })}
                  name="subtotal"
                  type="number"
                  step="0.01"
                  min="0"
                  placeholder="0.00"
                  className={cn(
                    errors.subtotal && "border-destructive"
                  )}
                  disabled={isSubmitting}
                />
                {errors.subtotal && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.subtotal.message}
                  </p>
                )}
              </div>

              <div>
                <Label>Total</Label>
                <Input
                  {...form.register("total", { valueAsNumber: true })}
                  name="total"
                  type="number"
                  step="0.01"
                  min="0"
                  placeholder="0.00"
                  className={cn(
                    errors.total && "border-destructive"
                  )}
                  disabled={isSubmitting}
                />
                {errors.total && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.total.message}
                  </p>
                )}
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "details",
      title: "Additional Details",
      icon: <FileText className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasDetailsErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Additional Details</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Memo</Label>
                <Textarea
                  {...form.register("memo")}
                  name="memo"
                  placeholder="Enter additional notes or memo"
                  className={cn(errors.memo && "border-destructive")}
                  disabled={isSubmitting}
                  rows={3}
                />
                {errors.memo && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.memo.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Optional notes about this expense
                </p>
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "attachments",
      title: "Attachments",
      icon: <Paperclip className="h-5 w-5 text-muted-foreground" />,
      hasErrors: false,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Paperclip className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Attachments</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <FileUploader
              files={uploadedFiles}
              onFilesChange={setUploadedFiles}
              fileType="document"
              maxFiles={5}
              maxSize={10 * 1024 * 1024} // 10MB
              multiple={true}
              label="Attachments"
              description="Upload receipts, invoices, or other supporting documents. Maximum 5 files, 10MB each."
              placeholder="Click to upload or drag and drop"
              disabled={isSubmitting}
              showPreview={true}
              previewSize="sm"
              onError={(error) => {
                toast.error(error);
              }}
            />
          </AccordionContent>
        </>
      ),
    },
  ];

  return (
    <BaseSheet<ExpenseTableData, FormData>
      isDemo={isDemo}
      onSuccess={onSuccess}
      data={expense}
      open={open}
      onOpenChange={onOpenChange}
      isUpdate={isUpdate}
      title="Expense"
      sections={sections}
      onSubmit={handleSubmit}
      formRef={formRef}
      isSubmitting={isSubmitting}
      isPending={isLoadingExpense}
      // Location props - disabled for expenses
      isLocationEnabled={false}
      // SEO props - disabled for expenses
      isSEOEnabled={false}
    />
  );
}
