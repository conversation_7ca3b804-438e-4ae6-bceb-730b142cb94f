"use client";

import { usePathname, useRouter } from "next/navigation";
import { CreditCard, Tags } from "lucide-react";
import { useState, useCallback } from "react";
import { ExpenseSheet } from "@/components/expenses/expense-sheet";
import { ExpenseCategorySheet } from "@/components/expenses-categories/expense-category-sheet";
import { useQueryClient } from "@tanstack/react-query";
import { expenseKeys } from "@/lib/expenses/hooks";
import { useModules } from "@/contexts/auth-contexts";
import { ModuleType } from "@/types/business-modules";

import DashboardLayout, {
  SectionCategory,
  NavigationViewType,
} from "@/components/layout/dashboard-layout";
import { ImportExpenseCategoriesSheet } from "../expenses-categories/import-expenses-categories-sheet";

// Expense categories for the tabs with their routes
const ALL_EXPENSE_CATEGORIES: SectionCategory[] = [
  {
    name: "expenses",
    path: "expenses",
    icon: CreditCard,
    module: ModuleType.EXPENSES,
    label: "Expenses",
  },
  {
    name: "expense-categories",
    path: "expense-categories",
    icon: Tags,
    module: ModuleType.EXPENSE_CATEGORIES,
    label: "Expense Categories",
  },
];

export default function ExpensesLayout({
  children,
  isDemo = false,
}: {
  children: React.ReactNode;
  isDemo?: boolean;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const queryClient = useQueryClient();
  const { hasAccess } = useModules();

  const [openExpenseSheet, setOpenExpenseSheet] = useState(false);
  const [openExpenseCategorySheet, setOpenExpenseCategorySheet] =
    useState(false);
  const [
    openImportExpenseCategoriesSheet,
    setOpenImportExpenseCategoriesSheet,
  ] = useState(false);

  // Handle success for expense operations - invalidate queries and refresh
  const handleExpenseSuccess = useCallback(() => {
    // queryClient.invalidateQueries({ queryKey: expenseKeys.list() });
    router.refresh();
  }, [queryClient, router]);

  // const handleExpenseCategorySuccess = useCallback(() => {
  //   queryClient.invalidateQueries({ queryKey: expenseCategoryKeys.list() });
  //   router.refresh();
  // }, [router, queryClient]);

  const handleAddNew = () => {
    // For expense categories, open the category sheet
    if (pathname.includes("/expense-categories")) {
      setOpenExpenseCategorySheet(true);
    } else if (pathname.includes("/expenses")) {
      setOpenExpenseSheet(true);
    }
  };

  const handleImport = useCallback(() => {
    if (pathname.includes("/expense-categories")) {
      setOpenImportExpenseCategoriesSheet(true);
    }
  }, [pathname]);

  const getAddButtonText = () => {
    if (pathname.includes("/expense-categories")) return "Add New Category";
    return "Add New Expense";
  };

  const getImportButtonText = () => {
    if (pathname.includes("/expense-categories")) return "Import Categories";
    return "Import Expenses";
  };

  return (
    <DashboardLayout
      isDemo={isDemo}
      title="Expenses"
      categories={ALL_EXPENSE_CATEGORIES}
      hasAccess={hasAccess}
      addAction={{
        text: getAddButtonText(),
        onClick: handleAddNew,
      }}
      importAction={{
        text: getImportButtonText(),
        onClick: handleImport,
      }}
      sheets={
        <>
          <ExpenseSheet
            expense={null}
            open={openExpenseSheet}
            onOpenChange={setOpenExpenseSheet}
            isDemo={isDemo}
            onSuccess={handleExpenseSuccess}
          />
          <ExpenseCategorySheet
            expenseCategory={null}
            open={openExpenseCategorySheet}
            onOpenChange={setOpenExpenseCategorySheet}
            isDemo={isDemo}
            onSuccess={handleExpenseSuccess}
          />
          <ImportExpenseCategoriesSheet
            open={openImportExpenseCategoriesSheet}
            onOpenChange={setOpenImportExpenseCategoriesSheet}
            isDemo={isDemo}
            onSuccess={handleExpenseSuccess}
          />
        </>
      }
    >
      {children}
    </DashboardLayout>
  );
}
