"use client";

import { usePathname, useRouter } from "next/navigation";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Users, Building2 } from "lucide-react";
import { useState, useCallback } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { useModules } from "@/contexts/auth-contexts";
import { ModuleType } from "@/types/business-modules";
import { serviceKeys } from "@/lib/services/hooks";
import { serviceCategoryKeys } from "@/lib/service-categories/hooks";

import DashboardLayout, {
  SectionCategory,
  NavigationViewType,
} from "@/components/layout/dashboard-layout";
import { ServiceCategorySheet } from "../service-categories/service-category-sheet";
import { ImportServiceCategoriesSheet } from "../service-categories/import-service-categories-sheet";
import { ServiceSheet } from "../services/service-sheet";

// Service categories for the tabs with their routes
const ALL_SERVICE_CATEGORIES: SectionCategory[] = [
  {
    name: "services",
    path: "services",
    icon: Wrench,
    module: ModuleType.SERVICES,
    label: "Services",
  },
  {
    name: "service-categories",
    path: "service-categories",
    icon: Building2,
    module: ModuleType.SERVICES,
    label: "Service Categories",
  },
];

export default function ServiceLayout({
  children,
  isDemo = false,
}: {
  children: React.ReactNode;
  isDemo?: boolean;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const queryClient = useQueryClient();
  const { hasAccess } = useModules();

  const [openServiceSheet, setOpenServiceSheet] = useState(false);
  const [openServiceTypeSheet, setOpenServiceTypeSheet] = useState(false);
  const [openImportServicesSheet, setOpenImportServicesSheet] = useState(false);
  const [
    openImportServiceCategoriesSheet,
    setOpenImportServiceCategoriesSheet,
  ] = useState(false);
  const [openServiceCategorySheet, setOpenServiceCategorySheet] =
    useState(false);

  // Handle success for service operations - invalidate queries and refresh
  const handleServiceSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: serviceKeys.all });
    router.refresh();
  }, [queryClient, router]);

  const handleServiceCategorySuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: serviceCategoryKeys.all });
    router.refresh();
  }, [queryClient, router]);

  const handleImportServicesSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: serviceKeys.all });
    router.refresh();
  }, [router, queryClient]);

  const handleImportServiceCategoriesSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: serviceCategoryKeys.all });
    router.refresh();
  }, [router, queryClient]);

  const handleAddNew = () => {
    // For different service routes, open the appropriate sheet
    if (
      pathname.includes("/services") &&
      !pathname.includes("/service-types") &&
      !pathname.includes("/service-providers") &&
      !pathname.includes("/service-categories")
    ) {
      setOpenServiceSheet(true);
    } else if (pathname.includes("/service-categories")) {
      setOpenServiceCategorySheet(true);
    } else if (pathname.includes("/service-providers")) {
      // TODO: Add service provider sheet when implemented
      setOpenServiceSheet(true);
    }
  };

  const handleImport = useCallback(() => {
    if (
      pathname.includes("/services") &&
      !pathname.includes("/service-types") &&
      !pathname.includes("/service-providers") &&
      !pathname.includes("/service-categories")
    ) {
      setOpenImportServicesSheet(true);
    } else if (pathname.includes("/service-categories")) {
      setOpenImportServiceCategoriesSheet(true);
    } else if (pathname.includes("/service-providers")) {
      // TODO: Add import service providers when implemented
      setOpenImportServicesSheet(true);
    }
  }, [pathname]);

  const getAddButtonText = () => {
    if (pathname.includes("/service-categories"))
      return "Import Service Categories";
    if (pathname.includes("/service-providers"))
      return "Add New Service Provider";
    if (pathname.includes("/service-categories"))
      return "Add New Service Category";
    return "Add New Service";
  };

  const getImportButtonText = () => {
    if (pathname.includes("/service-categories"))
      return "Import Service Categories";
    if (pathname.includes("/service-providers"))
      return "Import Service Providers";
    if (pathname.includes("/service-categories"))
      return "Import Service Categories";
    return "Import Services";
  };

  return (
    <DashboardLayout
      isDemo={isDemo}
      title="Services"
      categories={ALL_SERVICE_CATEGORIES}
      hasAccess={hasAccess}
      addAction={{
        text: getAddButtonText(),
        onClick: handleAddNew,
      }}
      importAction={{
        text: getImportButtonText(),
        onClick: handleImport,
      }}
      sheets={
        <>
          <ServiceSheet
            service={null}
            open={openServiceSheet}
            onOpenChange={setOpenServiceSheet}
            isDemo={isDemo}
            onSuccess={handleServiceSuccess}
          />
          <ServiceCategorySheet
            serviceCategory={null}
            open={openServiceCategorySheet}
            onOpenChange={setOpenServiceCategorySheet}
            isDemo={isDemo}
            onSuccess={handleServiceCategorySuccess}
          />
          <ImportServiceCategoriesSheet
            open={openImportServiceCategoriesSheet}
            onOpenChange={setOpenImportServiceCategoriesSheet}
            isDemo={isDemo}
            onSuccess={handleImportServiceCategoriesSuccess}
          />
        </>
      }
    >
      {children}
    </DashboardLayout>
  );
}
