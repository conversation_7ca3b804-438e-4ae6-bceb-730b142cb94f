"use client";

import * as React from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Info, Clock, Shield, DollarSign, Settings } from "lucide-react";
import { type z } from "zod";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { AccordionTrigger, AccordionContent } from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import {
  warrantyTemplateFormSchema,
  getDurationTypeOptions,
  getWarrantyTypeOptions,
  getCoverageTypeOptions,
  getStatusOptions,
  generateTemplateCode,
} from "@/lib/warranty-templates/validations";
import { BaseSheet } from "@/components/shared/base-sheet";
import {
  WarrantyTemplateDto,
  UpdateWarrantyTemplateDto,
  CreateWarrantyTemplateDto,
  WarrantyStatus,
  DurationType,
  WarrantyType,
  CoverageType,
} from "@/types/warranty-templates";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import {
  useCreateWarrantyTemplate,
  useUpdateWarrantyTemplate,
} from "@/lib/warranty-templates/hooks";

interface WarrantyTemplateSheetProps {
  warrantyTemplate: WarrantyTemplateDto | null;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: (warrantyTemplate?: WarrantyTemplateDto) => void;
  isUpdate?: boolean;
  isDemo?: boolean;
}

type FormData = z.infer<typeof warrantyTemplateFormSchema>;

export function WarrantyTemplateSheet({
  warrantyTemplate,
  open,
  onOpenChange,
  onSuccess,
  isUpdate = false,
  isDemo = false,
}: WarrantyTemplateSheetProps) {
  const formRef = React.useRef<HTMLFormElement>(
    null
  ) as React.RefObject<HTMLFormElement>;
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [autoGenerateCode, setAutoGenerateCode] = React.useState(!isUpdate);

  const createMutation = useCreateWarrantyTemplate(isDemo);
  const updateMutation = useUpdateWarrantyTemplate(isDemo);

  const form = useForm<FormData>({
    resolver: zodResolver(warrantyTemplateFormSchema),
    defaultValues: {
      templateCode: warrantyTemplate?.templateCode ?? "",
      templateName: warrantyTemplate?.templateName ?? "",
      warrantyType: warrantyTemplate?.warrantyType ?? WarrantyType.MANUFACTURER,
      duration: warrantyTemplate?.duration ?? undefined,
      durationType: warrantyTemplate?.durationType ?? DurationType.MONTHS,
      coverageType: warrantyTemplate?.coverageType ?? CoverageType.FULL,
      coverageDetails: warrantyTemplate?.coverageDetails ?? "",
      maxClaims: warrantyTemplate?.maxClaims ?? undefined,
      termsConditions: warrantyTemplate?.termsConditions ?? "",
      isTransferable: warrantyTemplate?.isTransferable ?? false,
      autoApply: warrantyTemplate?.autoApply ?? false,
      applicableCategories: warrantyTemplate?.applicableCategories ?? "",
      status: warrantyTemplate?.status ?? WarrantyStatus.ACTIVE,
    },
  });

  // Watch fields for auto-generation and conditional validation
  const watchedDurationType = form.watch("durationType");
  const watchedDuration = form.watch("duration");
  const watchedTemplateName = form.watch("templateName");
  const watchedWarrantyType = form.watch("warrantyType");

  React.useEffect(() => {
    if (warrantyTemplate) {
      form.reset({
        templateCode: warrantyTemplate.templateCode,
        templateName: warrantyTemplate.templateName,
        warrantyType: warrantyTemplate.warrantyType,
        duration: warrantyTemplate.duration,
        durationType: warrantyTemplate.durationType,
        coverageType: warrantyTemplate.coverageType,
        coverageDetails: warrantyTemplate.coverageDetails,
        maxClaims: warrantyTemplate.maxClaims,
        termsConditions: warrantyTemplate.termsConditions,
        isTransferable: warrantyTemplate.isTransferable,
        autoApply: warrantyTemplate.autoApply,
        applicableCategories: warrantyTemplate.applicableCategories,
        status: warrantyTemplate.status,
      });
    } else {
      form.reset({
        templateCode: "",
        templateName: "",
        warrantyType: WarrantyType.MANUFACTURER,
        duration: undefined,
        durationType: DurationType.MONTHS,
        coverageType: CoverageType.FULL,
        coverageDetails: "",
        maxClaims: undefined,
        termsConditions: "",
        isTransferable: false,
        autoApply: false,
        applicableCategories: "",
        status: WarrantyStatus.ACTIVE,
      });
    }
  }, [warrantyTemplate]);

  // Reset form when sheet closes
  React.useEffect(() => {
    if (!open) {
      form.reset();
      setIsSubmitting(false);
      setAutoGenerateCode(!isUpdate);
    }
  }, [open, isUpdate]);

  // Handle duration type changes
  React.useEffect(() => {
    if (watchedDurationType === DurationType.LIFETIME) {
      form.setValue("duration", null);
    } else if (
      (!watchedDuration || watchedDuration === null) &&
      watchedDurationType
    ) {
      form.setValue("duration", 12);
    }
  }, [watchedDurationType, watchedDuration]);

  // Auto-generate template code
  React.useEffect(() => {
    if (autoGenerateCode && watchedTemplateName && watchedWarrantyType) {
      const generatedCode = generateTemplateCode(
        watchedTemplateName,
        watchedWarrantyType,
        watchedDuration ?? undefined,
        watchedDurationType
      );
      form.setValue("templateCode", generatedCode);
    }
  }, [
    autoGenerateCode,
    watchedTemplateName,
    watchedWarrantyType,
    watchedDuration,
    watchedDurationType,
    form,
  ]);

  const {
    formState: { errors },
  } = form;

  // Check if sections have errors
  const hasBasicInfoErrors = !!(errors.templateCode || errors.templateName);
  const hasWarrantyInfoErrors = !!(
    errors.warrantyType ||
    errors.duration ||
    errors.durationType
  );
  const hasCoverageErrors = !!(errors.coverageType || errors.coverageDetails);
  const hasPricingErrors = !!(errors.maxClaims);
  const hasSettingsErrors = !!(
    errors.termsConditions || errors.applicableCategories
  );

  const handleSubmit = async () => {
    const data = form.getValues();

    const isValid = await form.trigger();
    if (!isValid) {
      return;
    }

    setIsSubmitting(true);

    try {
      if (warrantyTemplate) {
        // Update existing warranty template
        const updateData: UpdateWarrantyTemplateDto = {
          templateCode: data.templateCode,
          templateName: data.templateName,
          warrantyType: data.warrantyType,
          duration:
            data.durationType === DurationType.LIFETIME
              ? undefined
              : data.duration || undefined,
          durationType: data.durationType || DurationType.MONTHS,
          coverageType: data.coverageType,
          coverageDetails: data.coverageDetails || undefined,
          maxClaims: data.maxClaims || undefined,
          termsConditions: data.termsConditions || undefined,
          isTransferable: data.isTransferable,
          autoApply: data.autoApply,
          applicableCategories: data.applicableCategories || undefined,
          status: data.status,
        };

        await updateMutation.mutateAsync({
          id: warrantyTemplate.id,
          data: updateData,
        });

        onOpenChange?.(false);
        onSuccess?.(warrantyTemplate);
      } else {
        // Create new warranty template
        const createData: CreateWarrantyTemplateDto = {
          templateCode: data.templateCode,
          templateName: data.templateName,
          warrantyType: data.warrantyType,
          duration:
            data.durationType === DurationType.LIFETIME
              ? undefined
              : data.duration || undefined,
          durationType: data.durationType || DurationType.MONTHS,
          coverageType: data.coverageType,
          coverageDetails: data.coverageDetails || undefined,
          maxClaims: data.maxClaims || undefined,
          termsConditions: data.termsConditions || undefined,
          isTransferable: data.isTransferable,
          autoApply: data.autoApply,
          applicableCategories: data.applicableCategories || undefined,
          status: data.status,
        };

        await createMutation.mutateAsync(createData);

        onOpenChange?.(false);
        onSuccess?.();
      }
    } catch (error) {
      console.error("Failed to save warranty template:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const sections = [
    {
      id: "basic-info",
      title: "Basic Information",
      icon: <Info className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasBasicInfoErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Info className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Basic Information</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Template Name</Label>
                <Input
                  {...form.register("templateName")}
                  placeholder="Enter template name (e.g., Standard Electronics Warranty)"
                  className={cn(errors.templateName && "border-destructive")}
                  disabled={isSubmitting}
                />
                {errors.templateName && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.templateName.message}
                  </p>
                )}
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <Label>Template Code</Label>
                  {!isUpdate && (
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="auto-generate"
                        checked={autoGenerateCode}
                        onCheckedChange={setAutoGenerateCode}
                      />
                      <Label
                        htmlFor="auto-generate"
                        className="text-sm text-muted-foreground"
                      >
                        Auto-generate
                      </Label>
                    </div>
                  )}
                </div>
                <Input
                  {...form.register("templateCode")}
                  placeholder="Enter unique template code (e.g., ELEC-STD-12M)"
                  className={cn(errors.templateCode && "border-destructive")}
                  disabled={isSubmitting || autoGenerateCode}
                />
                {errors.templateCode && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.templateCode.message}
                  </p>
                )}
                <p className="text-xs text-muted-foreground mt-1">
                  Unique identifier for this warranty template
                </p>
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "warranty-info",
      title: "Warranty Details",
      icon: <Clock className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasWarrantyInfoErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Warranty Details</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Warranty Type</Label>
                <Select
                  onValueChange={(value) =>
                    form.setValue("warrantyType", value as WarrantyType, {
                      shouldValidate: true,
                    })
                  }
                  value={form.watch("warrantyType")}
                  disabled={isSubmitting}
                >
                  <SelectTrigger
                    className={cn(errors.warrantyType && "border-destructive")}
                  >
                    <SelectValue placeholder="Select warranty type" />
                  </SelectTrigger>
                  <SelectContent>
                    {getWarrantyTypeOptions().map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.warrantyType && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.warrantyType.message}
                  </p>
                )}
              </div>

              <div>
                <Label>Duration Type</Label>
                <Select
                  onValueChange={(value) => {
                    if (value && Object.values(DurationType).includes(value as DurationType)) {
                      form.setValue("durationType", value as DurationType, {
                        shouldValidate: true,
                      });
                    }
                  }}
                  value={form.watch("durationType") || DurationType.MONTHS}
                  disabled={isSubmitting}
                >
                  <SelectTrigger
                    className={cn(errors.durationType && "border-destructive")}
                  >
                    <SelectValue placeholder="Select duration type" />
                  </SelectTrigger>
                  <SelectContent>
                    {getDurationTypeOptions().map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.durationType && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.durationType.message}
                  </p>
                )}
              </div>

              {watchedDurationType !== DurationType.LIFETIME && (
                <div>
                  <Label>
                    Duration Value
                    <span className="text-muted-foreground ml-1">
                      (in {watchedDurationType?.toLowerCase()})
                    </span>
                  </Label>
                  <Input
                    {...form.register("duration", {
                      valueAsNumber: true,
                    })}
                    type="number"
                    min="1"
                    placeholder={`Enter number of ${watchedDurationType?.toLowerCase()}`}
                    className={cn(errors.duration && "border-destructive")}
                    disabled={isSubmitting}
                  />
                  {errors.duration && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.duration.message}
                    </p>
                  )}
                </div>
              )}

              {watchedDurationType === DurationType.LIFETIME && (
                <div className="rounded-md bg-blue-50 border border-blue-200 p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <Info className="h-5 w-5 text-blue-400" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-blue-800">
                        Lifetime Warranty
                      </h3>
                      <div className="mt-2 text-sm text-blue-700">
                        <p>
                          This warranty will provide coverage for the entire
                          lifetime of the product. No duration value is needed.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "coverage-info",
      title: "Coverage Details",
      icon: <Shield className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasCoverageErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Coverage Details</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Coverage Type</Label>
                <Select
                  onValueChange={(value) =>
                    form.setValue("coverageType", value as CoverageType, {
                      shouldValidate: true,
                    })
                  }
                  value={form.watch("coverageType")}
                  disabled={isSubmitting}
                >
                  <SelectTrigger
                    className={cn(errors.coverageType && "border-destructive")}
                  >
                    <SelectValue placeholder="Select coverage type" />
                  </SelectTrigger>
                  <SelectContent>
                    {getCoverageTypeOptions().map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.coverageType && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.coverageType.message}
                  </p>
                )}
              </div>

              <div>
                <Label>Coverage Details</Label>
                <Textarea
                  {...form.register("coverageDetails")}
                  placeholder="Describe what is covered (e.g., Manufacturing defects, parts replacement, labor costs)"
                  className={cn(errors.coverageDetails && "border-destructive")}
                  disabled={isSubmitting}
                  rows={3}
                />
                {errors.coverageDetails && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.coverageDetails.message}
                  </p>
                )}
                <p className="text-xs text-muted-foreground mt-1">
                  Detailed explanation of what this warranty covers
                </p>
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "pricing-info",
      title: "Claims",
      icon: <DollarSign className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasPricingErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <DollarSign className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Claims</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">

              <div>
                <Label>Maximum Claims</Label>
                <Input
                  {...form.register("maxClaims", {
                    valueAsNumber: true,
                  })}
                  type="number"
                  min="1"
                  placeholder="Unlimited"
                  className={cn(errors.maxClaims && "border-destructive")}
                  disabled={isSubmitting}
                />
                {errors.maxClaims && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.maxClaims.message}
                  </p>
                )}
                <p className="text-xs text-muted-foreground mt-1">
                  Leave empty for unlimited claims
                </p>
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "settings-info",
      title: "Settings & Terms",
      icon: <Settings className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasSettingsErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Settings className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Settings & Terms</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Transferable</Label>
                    <p className="text-sm text-muted-foreground">
                      Allow warranty to be transferred to new owners
                    </p>
                  </div>
                  <Switch
                    checked={form.watch("isTransferable")}
                    onCheckedChange={(checked) =>
                      form.setValue("isTransferable", checked)
                    }
                    disabled={isSubmitting}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Auto Apply</Label>
                    <p className="text-sm text-muted-foreground">
                      Automatically apply this warranty to eligible products
                    </p>
                  </div>
                  <Switch
                    checked={form.watch("autoApply")}
                    onCheckedChange={(checked) =>
                      form.setValue("autoApply", checked)
                    }
                    disabled={isSubmitting}
                  />
                </div>
              </div>

              <div>
                <Label>Applicable Categories</Label>
                <Input
                  {...form.register("applicableCategories")}
                  placeholder="electronics,mobile,laptop (comma-separated)"
                  className={cn(
                    errors.applicableCategories && "border-destructive"
                  )}
                  disabled={isSubmitting}
                />
                {errors.applicableCategories && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.applicableCategories.message}
                  </p>
                )}
                <p className="text-xs text-muted-foreground mt-1">
                  Product categories this warranty applies to
                </p>
              </div>

              <div>
                <Label>Terms & Conditions</Label>
                <Textarea
                  {...form.register("termsConditions")}
                  placeholder="Enter warranty terms and conditions..."
                  className={cn(errors.termsConditions && "border-destructive")}
                  disabled={isSubmitting}
                  rows={4}
                />
                {errors.termsConditions && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.termsConditions.message}
                  </p>
                )}
                <p className="text-xs text-muted-foreground mt-1">
                  Legal terms and conditions for this warranty
                </p>
              </div>

              <div>
                <Label>Status</Label>
                <Select
                  onValueChange={(value) =>
                    form.setValue("status", value as WarrantyStatus, {
                      shouldValidate: true,
                    })
                  }
                  value={form.watch("status")}
                  disabled={isSubmitting}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    {getStatusOptions().map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
  ];

  return (
    <BaseSheet<WarrantyTemplateDto, FormData>
      isDemo={isDemo}
      onSuccess={onSuccess}
      data={warrantyTemplate}
      open={open}
      onOpenChange={onOpenChange}
      isUpdate={isUpdate}
      title="Warranty Template"
      sections={sections}
      onSubmit={handleSubmit}
      formRef={formRef}
      isSubmitting={isSubmitting}
    />
  );
}
