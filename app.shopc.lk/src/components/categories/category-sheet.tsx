"use client";

import * as React from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import {
  Info,
  Image as ImageIcon,
  FolderTree,
  Check,
  X,
  Loader2,
} from "lucide-react";
import { type z } from "zod";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { AvailabilityInput } from "@/components/ui/availability-input";
import { Textarea } from "@/components/ui/textarea";
import { AccordionTrigger, AccordionContent } from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import { categoryFormSchema } from "@/lib/categories/validations";
import { ApiStatus } from "@/types/common";
import { BaseSheet } from "@/components/shared/base-sheet";
import {
  CategoryTableData,
  CategoryStatus,
  UpdateCategoryDto,
  CategorySlimDto,
} from "@/types/category";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import {
  FileUploader,
  type UploadedFile,
} from "@/components/shared/file-uploader";
import {
  useCategoriesSlim,
  useCategoryData,
  useCategoryNameAvailability,
  useCategorySlugAvailability,
  useCategoryShortCodeAvailability,
  useCreateCategory,
  useUpdateCategory,
} from "@/lib/categories/hooks";
import { useEffect } from "react";

interface CategorySheetProps {
  category: CategoryTableData | null;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: (category?: CategoryTableData) => void;
  isUpdate?: boolean;
  isDemo?: boolean;
}

type FormData = z.infer<typeof categoryFormSchema>;

export function CategorySheet({
  category,
  open,
  onOpenChange,
  onSuccess,
  isUpdate = false,
  isDemo = false,
}: CategorySheetProps) {
  const formRef = React.useRef<HTMLFormElement>(
    null
  ) as React.RefObject<HTMLFormElement>;
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [uploadedFiles, setUploadedFiles] = React.useState<UploadedFile[]>([]);
  const [ogImageFiles, setOgImageFiles] = React.useState<UploadedFile[]>([]);
  const [keywords, setKeywords] = React.useState<string>("");

  // Values for availability checking
  const [checkName, setCheckName] = React.useState<string>("");
  const [checkSlug, setCheckSlug] = React.useState<string>("");
  const [checkShortCode, setCheckShortCode] = React.useState<string>("");

  // Fetch complete category data if updating
  const { data: fullCategoryResponse, isLoading: isLoadingCategory } =
    useCategoryData(category?.id || "", isDemo);
  const fullCategory = fullCategoryResponse?.data;

  useEffect(() => {
    if (fullCategoryResponse) {
      console.log("fullCategoryResponse", fullCategoryResponse);
    }
  }, [fullCategoryResponse]);

  // Fetch categories for parent selection
  const { data: categoriesResponse } = useCategoriesSlim(isDemo);
  const categories = React.useMemo(
    () => categoriesResponse?.data || [],
    [categoriesResponse?.data]
  );

  // Mutation hooks for create and update operations
  const createCategoryMutation = useCreateCategory(isDemo);
  const updateCategoryMutation = useUpdateCategory(isDemo);

  // Availability checks (only check if not updating the same category)
  const shouldCheckNameAvailability =
    checkName.length > 0 &&
    (!isUpdate ||
      (category && checkName.toLowerCase() !== category.name.toLowerCase()));
  const shouldCheckSlugAvailability =
    checkSlug.length > 0 &&
    (!isUpdate ||
      (category && checkSlug.toLowerCase() !== category.slug?.toLowerCase()));
  const shouldCheckShortCodeAvailability =
    checkShortCode.length > 0 &&
    (!isUpdate ||
      (category &&
        checkShortCode.toUpperCase() !== category.shortCode?.toUpperCase()));

  const { data: nameAvailabilityResponse, isLoading: isCheckingName } =
    useCategoryNameAvailability(checkName, isDemo);
  const { data: slugAvailabilityResponse, isLoading: isCheckingSlug } =
    useCategorySlugAvailability(checkSlug, isDemo);
  const {
    data: shortCodeAvailabilityResponse,
    isLoading: isCheckingShortCode,
  } = useCategoryShortCodeAvailability(checkShortCode, isDemo);

  const isNameAvailable = shouldCheckNameAvailability
    ? nameAvailabilityResponse?.data?.available ?? true
    : true;
  const isSlugAvailable = shouldCheckSlugAvailability
    ? slugAvailabilityResponse?.data?.available ?? true
    : true;
  const isShortCodeAvailable = shouldCheckShortCodeAvailability
    ? shortCodeAvailabilityResponse?.data?.available ?? true
    : true;

  // Helper function to flatten categories into a single list for parent selection
  const flattenCategories = (
    categories: CategorySlimDto[]
  ): CategorySlimDto[] => {
    const flattened: CategorySlimDto[] = [];

    const addCategory = (cat: CategorySlimDto) => {
      flattened.push(cat);
      if (cat.subcategories && cat.subcategories.length > 0) {
        cat.subcategories.forEach(addCategory);
      }
    };

    categories.forEach(addCategory);
    return flattened;
  };

  const flatCategories = React.useMemo(
    () => flattenCategories(categories),
    [categories]
  );

  const form = useForm<FormData>({
    resolver: zodResolver(categoryFormSchema),
    defaultValues: {
      name: "",
      shortCode: "",
      parentId: null,
      description: "",
      slug: "",
      availableOnline: true,
      color: "",
      status: CategoryStatus.ACTIVE,
      // Location fields
      isAllocatedToAllLocations: false,
      locationIds: [],
      // SEO fields
      seoTitle: "",
      seoDescription: "",
      seoKeywords: [],
    },
  });

  // Get the selected image file for API calls (only if it's a real file, not the mock one for existing images)
  const selectedImage =
    uploadedFiles.length > 0 && !uploadedFiles[0].id.startsWith("existing-")
      ? uploadedFiles[0].file
      : null;

  // Get the selected OG image file for API calls
  const selectedOgImage =
    ogImageFiles.length > 0 && !ogImageFiles[0].id.startsWith("existing-og-")
      ? ogImageFiles[0].file
      : null;

  // Helper function to parse keywords
  const parseKeywords = (keywordsString: string): string[] => {
    return keywordsString
      .split(",")
      .map((k) => k.trim())
      .filter((k) => k.length > 0);
  };

  React.useEffect(() => {
    if (category && fullCategory) {
      // Use full category data for populating the form
      // Find the parent ID if parent exists
      const parentId = fullCategory.parentId || null;

      form.reset({
        name: fullCategory.name,
        shortCode: fullCategory.shortCode || "",
        parentId: parentId,
        description: fullCategory.description || "",
        slug: fullCategory.slug || "",
        availableOnline: fullCategory.availableOnline,
        color: fullCategory.color || "",
        status: fullCategory.status as CategoryStatus,
        // Location fields
        isAllocatedToAllLocations:
          fullCategory.isAllocatedToAllLocations || false,
        locationIds: fullCategory.locations?.map((loc) => loc.id) || [],
        // SEO fields
        seoTitle: fullCategory.seoTitle || "",
        seoDescription: fullCategory.seoDescription || "",
        seoKeywords: fullCategory.seoKeywords || [],
      });

      // Set keywords state for UI
      setKeywords(fullCategory.seoKeywords?.join(", ") || "");

      // Trigger validation to show errors for required fields based on availableOnline status
      setTimeout(() => {
        form.trigger(["slug", "seoTitle", "seoDescription"]);
      }, 100);

      // Set existing image as uploaded file for preview
      if (fullCategory.image) {
        // Create a mock UploadedFile for existing image
        setUploadedFiles([
          {
            id: `existing-${fullCategory.id}`,
            file: new File([], fullCategory.name + "-image", {
              type: "image/jpeg",
            }),
            preview: fullCategory.image,
          },
        ]);
      } else {
        setUploadedFiles([]);
      }

      // Set existing OG image as uploaded file for preview
      if (fullCategory.ogImage) {
        setOgImageFiles([
          {
            id: `existing-og-${fullCategory.id}`,
            file: new File([], fullCategory.name + "-og-image", {
              type: "image/jpeg",
            }),
            preview: fullCategory.ogImage,
          },
        ]);
      } else {
        setOgImageFiles([]);
      }
    } else if (!category) {
      // Reset form for new category
      form.reset({
        name: "",
        shortCode: "",
        parentId: null,
        description: "",
        slug: "",
        availableOnline: true,
        color: "",
        status: CategoryStatus.ACTIVE,
        // Location fields
        isAllocatedToAllLocations: false,
        locationIds: [],
        // SEO fields
        seoTitle: "",
        seoDescription: "",
        seoKeywords: [],
      });
      setKeywords("");
      setUploadedFiles([]);
      setOgImageFiles([]);

      // Trigger validation for new category (availableOnline defaults to true)
      setTimeout(() => {
        form.trigger(["slug", "seoTitle", "seoDescription"]);
      }, 100);
    }
  }, [category, fullCategory]);

  // Reset form when sheet closes and trigger validation when opens
  React.useEffect(() => {
    if (!open) {
      form.reset();
      setIsSubmitting(false);
      setUploadedFiles([]);
      setOgImageFiles([]);
      setKeywords("");
    } else {
      // When sheet opens, trigger validation to show required field errors
      setTimeout(() => {
        form.trigger(["slug", "seoTitle", "seoDescription"]);
      }, 200);
    }
  }, [open]);

  const {
    formState: { errors },
  } = form;

  // Check if sections have errors
  const hasBasicInfoErrors = !!(
    errors.name ||
    errors.shortCode ||
    errors.color ||
    errors.availableOnline ||
    errors.status ||
    (shouldCheckNameAvailability && isNameAvailable === false) ||
    (shouldCheckShortCodeAvailability && isShortCodeAvailable === false)
  );

  const hasHierarchyErrors = !!errors.parentId;

  const hasDetailsErrors = !!(
    errors.description ||
    errors.slug ||
    (shouldCheckSlugAvailability && isSlugAvailable === false)
  );

  const hasSEOErrors = !!(
    errors.seoTitle ||
    errors.seoDescription ||
    errors.seoKeywords ||
    errors.slug
  );

  const handleSubmit = async () => {
    const data = form.getValues();

    // Check availability before submitting
    if (shouldCheckNameAvailability && isNameAvailable === false) {
      toast.error(
        "Category name is already taken. Please choose a different name."
      );
      return;
    }

    if (shouldCheckSlugAvailability && isSlugAvailable === false) {
      toast.error(
        "Category slug is already taken. Please choose a different slug."
      );
      return;
    }

    if (shouldCheckShortCodeAvailability && isShortCodeAvailable === false) {
      toast.error(
        "Category short code is already taken. Please choose a different short code."
      );
      return;
    }

    setIsSubmitting(true);

    try {
      // Helper function to convert null to undefined for parentId
      const getParentId = (
        parentId: string | null | undefined
      ): string | undefined => {
        return parentId === null ? undefined : parentId || undefined;
      };

      if (category) {
        // Update existing category
        const updateData: UpdateCategoryDto = {
          name: data.name,
          shortCode: data.shortCode || undefined,
          parentId: getParentId(data.parentId),
          description: data.description || undefined,
          slug: data.slug || undefined,
          availableOnline: data.availableOnline,
          color: data.color || undefined,
          status: data.status,
          // Location fields
          isAllocatedToAllLocations: data.isAllocatedToAllLocations,
          locationIds:
            data.isAllocatedToAllLocations ||
            !data.locationIds ||
            data.locationIds.length === 0
              ? undefined
              : data.locationIds,
          // SEO fields
          seoTitle: data.seoTitle || undefined,
          seoDescription: data.seoDescription || undefined,
          seoKeywords: parseKeywords(keywords),
        };

        const response = await updateCategoryMutation.mutateAsync({
          id: category.id,
          data: updateData,
          image: selectedImage || undefined,
          ogImage: selectedOgImage || undefined,
        });

        if (response.status === ApiStatus.SUCCESS && response.data) {
          toast.success("Category updated successfully");
          onOpenChange?.(false);
          onSuccess?.(response.data as CategoryTableData);
          return;
        }
        toast.error(response.message || "Failed to update category");
        return; // Don't close the sheet if there was an error
      } else {
        // Create new category
        const createData = {
          name: data.name,
          shortCode: data.shortCode || undefined,
          parentId: getParentId(data.parentId),
          description: data.description || undefined,
          slug: data.slug || undefined,
          availableOnline: data.availableOnline,
          color: data.color || undefined,
          status: data.status,
          // Location fields
          isAllocatedToAllLocations: data.isAllocatedToAllLocations,
          locationIds:
            data.isAllocatedToAllLocations ||
            !data.locationIds ||
            data.locationIds.length === 0
              ? undefined
              : data.locationIds,
          // SEO fields
          seoTitle: data.seoTitle || undefined,
          seoDescription: data.seoDescription || undefined,
          seoKeywords: parseKeywords(keywords),
        };

        const response = await createCategoryMutation.mutateAsync({
          data: createData,
          image: selectedImage || undefined,
          ogImage: selectedOgImage || undefined,
        });

        if (response.status === ApiStatus.SUCCESS && response.data) {
          toast.success("Category created successfully");
          onOpenChange?.(false);
          onSuccess?.(response.data as CategoryTableData);
          return;
        }
        toast.error(response.message || "Failed to create category");
        return; // Don't close the sheet if there was an error
      }
    } catch (error) {
      console.error("Failed to save category:", error);
      toast.error("Failed to save category");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Generate slug from name
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, "") // Remove special characters
      .replace(/\s+/g, "-") // Replace spaces with hyphens
      .replace(/-+/g, "-") // Replace multiple hyphens with single
      .replace(/^-|-$/g, ""); // Remove leading/trailing hyphens
  };

  // Auto-generate slug when name changes
  React.useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === "name" && value.name) {
        form.setValue("slug", generateSlug(value.name));
      }
      // Trigger validation when availableOnline changes to show/hide required field errors
      if (name === "availableOnline") {
        // Force validation of SEO fields when availableOnline changes
        form.trigger(["slug", "seoTitle", "seoDescription"]);
      }
    });
    return () => subscription.unsubscribe();
  }, [form]);

  // Handle availability checks
  const handleCheckNameAvailability = (value: string) => {
    setCheckName(value);
  };

  const handleCheckSlugAvailability = (value: string) => {
    setCheckSlug(value);
  };

  const handleCheckShortCodeAvailability = (value: string) => {
    setCheckShortCode(value);
  };

  const sections = [
    {
      id: "basic-info",
      title: "Basic Information",
      icon: <Info className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasBasicInfoErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Info className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Basic Information</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <AvailabilityInput
                name="name"
                label="Name"
                placeholder="Enter category name (e.g., Electronics, Clothing)"
                value={form.watch("name") || ""}
                onChange={(value) =>
                  form.setValue("name", value, { shouldValidate: true })
                }
                onCheckAvailability={handleCheckNameAvailability}
                isCheckingAvailability={isCheckingName}
                isAvailable={isNameAvailable}
                shouldCheck={shouldCheckNameAvailability}
                error={errors.name?.message}
                disabled={isSubmitting}
                fieldName="Name"
              />

              <div>
                <AvailabilityInput
                  name="shortCode"
                  label="Short Code"
                  placeholder="Enter short code (e.g., ELEC, CLOTH)"
                  value={form.watch("shortCode") || ""}
                  onChange={(value) =>
                    form.setValue("shortCode", value, { shouldValidate: true })
                  }
                  onCheckAvailability={handleCheckShortCodeAvailability}
                  isCheckingAvailability={isCheckingShortCode}
                  isAvailable={isShortCodeAvailable}
                  shouldCheck={shouldCheckShortCodeAvailability}
                  error={errors.shortCode?.message}
                  disabled={isSubmitting}
                  fieldName="Short code"
                />
                <p className="text-sm text-muted-foreground mt-1">
                  Optional short identifier for the category
                </p>
              </div>

              <div>
                <Label>Color</Label>
                <Input
                  {...form.register("color")}
                  name="color"
                  type="color"
                  placeholder="#3B82F6"
                  className={cn(
                    "h-10 w-full cursor-pointer",
                    errors.color && "border-destructive"
                  )}
                  disabled={isSubmitting}
                />
                {errors.color && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.color.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Choose a color to represent this category
                </p>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Available Online</Label>
                  <p className="text-sm text-muted-foreground">
                    Make this category available in the online store
                    {form.watch("availableOnline") && (
                      <span className="block text-orange-600 font-medium mt-1">
                        ⚠️ When enabled, Slug and SEO fields become required
                      </span>
                    )}
                  </p>
                </div>
                <Switch
                  checked={form.watch("availableOnline")}
                  onCheckedChange={(checked) =>
                    form.setValue("availableOnline", checked, {
                      shouldValidate: true,
                    })
                  }
                  disabled={isSubmitting}
                />
              </div>
              {errors.availableOnline && (
                <p className="text-sm text-destructive mt-1">
                  {errors.availableOnline.message}
                </p>
              )}

              <div>
                <Label>Status</Label>
                <Select
                  value={form.watch("status")}
                  onValueChange={(value) =>
                    form.setValue(
                      "status",
                      value as CategoryStatus.ACTIVE | CategoryStatus.INACTIVE,
                      {
                        shouldValidate: true,
                      }
                    )
                  }
                  disabled={isSubmitting}
                >
                  <SelectTrigger
                    className={cn(errors.status && "border-destructive")}
                  >
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={CategoryStatus.ACTIVE}>
                      Active
                    </SelectItem>
                    <SelectItem value={CategoryStatus.INACTIVE}>
                      Inactive
                    </SelectItem>
                  </SelectContent>
                </Select>
                {errors.status && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.status.message}
                  </p>
                )}
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "hierarchy",
      title: "Category Hierarchy",
      icon: <FolderTree className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasHierarchyErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <FolderTree className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Category Hierarchy</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Parent Category</Label>
                <Select
                  value={form.watch("parentId") || "none"}
                  onValueChange={(value) =>
                    form.setValue("parentId", value === "none" ? null : value, {
                      shouldValidate: true,
                    })
                  }
                  disabled={isSubmitting}
                >
                  <SelectTrigger
                    className={cn(errors.parentId && "border-destructive")}
                  >
                    <SelectValue placeholder="Select parent category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">
                      No Parent (Root Category)
                    </SelectItem>
                    {flatCategories
                      .filter((cat) => cat.id && cat.id.trim() !== "") // Filter out categories with empty IDs
                      .map((cat) => (
                        <SelectItem
                          key={cat.id}
                          value={cat.id}
                          disabled={category?.id === cat.id} // Don't allow self as parent
                        >
                          {cat.name}
                          {cat.subcategories &&
                            cat.subcategories.length > 0 && (
                              <span className="text-muted-foreground ml-2">
                                ({cat.subcategories.length} subcategories)
                              </span>
                            )}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
                {errors.parentId && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.parentId.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Choose a parent category to create a hierarchy
                </p>
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "details",
      title: "Additional Details",
      icon: <Info className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasDetailsErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Info className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Additional Details</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Description</Label>
                <Textarea
                  {...form.register("description")}
                  name="description"
                  placeholder="Enter category description"
                  className={cn(errors.description && "border-destructive")}
                  disabled={isSubmitting}
                  rows={3}
                />
                {errors.description && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.description.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Optional description for the category
                </p>
              </div>

              <div>
                <AvailabilityInput
                  name="slug"
                  label={`Slug${form.watch("availableOnline") ? " *" : ""}`}
                  placeholder="URL-friendly version (auto-generated from name)"
                  value={form.watch("slug") || ""}
                  onChange={(value) =>
                    form.setValue("slug", value, { shouldValidate: true })
                  }
                  onCheckAvailability={handleCheckSlugAvailability}
                  isCheckingAvailability={isCheckingSlug}
                  isAvailable={isSlugAvailable}
                  shouldCheck={shouldCheckSlugAvailability}
                  error={errors.slug?.message}
                  disabled={isSubmitting}
                  fieldName="Slug"
                />
                <p className="text-sm text-muted-foreground mt-1">
                  Used for SEO-friendly URLs (automatically generated if left
                  empty)
                  {form.watch("availableOnline") && (
                    <span className="block text-orange-600 font-medium">
                      Required when category is available online
                    </span>
                  )}
                </p>
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "category-image",
      title: "Category Image",
      icon: <ImageIcon className="h-5 w-5 text-muted-foreground" />,
      hasErrors: false,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <ImageIcon className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Category Image</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <FileUploader
              files={uploadedFiles}
              onFilesChange={setUploadedFiles}
              fileType="image"
              maxFiles={1}
              maxSize={5 * 1024 * 1024} // 5MB
              multiple={false}
              label="Image"
              description="Upload an image for this category. Maximum file size is 5MB."
              placeholder="Click to upload or drag and drop"
              disabled={isSubmitting}
              showPreview={true}
              previewSize="md"
              onError={(error) => {
                toast.error(error);
              }}
            />
          </AccordionContent>
        </>
      ),
    },
  ];

  return (
    <BaseSheet<CategoryTableData, FormData>
      isDemo={isDemo}
      onSuccess={onSuccess}
      data={category}
      open={open}
      onOpenChange={onOpenChange}
      isUpdate={isUpdate}
      title="Category"
      sections={sections}
      onSubmit={handleSubmit}
      formRef={formRef}
      isSubmitting={isSubmitting}
      isPending={isLoadingCategory}
      // Location props
      isLocationEnabled={true}
      isAllocatedToAllLocations={
        form.watch("isAllocatedToAllLocations") || false
      }
      onAllLocationsChange={(isAllocated) => {
        form.setValue("isAllocatedToAllLocations", isAllocated, {
          shouldValidate: true,
        });
        // Clear location IDs when allocating to all locations
        if (isAllocated) {
          form.setValue("locationIds", [], { shouldValidate: true });
        }
      }}
      locationIds={form.watch("locationIds") || []}
      onLocationChange={(locationIds) => {
        form.setValue("locationIds", locationIds, { shouldValidate: true });
      }}
      locationErrors={
        errors.locationIds?.message || errors.isAllocatedToAllLocations?.message
      }
      // SEO props
      isSEOEnabled={true}
      seoTitle={form.watch("seoTitle") || ""}
      onSeoTitleChange={(value) => {
        form.setValue("seoTitle", value, { shouldValidate: true });
      }}
      seoTitleError={errors.seoTitle?.message}
      seoDescription={form.watch("seoDescription") || ""}
      onSeoDescriptionChange={(value) => {
        form.setValue("seoDescription", value, { shouldValidate: true });
      }}
      seoDescriptionError={errors.seoDescription?.message}
      seoKeywords={keywords}
      onSeoKeywordsChange={(value) => {
        setKeywords(value);
        form.setValue("seoKeywords", parseKeywords(value), {
          shouldValidate: true,
        });
      }}
      seoKeywordsError={errors.seoKeywords?.message}
      ogImageFiles={ogImageFiles}
      onOgImageFilesChange={setOgImageFiles}
    />
  );
}
