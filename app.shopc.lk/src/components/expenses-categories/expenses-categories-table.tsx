"use client";

import * as React from "react";
import type {
  DataTableAdvancedFilterField,
  DataTableFilterField,
  DataTableRowAction,
} from "@/types";
import { BaseTable } from "@/components/shared/base-table";
import { getExpenseCategoriesTableData } from "@/lib/expenses-categories/queries";
import { DeleteExpenseCategoriesDialog } from "./delete-expenses-categories-dialog";
import { ExpenseCategoriesTableToolbarActions } from "./expenses-categories-table-toolbar-actions";
import { ExpenseCategoriesTableFloatingBar } from "./expenses-categories-table-floating-bar";
import { ExpenseCategorySheet } from "./expense-category-sheet";
import { ExpenseCategoryDetails } from "./expense-category-details";
import { ExpenseCategoryDetailsContent } from "./expense-category-details-content";
import { useSearchParams } from "next/navigation";
import { getColumns } from "./expenses-categories-table-columns";
import {
  ExpenseCategoryTableData,
  AccountStatus,
  AccountCategory,
} from "@/types/expense-category";
import { useExpenseCategoriesData } from "@/lib/expenses-categories/hooks";
import { useQueryClient } from "@tanstack/react-query";
import { expenseCategoryKeys } from "@/lib/expenses-categories/hooks";

interface ExpenseCategoriesTableProps {
  isDemo?: boolean;
}

export function ExpenseCategoriesTable({
  isDemo = false,
}: ExpenseCategoriesTableProps) {
  const [rowAction, setRowAction] =
    React.useState<DataTableRowAction<ExpenseCategoryTableData> | null>(null);
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();

  const filterFields: DataTableFilterField<ExpenseCategoryTableData>[] =
    React.useMemo(
      () => [
        {
          id: "accountName",
          label: "Account Name",
          placeholder: "Filter by account name...",
        },
        {
          id: "status",
          label: "Status",
          placeholder: "Filter by status...",
          type: "select",
          options: [
            { label: "Active", value: AccountStatus.ACTIVE },
            { label: "Inactive", value: AccountStatus.INACTIVE },
          ],
        },
        {
          id: "accountCategory",
          label: "Category",
          placeholder: "Filter by category...",
          type: "select",
          options: [
            { label: "Assets", value: AccountCategory.ASSETS },
            { label: "Liabilities", value: AccountCategory.LIABILITIES },
            { label: "Equity", value: AccountCategory.EQUITY },
            { label: "Income", value: AccountCategory.INCOME },
            { label: "Expenses", value: AccountCategory.EXPENSES },
            { label: "Revenue", value: AccountCategory.REVENUE },
          ],
        },
        {
          id: "useForBillableExpenses",
          label: "Billable",
          placeholder: "Filter by billable...",
          type: "select",
          options: [
            { label: "Yes", value: "true" },
            { label: "No", value: "false" },
          ],
        },
      ],
      []
    );

  const advancedFilterFields: DataTableAdvancedFilterField<ExpenseCategoryTableData>[] =
    React.useMemo(
      () => [
        {
          id: "accountName",
          label: "Account Name",
          type: "text",
        },
        {
          id: "accountNumber",
          label: "Account Number",
          type: "text",
        },
        {
          id: "status",
          label: "Status",
          type: "select",
          options: [
            { label: "Active", value: AccountStatus.ACTIVE },
            { label: "Inactive", value: AccountStatus.INACTIVE },
          ],
        },
        {
          id: "accountCategory",
          label: "Category",
          type: "select",
          options: [
            { label: "Assets", value: AccountCategory.ASSETS },
            { label: "Liabilities", value: AccountCategory.LIABILITIES },
            { label: "Equity", value: AccountCategory.EQUITY },
            { label: "Income", value: AccountCategory.INCOME },
            { label: "Expenses", value: AccountCategory.EXPENSES },
            { label: "Revenue", value: AccountCategory.REVENUE },
          ],
        },
        {
          id: "useForBillableExpenses",
          label: "Billable",
          type: "select",
          options: [
            { label: "Yes", value: "true" },
            { label: "No", value: "false" },
          ],
        },
      ],
      []
    );

  const sortFields: DataTableAdvancedFilterField<ExpenseCategoryTableData>[] =
    React.useMemo(
      () => [
        {
          id: "accountName",
          label: "Account Name",
          type: "text",
        },
        {
          id: "createdAt",
          label: "Created Date",
          type: "text",
        },
        {
          id: "openingBalance",
          label: "Opening Balance",
          type: "number",
        },
        {
          id: "updatedAt",
          label: "Updated Date",
          type: "text",
        },
      ],
      []
    );

  const handleRowAction = React.useCallback(
    (action: DataTableRowAction<ExpenseCategoryTableData>) => {
      setRowAction(action);
    },
    []
  );

  const searchParamsValues = React.useMemo(() => {
    return {
      page: searchParams.get("page")
        ? parseInt(searchParams.get("page") as string)
        : 1,
      limit: searchParams.get("perPage")
        ? parseInt(searchParams.get("perPage") as string)
        : 10,
      search: searchParams.get("accountName") || "",
      status: searchParams.get("status") as AccountStatus | undefined,
      accountCategory: searchParams.get("accountCategory") as
        | AccountCategory
        | undefined,
      from: searchParams.get("from") || "",
      to: searchParams.get("to") || "",
      filters: (() => {
        const filtersParam = searchParams.get("filters");
        return filtersParam ? JSON.parse(filtersParam) : [];
      })(),
      joinOperator: (searchParams.get("joinOperator") as "and" | "or") || "and",
      sort: (() => {
        const sortParam = searchParams.get("sort");
        return sortParam ? JSON.parse(sortParam) : [];
      })(),
    };
  }, [searchParams]);

  // Use TanStack Query hook
  const {
    data: expenseCategoriesData,
    isLoading,
    isFetching,
  } = useExpenseCategoriesData(searchParamsValues, isDemo);

  // Only show full loading indicator for initial loading
  // Show header loading for refetch operations
  const isHeaderLoading = isFetching && !isLoading;

  // Determine if actions should be disabled (during any loading/fetching state)
  const isActionsDisabled = isLoading || isFetching;

  // Check if there are active filters or search parameters
  const hasActiveFilters = React.useMemo(() => {
    return !!(
      searchParamsValues.search ||
      searchParamsValues.status ||
      searchParamsValues.accountCategory ||
      searchParamsValues.from ||
      searchParamsValues.to ||
      (searchParamsValues.filters && searchParamsValues.filters.length > 0)
    );
  }, [searchParamsValues]);

  const handleRefresh = React.useCallback(
    async (expenseCategoryIds?: string[]) => {
      const invalidatePromises = [
        queryClient.invalidateQueries({ queryKey: expenseCategoryKeys.list() }),
        queryClient.invalidateQueries({
          queryKey: expenseCategoryKeys.simple(),
        }),
      ];

      // If specific expense category IDs are provided, invalidate their detail cache as well
      if (expenseCategoryIds && expenseCategoryIds.length > 0) {
        expenseCategoryIds.forEach((expenseCategoryId) => {
          invalidatePromises.push(
            queryClient.invalidateQueries({
              queryKey: expenseCategoryKeys.detail(expenseCategoryId),
            })
          );
        });
      }

      await Promise.all(invalidatePromises);
    },
    [queryClient]
  );

  // Custom dialog content renderer for row clicks
  const renderExpenseCategoryDetails = React.useCallback(
    (expenseCategory: ExpenseCategoryTableData) => {
      return (
        <ExpenseCategoryDetailsContent
          expenseCategory={expenseCategory}
          isDemo={isDemo}
        />
      );
    },
    [isDemo]
  );

  return (
    <>
      <BaseTable<
        ExpenseCategoryTableData,
        Awaited<ReturnType<typeof getExpenseCategoriesTableData>>
      >
        data={expenseCategoriesData || undefined}
        isLoading={isLoading}
        isHeaderLoading={isHeaderLoading}
        isDemo={isDemo}
        hasActiveFilters={hasActiveFilters}
        enableSortableRows={false}
        currentPage={searchParamsValues.page}
        itemsPerPage={searchParamsValues.limit}
        isDragDropEnabled={() => false}
        columns={getColumns({
          setRowAction,
          isDemo,
          isActionsDisabled,
          onStatusUpdate: (expenseCategoryId?: string) => {
            queryClient.invalidateQueries({
              queryKey: expenseCategoryKeys.list(),
            });
            queryClient.invalidateQueries({
              queryKey: expenseCategoryKeys.simple(),
            });
            if (expenseCategoryId) {
              queryClient.invalidateQueries({
                queryKey: expenseCategoryKeys.detail(expenseCategoryId),
              });
            }
          },
        })}
        filterFields={filterFields}
        advancedFilterFields={advancedFilterFields}
        sortFields={sortFields}
        getInitialData={(response) => {
          return response?.data?.expenseCategories ?? [];
        }}
        getPageCount={(response) => {
          return response?.data?.totalPages ?? 1;
        }}
        getRowId={(row) => row.expenseCategoryId}
        enableAdvancedTable={false}
        enableFloatingBar={true}
        FloatingBar={(props) => (
          <ExpenseCategoriesTableFloatingBar
            {...props}
            onRefresh={handleRefresh}
            isDemo={isDemo}
          />
        )}
        ToolbarActions={(props) => (
          <ExpenseCategoriesTableToolbarActions {...props} isDemo={isDemo} />
        )}
        onRowAction={handleRowAction}
        defaultSortingId={undefined}
        defaultSortingDesc={false}
        onRefresh={handleRefresh}
        renderDialogContent={renderExpenseCategoryDetails}
      />
      <ExpenseCategoryDetails
        open={rowAction?.type === "view"}
        onOpenChange={() => setRowAction(null)}
        expenseCategory={rowAction?.row.original ?? null}
        isDemo={isDemo}
      />
      {/* <ExpenseCategorySheet
        open={rowAction?.type === "update"}
        onOpenChange={() => setRowAction(null)}
        expenseCategory={rowAction?.row.original ?? null}
        isDemo={isDemo}
        onSuccess={() => {
          const expenseCategoryId = rowAction?.row.original?.expenseCategoryId;
          queryClient.invalidateQueries({
            queryKey: expenseCategoryKeys.list(),
          });
          queryClient.invalidateQueries({
            queryKey: expenseCategoryKeys.simple(),
          });
          if (expenseCategoryId) {
            queryClient.invalidateQueries({
              queryKey: expenseCategoryKeys.detail(expenseCategoryId),
            });
          }
        }}
        isUpdate={true}
      /> */}
      <DeleteExpenseCategoriesDialog
        open={rowAction?.type === "delete"}
        onOpenChange={() => setRowAction(null)}
        expenseCategories={
          rowAction?.row.original ? [rowAction.row.original] : []
        }
        showTrigger={false}
        onSuccess={() => {
          queryClient.invalidateQueries({
            queryKey: expenseCategoryKeys.list(),
          });
          queryClient.invalidateQueries({
            queryKey: expenseCategoryKeys.simple(),
          });
        }}
        isDemo={isDemo}
      />
    </>
  );
}
