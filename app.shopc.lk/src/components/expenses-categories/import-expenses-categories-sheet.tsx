/**
 * ImportExpenseCategoriesSheet Component
 *
 * This component allows importing expense categories with the following features:
 * - Manual data entry or file upload (CSV/Excel)
 * - Parent account selection for hierarchy
 * - Field validation and error handling
 * - Integration with bulk import API
 */

"use client";

import { useMemo, useCallback } from "react";
import { ImportDataSheet, FieldConfig } from "../data-import/import-data-sheet";
import {
  AccountStatus,
  AccountCategory,
  ChartAccountType,
  AccountDetailType,
} from "@/types/expense-category";
import { ApiStatus } from "@/types/common";
import {
  useExpenseCategoriesSlim,
  useBulkCreateExpenseCategories,
} from "@/lib/expenses-categories/hooks";

// Define the expense category field types that can be imported
export type ExpenseCategoryImportFields =
  | "accountName"
  | "accountNumber"
  | "accountCategory"
  | "accountType"
  | "accountDetailType"
  | "parentAccountId"
  | "openingBalance"
  | "openingBalanceDate"
  | "description"
  | "useForBillableExpenses"
  | "status";

// All possible fields for expense category import
const ALL_EXPENSE_CATEGORY_FIELDS: ExpenseCategoryImportFields[] = [
  "accountName",
  "accountNumber",
  "accountCategory",
  "accountType",
  "accountDetailType",
  "parentAccountId",
  "openingBalance",
  "openingBalanceDate",
  "description",
  "useForBillableExpenses",
  "status",
];

export type ImportExpenseCategoriesSheetProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
  isDemo?: boolean;
};

export function ImportExpenseCategoriesSheet({
  open,
  onOpenChange,
  onSuccess,
  isDemo = false,
}: ImportExpenseCategoriesSheetProps) {
  // Hook for bulk importing expense categories
  const bulkImportMutation = useBulkCreateExpenseCategories(isDemo);

  // Fetch expense categories for parent selection (slim data)
  const { data: expenseCategoriesResponse } = useExpenseCategoriesSlim(isDemo);
  const expenseCategories = expenseCategoriesResponse?.data || [];

  // Field configurations for the import sheet
  const fieldConfigs: FieldConfig[] = useMemo(
    () => [
      {
        name: "accountName",
        type: "text",
        placeholder: "Enter account name",
      },
      {
        name: "accountNumber",
        type: "text",
        placeholder: "Enter account number (optional)",
      },
      {
        name: "accountCategory",
        type: "select",
        options: Object.values(AccountCategory).map((category) => ({
          value: category,
          label: category.charAt(0).toUpperCase() + category.slice(1),
        })),
      },
      {
        name: "accountType",
        type: "select",
        options: Object.values(ChartAccountType).map((type) => ({
          value: type,
          label: type
            .replace(/_/g, " ")
            .replace(/\b\w/g, (l) => l.toUpperCase()),
        })),
      },
      {
        name: "accountDetailType",
        type: "select",
        options: Object.values(AccountDetailType).map((detailType) => ({
          value: detailType,
          label: detailType
            .replace(/_/g, " ")
            .replace(/\b\w/g, (l) => l.toUpperCase()),
        })),
      },
      {
        name: "parentAccountId",
        type: "select",
        options: [
          { value: "", label: "No parent (root level)" },
          ...expenseCategories.map((cat) => ({
            value: cat.expenseCategoryId,
            label: `${cat.accountName}${
              cat.accountNumber ? ` (${cat.accountNumber})` : ""
            }`,
          })),
        ],
      },
      {
        name: "openingBalance",
        type: "text",
        placeholder: "0.00",
      },
      {
        name: "openingBalanceDate",
        type: "text",
        placeholder: "YYYY-MM-DD",
      },
      {
        name: "description",
        type: "text",
        placeholder: "Enter description (optional)",
      },
      {
        name: "useForBillableExpenses",
        type: "select",
        options: [
          { value: "true", label: "Yes" },
          { value: "false", label: "No" },
        ],
      },
      {
        name: "status",
        type: "select",
        options: [
          { value: AccountStatus.ACTIVE, label: "Active" },
          { value: AccountStatus.INACTIVE, label: "Inactive" },
        ],
      },
    ],
    [expenseCategories]
  );

  // Handle the import submission
  const handleImport = useCallback(
    async (data: Record<ExpenseCategoryImportFields, string>[]) => {
      try {
        // Transform the data to match the API schema
        const transformedData = data.map((row) => ({
          accountName: row.accountName,
          accountNumber: row.accountNumber || undefined,
          accountCategory: row.accountCategory as AccountCategory,
          accountType: row.accountType as ChartAccountType,
          accountDetailType: row.accountDetailType as AccountDetailType,
          parentAccountId: row.parentAccountId || undefined,
          openingBalance: row.openingBalance || "0.00",
          openingBalanceDate: row.openingBalanceDate || undefined,
          description: row.description || undefined,
          useForBillableExpenses: row.useForBillableExpenses === "true",
          status: (row.status as AccountStatus) || AccountStatus.ACTIVE,
        }));

        const result = await bulkImportMutation.mutateAsync(transformedData);

        if (result.status === ApiStatus.SUCCESS) {
          onSuccess();
          onOpenChange(false);
          return { success: true };
        } else {
          return {
            success: false,
            error: result.message || "Failed to import expense categories",
          };
        }
      } catch (error: any) {
        console.error("Import error:", error);
        return {
          success: false,
          error: error.message || "Failed to import expense categories",
        };
      }
    },
    [bulkImportMutation, onSuccess, onOpenChange]
  );

  return (
    <ImportDataSheet
      open={open}
      onOpenChange={onOpenChange}
      title="Import Expense Categories"
      description="Import expense categories from CSV/Excel file or enter data manually"
      targetFields={ALL_EXPENSE_CATEGORY_FIELDS}
      fieldConfigs={fieldConfigs}
      onSubmit={async (data) => {
        const result = await handleImport(data);
        if (!result.success) {
          throw new Error(result.error);
        }
      }}
      requireAllFields={false}
    />
  );
}
