"use client";

import * as React from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { Info, DollarSign, FolderTree, Check, X, Loader2 } from "lucide-react";
import { type z } from "zod";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { DatePicker } from "@/components/ui/date-picker";
import { AccordionTrigger, AccordionContent } from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import { expenseCategoryFormSchema } from "@/lib/expenses-categories/validations";
import { ApiStatus } from "@/types/common";
import { BaseSheet } from "@/components/shared/base-sheet";
import {
  ExpenseCategoryTableData,
  AccountStatus,
  UpdateExpenseCategoryDto,
} from "@/types/expense-category";
import {
  AccountCategory,
  ChartAccountType,
  AccountDetailType,
  ACCOUNT_HIERARCHY,
  AccountLabelUtils,
} from "@/types/account.enum";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import {
  useExpenseCategoriesSlim,
  useExpenseCategoryData,
  useExpenseCategoryNameAvailability,
  useCreateExpenseCategory,
  useUpdateExpenseCategory,
} from "@/lib/expenses-categories/hooks";

interface ExpenseCategorySheetProps {
  expenseCategory: ExpenseCategoryTableData | null;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: (expenseCategory?: ExpenseCategoryTableData) => void;
  isUpdate?: boolean;
  isDemo?: boolean;
}

type FormData = z.infer<typeof expenseCategoryFormSchema>;

export function ExpenseCategorySheet({
  expenseCategory,
  open,
  onOpenChange,
  onSuccess,
  isUpdate = false,
  isDemo = false,
}: ExpenseCategorySheetProps) {
  const formRef = React.useRef<HTMLFormElement>(
    null
  ) as React.RefObject<HTMLFormElement>;
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  // Debounced values for availability checking
  const [debouncedAccountName, setDebouncedAccountName] =
    React.useState<string>("");

  // Fetch complete expense category data if updating
  const {
    data: fullExpenseCategoryResponse,
    isLoading: isLoadingExpenseCategory,
  } = useExpenseCategoryData(expenseCategory?.expenseCategoryId || "", isDemo);
  const fullExpenseCategory = fullExpenseCategoryResponse?.data;

  // Fetch expense categories for parent selection
  const { data: expenseCategoriesResponse } = useExpenseCategoriesSlim(isDemo);
  const expenseCategories = React.useMemo(
    () => expenseCategoriesResponse?.data || [],
    [expenseCategoriesResponse]
  );

  // Filter out current expense category from parent options to prevent circular reference
  const parentOptions = React.useMemo(() => {
    if (!expenseCategory?.expenseCategoryId) return expenseCategories;
    return expenseCategories.filter(
      (cat) => cat.expenseCategoryId !== expenseCategory.expenseCategoryId
    );
  }, [expenseCategories, expenseCategory?.expenseCategoryId]);

  // Mutations
  const createExpenseCategoryMutation = useCreateExpenseCategory(isDemo);
  const updateExpenseCategoryMutation = useUpdateExpenseCategory(isDemo);

  // Form setup
  const form = useForm<FormData>({
    resolver: zodResolver(expenseCategoryFormSchema),
    defaultValues: {
      accountName: "",
      accountNumber: "",
      accountCategory: AccountCategory.EXPENSES,
      accountType: ChartAccountType.EXPENSES,
      accountDetailType: AccountDetailType.OTHER_BUSINESS_EXPENSES,
      parentAccountId: "",
      openingBalance: "0.00",
      openingBalanceDate: "",
      description: "",
      defaultTaxId: "",
      useForBillableExpenses: false,
      incomeAccountId: "",
      status: AccountStatus.ACTIVE,
    },
  });

  const { errors } = form.formState;

  // Availability checking
  const currentAccountName = form.watch("accountName");
  const shouldCheckAccountNameAvailability = React.useMemo(() => {
    return (
      currentAccountName &&
      currentAccountName.length > 0 &&
      currentAccountName !== fullExpenseCategory?.accountName &&
      debouncedAccountName === currentAccountName
    );
  }, [
    currentAccountName,
    fullExpenseCategory?.accountName,
    debouncedAccountName,
  ]);

  const { data: accountNameAvailability, isLoading: isCheckingAccountName } =
    useExpenseCategoryNameAvailability(
      debouncedAccountName,
      isDemo,
      expenseCategory?.expenseCategoryId
    );

  // Debounce account name for availability checking
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedAccountName(currentAccountName);
    }, 500);

    return () => clearTimeout(timer);
  }, [currentAccountName]);

  // Auto-set detail type when account type changes
  React.useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === "accountType" && value.accountType) {
        // When account type changes, update detail type if it's not valid for the new account type
        const validDetailTypes = AccountLabelUtils.getDetailTypesForAccountType(value.accountType);
        const currentDetailType = form.getValues("accountDetailType");
        
        if (!validDetailTypes.some(type => type.value === currentDetailType)) {
          const defaultDetailType = validDetailTypes[0]?.value;
          if (defaultDetailType) {
            form.setValue("accountDetailType", defaultDetailType, { shouldValidate: true });
          }
        }
      }
    });
    return () => subscription.unsubscribe();
  }, [form]);

  // Load data when editing
  React.useEffect(() => {
    if (fullExpenseCategory && isUpdate) {
      form.reset({
        accountName: fullExpenseCategory.accountName || "",
        accountNumber: fullExpenseCategory.accountNumber || "",
        accountCategory: fullExpenseCategory.accountCategory,
        accountType: fullExpenseCategory.accountType,
        accountDetailType: fullExpenseCategory.accountDetailType,
        parentAccountId: fullExpenseCategory.parentAccountId || "",
        openingBalance: fullExpenseCategory.openingBalance || "0.00",
        openingBalanceDate: fullExpenseCategory.openingBalanceDate || "",
        description: fullExpenseCategory.description || "",
        defaultTaxId: fullExpenseCategory.defaultTaxId || "",
        useForBillableExpenses:
          fullExpenseCategory.useForBillableExpenses || false,
        incomeAccountId: fullExpenseCategory.incomeAccountId || "",
        status: fullExpenseCategory.status || AccountStatus.ACTIVE,
      });
    }
  }, [fullExpenseCategory, isUpdate]);

  // Reset form when opening for create
  React.useEffect(() => {
    if (open && !isUpdate) {
      form.reset({
        accountName: "",
        accountNumber: "",
        accountCategory: AccountCategory.EXPENSES,
        accountType: ChartAccountType.EXPENSES,
        accountDetailType: AccountDetailType.OTHER_BUSINESS_EXPENSES,
        parentAccountId: "",
          openingBalance: "0.00",
        openingBalanceDate: "",
        description: "",
        defaultTaxId: "",
        useForBillableExpenses: false,
        incomeAccountId: "",
        status: AccountStatus.ACTIVE,
      });
    }
  }, [open, isUpdate]);

  const handleSubmit = async (data: FormData) => {
    setIsSubmitting(true);

    try {
      if (isUpdate && expenseCategory?.expenseCategoryId) {
        // Update existing expense category
        const updateData: UpdateExpenseCategoryDto = {
          accountName: data.accountName,
          accountNumber: data.accountNumber || undefined,
          accountCategory: data.accountCategory,
          accountType: data.accountType,
          accountDetailType: data.accountDetailType,
          parentAccountId: data.parentAccountId || undefined,
          openingBalance: data.openingBalance,
          openingBalanceDate: data.openingBalanceDate || undefined,
          description: data.description || undefined,
          defaultTaxId: data.defaultTaxId || undefined,
          useForBillableExpenses: data.useForBillableExpenses,
          incomeAccountId: data.incomeAccountId || undefined,
          status: data.status,
        };

        const response = await updateExpenseCategoryMutation.mutateAsync({
          id: expenseCategory.expenseCategoryId,
          data: updateData,
        });

        if (response.status === ApiStatus.SUCCESS && response.data) {
          toast.success("Expense category updated successfully");
          onOpenChange?.(false);
          onSuccess?.(response.data as ExpenseCategoryTableData);
          return;
        }
        toast.error(response.message || "Failed to update expense category");
        return;
      } else {
        // Create new expense category
        const createData = {
          accountName: data.accountName,
          accountNumber: data.accountNumber || undefined,
          accountCategory: data.accountCategory,
          accountType: data.accountType,
          accountDetailType: data.accountDetailType,
          parentAccountId: data.parentAccountId || undefined,
          openingBalance: data.openingBalance,
          openingBalanceDate: data.openingBalanceDate || undefined,
          description: data.description || undefined,
          defaultTaxId: data.defaultTaxId || undefined,
          useForBillableExpenses: data.useForBillableExpenses,
          incomeAccountId: data.incomeAccountId || undefined,
          status: data.status,
        };

        const response = await createExpenseCategoryMutation.mutateAsync(
          createData
        );

        if (response.status === ApiStatus.SUCCESS && response.data) {
          toast.success("Expense category created successfully");
          onOpenChange?.(false);
          onSuccess?.(response.data as ExpenseCategoryTableData);
          return;
        }
        toast.error(response.message || "Failed to create expense category");
        return;
      }
    } catch (error) {
      console.error("Failed to save expense category:", error);
      toast.error("Failed to save expense category");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Validation helpers
  const hasBasicInfoErrors = !!(
    errors.accountName ||
    errors.accountNumber ||
    errors.accountType ||
    errors.accountDetailType
  );

  const hasFinancialInfoErrors = !!(
    errors.openingBalance ||
    errors.openingBalanceDate ||
    errors.defaultTaxId ||
    errors.incomeAccountId
  );

  const hasHierarchyErrors = !!errors.parentAccountId;

  const sections = [
    {
      id: "basic-info",
      title: "Basic Information",
      icon: <Info className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasBasicInfoErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Info className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Basic Information</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Account Name</Label>
                <Input
                  {...form.register("accountName")}
                  placeholder="Enter account name (e.g., Office Supplies, Travel Expenses)"
                  className={cn(
                    errors.accountName && "border-destructive",
                    shouldCheckAccountNameAvailability &&
                      accountNameAvailability?.data?.available === false &&
                      "border-destructive"
                  )}
                />
                <div className="flex items-center gap-2 mt-1">
                  {errors.accountName && (
                    <p className="text-sm text-destructive flex items-center gap-1">
                      <X className="h-3 w-3" />
                      {errors.accountName.message}
                    </p>
                  )}
                  {shouldCheckAccountNameAvailability && (
                    <>
                      {isCheckingAccountName ? (
                        <p className="text-sm text-muted-foreground flex items-center gap-1">
                          <Loader2 className="h-3 w-3 animate-spin" />
                          Checking availability...
                        </p>
                      ) : accountNameAvailability?.data?.available === false ? (
                        <p className="text-sm text-destructive flex items-center gap-1">
                          <X className="h-3 w-3" />
                          Account name is already taken
                        </p>
                      ) : accountNameAvailability?.data?.available === true ? (
                        <p className="text-sm text-green-600 flex items-center gap-1">
                          <Check className="h-3 w-3" />
                          Account name is available
                        </p>
                      ) : null}
                    </>
                  )}
                </div>
              </div>

              <div>
                <Label>Account Number (Optional)</Label>
                <Input
                  {...form.register("accountNumber")}
                  placeholder="Enter account number (e.g., 5001, EXP-001)"
                  className={cn(errors.accountNumber && "border-destructive")}
                />
                {errors.accountNumber && (
                  <p className="text-sm text-destructive mt-1 flex items-center gap-1">
                    <X className="h-3 w-3" />
                    {errors.accountNumber.message}
                  </p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label>Account Type</Label>
                  <Select
                    value={form.watch("accountType")}
                    onValueChange={(value) =>
                      form.setValue("accountType", value as ChartAccountType, {
                        shouldValidate: true,
                      })
                    }
                  >
                    <SelectTrigger
                      className={cn(errors.accountType && "border-destructive")}
                    >
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      {AccountLabelUtils.getAccountTypesForCategory(
                        AccountCategory.EXPENSES
                      ).map(({ value, label }) => (
                        <SelectItem key={value} value={value}>
                          {label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.accountType && (
                    <p className="text-sm text-destructive mt-1 flex items-center gap-1">
                      <X className="h-3 w-3" />
                      {errors.accountType.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Detail Type</Label>
                  <Select
                    value={form.watch("accountDetailType")}
                    onValueChange={(value) =>
                      form.setValue(
                        "accountDetailType",
                        value as AccountDetailType,
                        {
                          shouldValidate: true,
                        }
                      )
                    }
                  >
                    <SelectTrigger
                      className={cn(
                        errors.accountDetailType && "border-destructive"
                      )}
                    >
                      <SelectValue placeholder="Select detail type" />
                    </SelectTrigger>
                    <SelectContent>
                      {AccountLabelUtils.getDetailTypesForAccountType(
                        form.watch("accountType") || ChartAccountType.EXPENSES
                      ).map(({ value, label }) => (
                        <SelectItem key={value} value={value}>
                          {label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.accountDetailType && (
                    <p className="text-sm text-destructive mt-1 flex items-center gap-1">
                      <X className="h-3 w-3" />
                      {errors.accountDetailType.message}
                    </p>
                  )}
                </div>
              </div>

              <div>
                <Label>Description (Optional)</Label>
                <Textarea
                  {...form.register("description")}
                  placeholder="Enter a description for this expense category..."
                  className={cn(errors.description && "border-destructive")}
                  rows={3}
                />
                {errors.description && (
                  <p className="text-sm text-destructive mt-1 flex items-center gap-1">
                    <X className="h-3 w-3" />
                    {errors.description.message}
                  </p>
                )}
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "financial-info",
      title: "Financial Information",
      icon: <DollarSign className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasFinancialInfoErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <DollarSign className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Financial Information</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label>Opening Balance</Label>
                  <Input
                    {...form.register("openingBalance")}
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    className={cn(
                      errors.openingBalance && "border-destructive"
                    )}
                  />
                  {errors.openingBalance && (
                    <p className="text-sm text-destructive mt-1 flex items-center gap-1">
                      <X className="h-3 w-3" />
                      {errors.openingBalance.message}
                    </p>
                  )}
                </div>

                <div>
                  <DatePicker
                    name="openingBalanceDate"
                    label="Opening Balance Date (Optional)"
                    value={form.watch("openingBalanceDate") && form.watch("openingBalanceDate") !== "" ? new Date(form.watch("openingBalanceDate")!) : undefined}
                    onChange={(date) => {
                      const dateString = date ? date.toISOString().split('T')[0] : "";
                      form.setValue("openingBalanceDate", dateString, { shouldValidate: true });
                    }}
                    placeholder="Select opening balance date"
                    disabled={isSubmitting}
                    error={errors.openingBalanceDate?.message}
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="useForBillableExpenses"
                  checked={form.watch("useForBillableExpenses")}
                  onCheckedChange={(checked) =>
                    form.setValue("useForBillableExpenses", checked, {
                      shouldValidate: true,
                    })
                  }
                />
                <Label htmlFor="useForBillableExpenses">
                  Use for billable expenses
                </Label>
              </div>

            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "hierarchy",
      title: "Account Hierarchy",
      icon: <FolderTree className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasHierarchyErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <FolderTree className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Account Hierarchy</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Parent Account (Optional)</Label>
                <Select
                  value={form.watch("parentAccountId") || "none"}
                  onValueChange={(value) =>
                    form.setValue(
                      "parentAccountId",
                      value === "none" ? "" : value,
                      {
                        shouldValidate: true,
                      }
                    )
                  }
                >
                  <SelectTrigger
                    className={cn(
                      errors.parentAccountId && "border-destructive"
                    )}
                  >
                    <SelectValue placeholder="Select parent account (optional)" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">No parent (root level)</SelectItem>
                    {parentOptions.map((parentCategory) => (
                      <SelectItem
                        key={parentCategory.expenseCategoryId}
                        value={parentCategory.expenseCategoryId}
                      >
                        {parentCategory.accountName}
                        {parentCategory.accountNumber && (
                          <span className="text-muted-foreground ml-2">
                            ({parentCategory.accountNumber})
                          </span>
                        )}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.parentAccountId && (
                  <p className="text-sm text-destructive mt-1 flex items-center gap-1">
                    <X className="h-3 w-3" />
                    {errors.parentAccountId.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Select a parent account to create a hierarchical structure
                </p>
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
  ];

  const handleFormSubmit = async () => {
    const formData = form.getValues();
    await handleSubmit(formData);
  };

  return (
    <BaseSheet<ExpenseCategoryTableData, FormData>
      isDemo={isDemo}
      onSuccess={onSuccess}
      data={expenseCategory}
      open={open}
      onOpenChange={onOpenChange}
      isUpdate={isUpdate}
      title="Expense Category"
      sections={sections}
      onSubmit={handleFormSubmit}
      formRef={formRef}
      isSubmitting={isSubmitting}
      isPending={isLoadingExpenseCategory}
    />
  );
}
