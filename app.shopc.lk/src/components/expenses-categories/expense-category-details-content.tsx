"use client";

import * as React from "react";
import {
  Info,
  DollarSign,
  FolderTree,
  Calendar,
  Hash,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { useExpenseCategoryData, useExpenseCategoriesSlim } from "@/lib/expenses-categories/hooks";
import {
  ExpenseCategoryTableData,
  AccountStatus,
  AccountCategory,
} from "@/types/expense-category";
import { format } from "date-fns";
import { cn } from "@/lib/utils";

interface ExpenseCategoryDetailsContentProps {
  expenseCategory: ExpenseCategoryTableData;
  isDemo?: boolean;
}

export function ExpenseCategoryDetailsContent({
  expenseCategory,
  isDemo = false,
}: ExpenseCategoryDetailsContentProps) {
  // Fetch complete expense category data
  const { data: fullExpenseCategoryResponse, isLoading: isLoadingExpenseCategory } =
    useExpenseCategoryData(expenseCategory?.expenseCategoryId || "", isDemo);
  const fullExpenseCategory = fullExpenseCategoryResponse?.data;

  // Fetch expense categories for parent display
  const { data: expenseCategoriesResponse } = useExpenseCategoriesSlim(isDemo);
  const expenseCategories = expenseCategoriesResponse?.data || [];

  // Helper function to get status color
  const getStatusColor = (status: AccountStatus) => {
    switch (status) {
      case AccountStatus.ACTIVE:
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case AccountStatus.INACTIVE:
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  // Helper function to get category color
  const getCategoryColor = (category: AccountCategory) => {
    switch (category) {
      case AccountCategory.ASSETS:
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case AccountCategory.LIABILITIES:
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      case AccountCategory.EQUITY:
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
      case AccountCategory.INCOME:
        return "bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200";
      case AccountCategory.EXPENSES:
        return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";
      case AccountCategory.REVENUE:
        return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  // Find parent expense category
  const parentExpenseCategory = React.useMemo(() => {
    if (!fullExpenseCategory?.parentAccountId) return null;
    return expenseCategories.find(
      (cat) => cat.expenseCategoryId === fullExpenseCategory.parentAccountId
    );
  }, [fullExpenseCategory?.parentAccountId, expenseCategories]);

  if (isLoadingExpenseCategory) {
    return (
      <div className="space-y-6 p-6">
        <div className="space-y-3">
          <Skeleton className="h-8 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-1/3" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-16 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!fullExpenseCategory) {
    return (
      <div className="p-6 text-center">
        <p className="text-muted-foreground">Expense category not found</p>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">{fullExpenseCategory.accountName}</h2>
          <Badge
            className={cn(
              "font-medium",
              getStatusColor(fullExpenseCategory.status)
            )}
          >
            {fullExpenseCategory.status}
          </Badge>
        </div>
        {fullExpenseCategory.description && (
          <p className="text-muted-foreground">
            {fullExpenseCategory.description}
          </p>
        )}
        {fullExpenseCategory.accountNumber && (
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Hash className="h-4 w-4" />
            <span>Account Number: {fullExpenseCategory.accountNumber}</span>
          </div>
        )}
      </div>

      <Separator />

      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="h-5 w-5" />
            Basic Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Account Category</p>
              <Badge
                className={cn(
                  "mt-1",
                  getCategoryColor(fullExpenseCategory.accountCategory)
                )}
              >
                {fullExpenseCategory.accountCategory.charAt(0).toUpperCase() + 
                 fullExpenseCategory.accountCategory.slice(1)}
              </Badge>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Account Type</p>
              <p className="mt-1">
                {fullExpenseCategory.accountType.replace(/_/g, " ").replace(/\b\w/g, l => l.toUpperCase())}
              </p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Detail Type</p>
              <p className="mt-1">
                {fullExpenseCategory.accountDetailType.replace(/_/g, " ").replace(/\b\w/g, l => l.toUpperCase())}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Financial Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Financial Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Opening Balance</p>
              <p className="mt-1 text-lg font-semibold">
                ${parseFloat(fullExpenseCategory.openingBalance || "0").toFixed(2)}
              </p>
            </div>
            {fullExpenseCategory.openingBalanceDate && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">Opening Balance Date</p>
                <p className="mt-1">
                  {format(new Date(fullExpenseCategory.openingBalanceDate), "PPP")}
                </p>
              </div>
            )}
            <div>
              <p className="text-sm font-medium text-muted-foreground">Billable Expenses</p>
              <Badge variant={fullExpenseCategory.useForBillableExpenses ? "default" : "outline"}>
                {fullExpenseCategory.useForBillableExpenses ? "Yes" : "No"}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Hierarchy Information */}
      {parentExpenseCategory && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FolderTree className="h-5 w-5" />
              Hierarchy
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Parent Account</p>
              <div className="mt-1 flex items-center gap-2">
                <span>{parentExpenseCategory.accountName}</span>
                {parentExpenseCategory.accountNumber && (
                  <span className="text-muted-foreground">
                    ({parentExpenseCategory.accountNumber})
                  </span>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Metadata */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Metadata
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Created</p>
              <p className="mt-1">
                {format(new Date(fullExpenseCategory.createdAt), "PPP 'at' p")}
              </p>
              <p className="text-sm text-muted-foreground">
                by {fullExpenseCategory.createdByName}
              </p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Last Updated</p>
              <p className="mt-1">
                {format(new Date(fullExpenseCategory.updatedAt), "PPP 'at' p")}
              </p>
              {fullExpenseCategory.updatedByName && (
                <p className="text-sm text-muted-foreground">
                  by {fullExpenseCategory.updatedByName}
                </p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
