import { ExpensesTable } from "@/components/expenses/expenses-table";
import { type Metadata } from "next";
import { DataPage } from "@/components/shared/data-page";

export const metadata: Metadata = {
  title: "Expenses",
  description: "Manage expenses in demo mode",
};

export default async function IndexPage() {
  return (
    <DataPage
      TableComponent={ExpensesTable}
      columnCount={8}
      searchableColumnCount={2}
      filterableColumnCount={5}
      cellWidths={[
        "15rem",
        "15rem",
        "12rem",
        "10rem",
        "12rem",
        "10rem",
        "10rem",
        "8rem",
      ]}
      isDemo={false}
    />
  );
}
