import { type Metadata } from "next";
import { DataPage } from "@/components/shared/data-page";
import ExpenseCategoriesTableWrapper from "@/components/expenses-categories/expenses-categories-table-wrapper";

export const metadata: Metadata = {
  title: "Expense Categories",
  description: "Manage expense categories in demo mode",
};

export default async function IndexPage() {
  return (
    <DataPage
      TableComponent={ExpenseCategoriesTableWrapper}
      columnCount={4}
      searchableColumnCount={1}
      filterableColumnCount={1}
      cellWidths={["15rem", "12rem", "12rem", "12rem"]}
      isDemo={false}
    />
  );
}
