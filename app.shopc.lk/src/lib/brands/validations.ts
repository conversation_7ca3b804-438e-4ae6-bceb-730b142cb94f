import { z } from "zod";
import { BrandStatus } from "@/types/brand";

// Backend DTO: CreateBrandDto
export const createBrandSchema = z.object({
  name: z.string().min(1, "Name is required"),
  slug: z.string().optional(),
  availableOnline: z.boolean().optional().default(true),
  status: z
    .enum([BrandStatus.ACTIVE, BrandStatus.INACTIVE])
    .optional()
    .default(BrandStatus.ACTIVE),
  // SEO fields
  seoTitle: z.string().optional(),
  seoDescription: z.string().optional(),
  seoKeywords: z.array(z.string()).optional(),
  logoIndex: z.number().min(0, "Logo index must be 0 or greater").optional(),
  ogImageIndex: z.number().min(0, "OG image index must be 0 or greater").optional(),
});

// Backend DTO: UpdateBrandDto
export const updateBrandSchema = z.object({
  name: z.string().min(1, "Name is required").optional(),
  slug: z.string().optional(),
  availableOnline: z.boolean().optional(),
  status: z.enum([BrandStatus.ACTIVE, BrandStatus.INACTIVE]).optional(),
  // SEO fields
  seoTitle: z.string().optional(),
  seoDescription: z.string().optional(),
  seoKeywords: z.array(z.string()).optional(),
});

// Schema for getting brands with pagination and filters
export const getBrandsSchema = z.object({
  page: z.number().min(1).default(1),
  perPage: z.number().min(1).default(10),
  name: z.string().optional(),
  slug: z.string().optional(),
  status: z.string().optional(),
  availableOnline: z.string().optional(),
  from: z.string().optional(),
  to: z.string().optional(),
  filters: z
    .array(
      z.object({
        id: z.string(),
        value: z.union([z.string(), z.array(z.string())]),
        operator: z
          .enum([
            "iLike",
            "notILike",
            "eq",
            "ne",
            "isEmpty",
            "isNotEmpty",
            "lt",
            "lte",
            "gt",
            "gte",
            "isBetween",
            "isRelativeToToday",
          ])
          .default("iLike"),
        type: z
          .enum(["text", "number", "date", "boolean", "select", "multi-select"])
          .default("text"),
        rowId: z.string().optional(),
      })
    )
    .optional()
    .default([]),
  joinOperator: z.enum(["and", "or"]).default("and"),
  sort: z
    .array(
      z.object({
        id: z.string(),
        desc: z.boolean(),
      })
    )
    .optional()
    .default([{ id: "createdAt", desc: true }]),
});

// Schema for updating brand status
export const updateBrandStatusSchema = z.object({
  id: z.string().min(1, "ID is required"),
  status: z.enum([BrandStatus.ACTIVE, BrandStatus.INACTIVE]),
});

// Schema for importing brands (includes status)
export const importBrandSchema = z.object({
  name: z.string().min(1, "Name is required"),
  slug: z.string().optional(),
  availableOnline: z.boolean().optional().default(true),
  status: z
    .enum([BrandStatus.ACTIVE, BrandStatus.INACTIVE])
    .optional()
    .default(BrandStatus.ACTIVE),
  // SEO fields
  seoTitle: z.string().optional(),
  seoDescription: z.string().optional(),
  seoKeywords: z.array(z.string()).optional(),
  logoIndex: z.number().min(0, "Logo index must be 0 or greater").optional(),
  ogImageIndex: z.number().min(0, "OG image index must be 0 or greater").optional(),
});

// Bulk import schema for brands
export const bulkCreateBrandsSchema = z.array(createBrandSchema);

// Bulk import schema for brands with status
export const bulkImportBrandsSchema = z.array(importBrandSchema);

// Schema for bulk creation with images
export const bulkCreateBrandsWithImagesSchema = z.object({
  brands: z.array(createBrandSchema),
  images: z.array(z.instanceof(File)).optional(),
});

// Schema for bulk import with images
export const bulkImportBrandsWithImagesSchema = z.object({
  brands: z.array(importBrandSchema),
  images: z.array(z.instanceof(File)).optional(),
});

// Form schema for UI components (includes additional fields for form handling)
export const brandFormSchema = z.object({
  name: z.string().min(1, "Name is required"),
  slug: z.string().optional(),
  availableOnline: z.boolean().optional().default(true),
  status: z
    .enum([BrandStatus.ACTIVE, BrandStatus.INACTIVE])
    .optional()
    .default(BrandStatus.ACTIVE),
  // SEO fields
  seoTitle: z.string().optional(),
  seoDescription: z.string().optional(),
  seoKeywords: z.array(z.string()).optional(),
  logoIndex: z.number().min(0, "Logo index must be 0 or greater").optional(),
  ogImageIndex: z.number().min(0, "OG image index must be 0 or greater").optional(),
}).superRefine((data, ctx) => {
  // If availableOnline is true, validate required SEO fields
  if (data.availableOnline === true) {
    // Check slug
    if (!data.slug || data.slug.trim().length === 0) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Slug is required when the brand is available online",
        path: ["slug"],
      });
    }
    
    // Check seoTitle
    if (!data.seoTitle || data.seoTitle.trim().length === 0) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "SEO title is required when the brand is available online",
        path: ["seoTitle"],
      });
    }
    
    // Check seoDescription
    if (!data.seoDescription || data.seoDescription.trim().length === 0) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "SEO description is required when the brand is available online",
        path: ["seoDescription"],
      });
    }
  }
});

// Schema for brand name availability check
export const checkBrandNameSchema = z.object({
  name: z.string().min(1, "Name is required"),
});

// Schema for brand slug availability check
export const checkBrandSlugSchema = z.object({
  slug: z.string().min(1, "Slug is required"),
});

// Schema for updating brand positions
export const updateBrandPositionsSchema = z.object({
  updates: z.array(
    z.object({
      id: z.string().min(1, "Brand ID is required"),
      position: z.number().min(0, "Position must be 0 or greater"),
    })
  ),
});

export type GetBrandsSchema = z.infer<typeof getBrandsSchema>;
export type CreateBrandSchema = z.infer<typeof createBrandSchema>;
export type ImportBrandSchema = z.infer<typeof importBrandSchema>;
export type UpdateBrandSchema = z.infer<typeof updateBrandSchema>;
export type UpdateBrandStatusSchema = z.infer<typeof updateBrandStatusSchema>;
export type BulkCreateBrandsSchema = z.infer<typeof bulkCreateBrandsSchema>;
export type BulkImportBrandsSchema = z.infer<typeof bulkImportBrandsSchema>;
export type BulkCreateBrandsWithImagesSchema = z.infer<
  typeof bulkCreateBrandsWithImagesSchema
>;
export type BulkImportBrandsWithImagesSchema = z.infer<
  typeof bulkImportBrandsWithImagesSchema
>;
export type BrandFormSchema = z.infer<typeof brandFormSchema>;
export type CheckBrandNameSchema = z.infer<typeof checkBrandNameSchema>;
export type CheckBrandSlugSchema = z.infer<typeof checkBrandSlugSchema>;
export type UpdateBrandPositionsSchema = z.infer<typeof updateBrandPositionsSchema>;
