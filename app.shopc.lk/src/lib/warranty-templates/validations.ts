import { z } from "zod";
import { WarrantyStatus, DurationType, WarrantyType, CoverageType } from "@/types/warranty-templates";

// Base warranty template validation schemas
export const durationTypeSchema = z.nativeEnum(DurationType);
export const warrantyStatusSchema = z.nativeEnum(WarrantyStatus);
export const warrantyTypeSchema = z.nativeEnum(WarrantyType);
export const coverageTypeSchema = z.nativeEnum(CoverageType);

// Create warranty template schema with conditional validation
export const createWarrantyTemplateSchema = z
  .object({
    templateCode: z
      .string()
      .min(1, "Template code is required")
      .max(191, "Template code is too long"),
    templateName: z
      .string()
      .min(1, "Template name is required")
      .max(191, "Template name is too long"),
    warrantyType: warrantyTypeSchema,
    duration: z.number().int().positive("Duration must be positive").optional(),
    durationType: durationTypeSchema,
    coverageType: coverageTypeSchema,
    coverageDetails: z.string().optional(),
    maxClaims: z.number().int().min(1).optional(),
    termsConditions: z.string().optional(),
    isTransferable: z.boolean().optional().default(false),
    autoApply: z.boolean().optional().default(false),
    applicableCategories: z.string().optional(),
    status: warrantyStatusSchema.optional().default(WarrantyStatus.ACTIVE),
  })
  .superRefine((data, ctx) => {
    // Validate duration based on durationType
    if (data.durationType === DurationType.LIFETIME) {
      if (data.duration !== undefined) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Duration should not be specified for lifetime warranties",
          path: ["duration"],
        });
      }
    } else {
      if (data.duration === undefined) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Duration is required for non-lifetime warranties",
          path: ["duration"],
        });
      }
    }

  });

export type CreateWarrantyTemplateSchema = z.infer<typeof createWarrantyTemplateSchema>;

// Update warranty template schema with conditional validation
export const updateWarrantyTemplateSchema = z
  .object({
    id: z.string().min(1, "ID is required"),
    templateCode: z
      .string()
      .min(1, "Template code is required")
      .max(191, "Template code is too long")
      .optional(),
    templateName: z
      .string()
      .min(1, "Template name is required")
      .max(191, "Template name is too long")
      .optional(),
    warrantyType: warrantyTypeSchema.optional(),
    duration: z.number().int().positive("Duration must be positive").optional(),
    durationType: durationTypeSchema.optional(),
    coverageType: coverageTypeSchema.optional(),
    coverageDetails: z.string().optional(),
    maxClaims: z.number().int().min(1).optional(),
    termsConditions: z.string().optional(),
    isTransferable: z.boolean().optional(),
    autoApply: z.boolean().optional(),
    applicableCategories: z.string().optional(),
    status: warrantyStatusSchema.optional(),
  })
  .superRefine((data, ctx) => {
    // Validate duration based on durationType if both are provided
    if (
      data.durationType === DurationType.LIFETIME &&
      data.duration !== undefined
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Duration should not be specified for lifetime warranties",
        path: ["duration"],
      });
    }

  });

export type UpdateWarrantyTemplateSchema = z.infer<typeof updateWarrantyTemplateSchema>;

// Bulk create warranty template schema
export const bulkCreateWarrantyTemplateSchema = z.object({
  warrantyTemplates: z
    .array(createWarrantyTemplateSchema)
    .min(1, "At least one warranty template is required"),
});

export type BulkCreateWarrantyTemplateSchema = z.infer<typeof bulkCreateWarrantyTemplateSchema>;

// Get warranty templates query schema
export const getWarrantyTemplatesSchema = z.object({
  page: z.number().int().positive().optional().default(1),
  limit: z.number().int().positive().max(100).optional().default(10),
  from: z.string().optional(),
  to: z.string().optional(),
  templateName: z.string().optional(),
  templateCode: z.string().optional(),
  warrantyType: z.string().optional(),
  status: z.string().optional(),
  filters: z.string().optional(),
  joinOperator: z.enum(["and", "or"]).optional().default("and"),
  sort: z.string().optional(),
  isDemo: z.boolean().optional().default(false),
});

export type GetWarrantyTemplatesSchema = z.infer<typeof getWarrantyTemplatesSchema>;

// Name availability check schema
export const checkWarrantyTemplateNameSchema = z.object({
  name: z.string().min(1, "Name is required"),
  excludeId: z.string().optional(), // For checking availability when updating
});

export type CheckWarrantyTemplateNameSchema = z.infer<typeof checkWarrantyTemplateNameSchema>;

// Advanced filter schema
export const warrantyTemplateAdvancedFilterSchema = z.object({
  id: z.string(),
  value: z.string(),
  operator: z.enum(["iLike", "notILike", "eq", "ne", "isEmpty", "isNotEmpty"]),
  type: z.enum(["text", "select", "date"]),
  rowId: z.string(),
});

export type WarrantyTemplateAdvancedFilterSchema = z.infer<
  typeof warrantyTemplateAdvancedFilterSchema
>;

// Sort configuration schema
export const warrantyTemplateSortSchema = z.object({
  id: z.string(),
  desc: z.boolean(),
});

export type WarrantyTemplateSortSchema = z.infer<typeof warrantyTemplateSortSchema>;

// Warranty template form values schema (for UI forms)
export const warrantyTemplateFormSchema = z
  .object({
    templateCode: z
      .string()
      .min(1, "Template code is required")
      .max(191, "Template code is too long"),
    templateName: z
      .string()
      .min(1, "Template name is required")
      .max(191, "Template name is too long"),
    warrantyType: warrantyTypeSchema,
    duration: z
      .number()
      .int()
      .positive("Duration must be positive")
      .optional()
      .nullable(),
    durationType: durationTypeSchema,
    coverageType: coverageTypeSchema,
    coverageDetails: z.string().optional(),
    maxClaims: z.number().int().min(1).optional().nullable(),
    termsConditions: z.string().optional(),
    isTransferable: z.boolean().optional().default(false),
    autoApply: z.boolean().optional().default(false),
    applicableCategories: z.string().optional(),
    status: warrantyStatusSchema.optional().default(WarrantyStatus.ACTIVE),
  })
  .superRefine((data, ctx) => {
    // Validate duration based on durationType
    if (data.durationType === DurationType.LIFETIME) {
      if (data.duration !== null && data.duration !== undefined) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Duration should be null for lifetime warranties",
          path: ["duration"],
        });
      }
    } else {
      if (data.duration === null || data.duration === undefined) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Duration is required for non-lifetime warranties",
          path: ["duration"],
        });
      }
    }

  });

export type WarrantyTemplateFormSchema = z.infer<typeof warrantyTemplateFormSchema>;

// Validation helpers
export const validateWarrantyTemplateDuration = (
  duration: number | null | undefined,
  durationType: DurationType
) => {
  if (durationType === DurationType.LIFETIME) {
    return duration === null || duration === undefined;
  }
  return duration !== null && duration !== undefined && duration > 0;
};

export const validateWarrantyTemplateName = (name: string) => {
  const trimmedName = name.trim();
  return {
    isValid: trimmedName.length > 0 && trimmedName.length <= 191,
    message:
      trimmedName.length === 0
        ? "Template name is required"
        : trimmedName.length > 191
        ? "Template name is too long"
        : "",
  };
};

export const validateWarrantyTemplateCode = (code: string) => {
  const trimmedCode = code.trim();
  return {
    isValid: trimmedCode.length > 0 && trimmedCode.length <= 191,
    message:
      trimmedCode.length === 0
        ? "Template code is required"
        : trimmedCode.length > 191
        ? "Template code is too long"
        : "",
  };
};


// Display helpers
export const getDurationDisplayText = (
  duration: number | null | undefined,
  durationType: DurationType
): string => {
  if (durationType === DurationType.LIFETIME) {
    return "Lifetime";
  }

  if (!duration) {
    return "Not specified";
  }

  const unit = duration === 1 
    ? durationType.slice(0, -1).toLowerCase() 
    : durationType.toLowerCase();
  return `${duration} ${unit}`;
};

export const getCoverageDisplayText = (
  coverageType: CoverageType,
  coverageDetails?: string
): string => {
  const baseText = coverageType.replace(/_/g, " ").toLowerCase()
    .split(" ")
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
  
  return coverageDetails ? `${baseText}: ${coverageDetails}` : baseText;
};

export const getDurationTypeOptions = () => [
  { value: DurationType.DAYS, label: "Days" },
  { value: DurationType.MONTHS, label: "Months" },
  { value: DurationType.YEARS, label: "Years" },
  { value: DurationType.LIFETIME, label: "Lifetime" },
];

export const getWarrantyTypeOptions = () => [
  { value: WarrantyType.MANUFACTURER, label: "Manufacturer" },
  { value: WarrantyType.EXTENDED, label: "Extended" },
  { value: WarrantyType.SERVICE, label: "Service" },
  { value: WarrantyType.REPAIR, label: "Repair" },
];

export const getCoverageTypeOptions = () => [
  { value: CoverageType.FULL, label: "Full Coverage" },
  { value: CoverageType.LIMITED, label: "Limited Coverage" },
  { value: CoverageType.PARTS_ONLY, label: "Parts Only" },
  { value: CoverageType.LABOR_ONLY, label: "Labor Only" },
];

export const getStatusOptions = () => [
  { value: WarrantyStatus.ACTIVE, label: "Active" },
  { value: WarrantyStatus.INACTIVE, label: "Inactive" },
];

// Template code generation utility
export const generateTemplateCode = (
  templateName: string,
  warrantyType: WarrantyType,
  duration?: number,
  durationType?: DurationType
): string => {
  // Extract initials from template name
  const nameInitials = templateName
    .split(" ")
    .filter(word => word.length > 0)
    .map(word => word.charAt(0).toUpperCase())
    .join("")
    .slice(0, 4);

  // Get warranty type abbreviation
  const typeAbbr = warrantyType.slice(0, 3).toUpperCase();

  // Get duration part
  let durationPart = "";
  if (durationType === DurationType.LIFETIME) {
    durationPart = "LIFE";
  } else if (duration && durationType) {
    const unitAbbr = durationType.charAt(0).toUpperCase();
    durationPart = `${duration}${unitAbbr}`;
  }

  return `${nameInitials}-${typeAbbr}${durationPart ? `-${durationPart}` : ""}`;
};