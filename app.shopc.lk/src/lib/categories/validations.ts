import { z } from "zod";
import { CategoryStatus } from "@/types/category";

// Backend DTO: CreateCategoryDto
export const createCategorySchema = z.object({
  name: z.string().min(1, "Name is required"),
  shortCode: z.string().optional(),
  parentId: z.string().nullable().optional(),
  description: z.string().optional(),
  slug: z.string().optional(),
  availableOnline: z.boolean().optional().default(true),
  color: z
    .string()
    .regex(/^#[0-9A-F]{6}$/i, "Color must be a valid hex color (e.g., #3B82F6)")
    .optional(),
  status: z
    .enum([CategoryStatus.ACTIVE, CategoryStatus.INACTIVE])
    .optional()
    .default(CategoryStatus.ACTIVE),
  // Location fields
  isAllocatedToAllLocations: z.boolean().optional().default(false),
  locationIds: z.array(z.string().uuid("Invalid location ID")).optional(),
  // SEO fields
  seoTitle: z.string().optional(),
  seoDescription: z.string().optional(),
  seoKeywords: z.array(z.string()).optional(),
  imageIndex: z.number().min(0, "Image index must be 0 or greater").optional(),
});

// Backend DTO: UpdateCategoryDto
export const updateCategorySchema = z.object({
  name: z.string().min(1, "Name is required").optional(),
  shortCode: z.string().optional(),
  parentId: z.string().nullable().optional(),
  description: z.string().optional(),
  slug: z.string().optional(),
  availableOnline: z.boolean().optional(),
  color: z
    .string()
    .regex(/^#[0-9A-F]{6}$/i, "Color must be a valid hex color (e.g., #3B82F6)")
    .optional(),
  status: z.enum([CategoryStatus.ACTIVE, CategoryStatus.INACTIVE]).optional(),
  // Location fields
  isAllocatedToAllLocations: z.boolean().optional(),
  locationIds: z.array(z.string().uuid("Invalid location ID")).optional(),
  // SEO fields
  seoTitle: z.string().optional(),
  seoDescription: z.string().optional(),
  seoKeywords: z.array(z.string()).optional(),
});

// Schema for getting categories with pagination and filters
export const getCategoriesSchema = z.object({
  page: z.number().min(1).default(1),
  perPage: z.number().min(1).default(10),
  name: z.string().optional(),
  slug: z.string().optional(),
  shortCode: z.string().optional(),
  status: z.string().optional(),
  availableOnline: z.string().optional(),
  from: z.string().optional(),
  to: z.string().optional(),
  filters: z
    .array(
      z.object({
        id: z.string(),
        value: z.union([z.string(), z.array(z.string())]),
        operator: z
          .enum([
            "iLike",
            "notILike",
            "eq",
            "ne",
            "isEmpty",
            "isNotEmpty",
            "lt",
            "lte",
            "gt",
            "gte",
            "isBetween",
            "isRelativeToToday",
          ])
          .default("iLike"),
        type: z
          .enum(["text", "number", "date", "boolean", "select", "multi-select"])
          .default("text"),
        rowId: z.string().optional(),
      })
    )
    .optional()
    .default([]),
  joinOperator: z.enum(["and", "or"]).default("and"),
  sort: z
    .array(
      z.object({
        id: z.string(),
        desc: z.boolean(),
      })
    )
    .optional()
    .default([{ id: "createdAt", desc: true }]),
});

// Schema for updating category status
export const updateCategoryStatusSchema = z.object({
  id: z.string().min(1, "ID is required"),
  status: z.enum([CategoryStatus.ACTIVE, CategoryStatus.INACTIVE]),
});

// Schema for importing categories (includes status)
export const importCategorySchema = z.object({
  name: z.string().min(1, "Name is required"),
  shortCode: z.string().optional(),
  parentId: z.string().nullable().optional(),
  description: z.string().optional(),
  slug: z.string().optional(),
  availableOnline: z.boolean().optional().default(true),
  color: z
    .string()
    .regex(/^#[0-9A-F]{6}$/i, "Color must be a valid hex color (e.g., #3B82F6)")
    .optional(),
  status: z
    .enum([CategoryStatus.ACTIVE, CategoryStatus.INACTIVE])
    .optional()
    .default(CategoryStatus.ACTIVE),
  // Location fields
  isAllocatedToAllLocations: z.boolean().optional().default(false),
  locationIds: z.array(z.string().uuid("Invalid location ID")).optional(),
  // SEO fields
  seoTitle: z.string().optional(),
  seoDescription: z.string().optional(),
  seoKeywords: z.array(z.string()).optional(),
  imageIndex: z.number().min(0, "Image index must be 0 or greater").optional(),
});

// Bulk import schema for categories
export const bulkCreateCategoriesSchema = z.array(createCategorySchema);

// Bulk import schema for categories with status
export const bulkImportCategoriesSchema = z.array(importCategorySchema);

// Schema for bulk creation with images
export const bulkCreateCategoriesWithImagesSchema = z.object({
  categories: z.array(createCategorySchema),
  images: z.array(z.instanceof(File)).optional(),
});

// Schema for bulk import with images
export const bulkImportCategoriesWithImagesSchema = z.object({
  categories: z.array(importCategorySchema),
  images: z.array(z.instanceof(File)).optional(),
});

// Form schema for UI components (includes additional fields for form handling)
export const categoryFormSchema = z.object({
  name: z.string().min(1, "Name is required"),
  shortCode: z.string().optional(),
  parentId: z.string().nullable().optional(),
  description: z.string().optional(),
  slug: z.string().optional(),
  availableOnline: z.boolean().optional().default(true),
  color: z
    .string()
    .regex(/^#[0-9A-F]{6}$/i, "Color must be a valid hex color (e.g., #3B82F6)")
    .optional(),
  status: z
    .enum([CategoryStatus.ACTIVE, CategoryStatus.INACTIVE])
    .optional()
    .default(CategoryStatus.ACTIVE),
  // Location fields
  isAllocatedToAllLocations: z.boolean().optional().default(false),
  locationIds: z.array(z.string().uuid("Invalid location ID")).optional(),
  // SEO fields
  seoTitle: z.string().optional(),
  seoDescription: z.string().optional(),
  seoKeywords: z.array(z.string()).optional(),
  imageIndex: z.number().min(0, "Image index must be 0 or greater").optional(),
}).superRefine((data, ctx) => {
  // If availableOnline is true, validate required SEO fields
  if (data.availableOnline === true) {
    // Check slug
    if (!data.slug || data.slug.trim().length === 0) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Slug is required when the category is available online",
        path: ["slug"],
      });
    }
    
    // Check seoTitle
    if (!data.seoTitle || data.seoTitle.trim().length === 0) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "SEO title is required when the category is available online",
        path: ["seoTitle"],
      });
    }
    
    // Check seoDescription
    if (!data.seoDescription || data.seoDescription.trim().length === 0) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "SEO description is required when the category is available online",
        path: ["seoDescription"],
      });
    }
  }
});

// Schema for category name availability check
export const checkCategoryNameSchema = z.object({
  name: z.string().min(1, "Name is required"),
});

// Schema for category slug availability check
export const checkCategorySlugSchema = z.object({
  slug: z.string().min(1, "Slug is required"),
});

// Schema for category short code availability check
export const checkCategoryShortCodeSchema = z.object({
  shortCode: z.string().min(1, "Short code is required"),
});

// Schema for bulk hierarchy update
export const bulkUpdateCategoryHierarchySchema = z.object({
  updates: z.array(
    z.object({
      id: z.string().min(1, "Category ID is required"),
      parentId: z.string().nullable(), // null means root level, string means new parent
    })
  ),
});

export type GetCategoriesSchema = z.infer<typeof getCategoriesSchema>;
export type CreateCategorySchema = z.infer<typeof createCategorySchema>;
export type ImportCategorySchema = z.infer<typeof importCategorySchema>;
export type UpdateCategorySchema = z.infer<typeof updateCategorySchema>;
export type UpdateCategoryStatusSchema = z.infer<
  typeof updateCategoryStatusSchema
>;
export type BulkCreateCategoriesSchema = z.infer<
  typeof bulkCreateCategoriesSchema
>;
export type BulkImportCategoriesSchema = z.infer<
  typeof bulkImportCategoriesSchema
>;
export type BulkCreateCategoriesWithImagesSchema = z.infer<
  typeof bulkCreateCategoriesWithImagesSchema
>;
export type BulkImportCategoriesWithImagesSchema = z.infer<
  typeof bulkImportCategoriesWithImagesSchema
>;
export type CategoryFormSchema = z.infer<typeof categoryFormSchema>;
export type CheckCategoryNameSchema = z.infer<typeof checkCategoryNameSchema>;
export type CheckCategorySlugSchema = z.infer<typeof checkCategorySlugSchema>;
export type CheckCategoryShortCodeSchema = z.infer<
  typeof checkCategoryShortCodeSchema
>;
export type BulkUpdateCategoryHierarchySchema = z.infer<
  typeof bulkUpdateCategoryHierarchySchema
>;
