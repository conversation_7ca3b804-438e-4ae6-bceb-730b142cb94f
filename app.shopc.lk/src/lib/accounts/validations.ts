import { z } from "zod";
import {
  AccountCategory,
  ChartAccountType,
  AccountDetailType,
} from "@/types/account.enum";
import { AccountStatus } from "@/types/account";

// Backend DTO: CreateAccountDto
export const createAccountSchema = z.object({
  accountName: z
    .string()
    .min(1, "Account name is required")
    .max(191, "Account name must be less than 191 characters"),
  accountNumber: z
    .string()
    .max(191, "Account number must be less than 191 characters")
    .optional(),
  accountCategory: z.nativeEnum(AccountCategory, {
    errorMap: () => ({ message: "Account category must be a valid category" }),
  }),
  accountType: z.nativeEnum(ChartAccountType, {
    errorMap: () => ({ message: "Account type must be a valid type" }),
  }),
  accountDetailType: z.nativeEnum(AccountDetailType, {
    errorMap: () => ({
      message: "Account detail type must be a valid detail type",
    }),
  }),
  parentAccountId: z.string().uuid("Invalid parent account ID").optional(),
  openingBalance: z
    .string()
    .regex(
      /^\d+(\.\d{1,2})?$/,
      "Opening balance must be a valid decimal with up to 2 decimal places"
    )
    .optional(),
  openingBalanceDate: z
    .string()
    .regex(
      /^\d{4}-\d{2}-\d{2}$/,
      "Opening balance date must be in YYYY-MM-DD format"
    )
    .optional(),
  description: z.string().optional(),
  defaultTaxId: z.string().uuid("Invalid tax ID").optional(),
  useForBillableExpenses: z.boolean().optional().default(false),
  incomeAccountId: z.string().uuid("Invalid income account ID").optional(),
  isSystemAccount: z.boolean().optional().default(false),
  status: z.nativeEnum(AccountStatus).optional().default(AccountStatus.ACTIVE),
});

// Backend DTO: UpdateAccountDto
export const updateAccountSchema = z.object({
  accountName: z
    .string()
    .min(1, "Account name is required")
    .max(191, "Account name must be less than 191 characters")
    .optional(),
  accountNumber: z
    .string()
    .max(191, "Account number must be less than 191 characters")
    .optional(),
  accountCategory: z
    .nativeEnum(AccountCategory, {
      errorMap: () => ({
        message: "Account category must be a valid category",
      }),
    })
    .optional(),
  accountType: z
    .nativeEnum(ChartAccountType, {
      errorMap: () => ({ message: "Account type must be a valid type" }),
    })
    .optional(),
  accountDetailType: z
    .nativeEnum(AccountDetailType, {
      errorMap: () => ({
        message: "Account detail type must be a valid detail type",
      }),
    })
    .optional(),
  parentAccountId: z.string().uuid("Invalid parent account ID").optional(),
  openingBalance: z
    .string()
    .regex(
      /^\d+(\.\d{1,2})?$/,
      "Opening balance must be a valid decimal with up to 2 decimal places"
    )
    .optional(),
  openingBalanceDate: z
    .string()
    .regex(
      /^\d{4}-\d{2}-\d{2}$/,
      "Opening balance date must be in YYYY-MM-DD format"
    )
    .optional(),
  description: z.string().optional(),
  defaultTaxId: z.string().uuid("Invalid tax ID").optional(),
  useForBillableExpenses: z.boolean().optional(),
  incomeAccountId: z.string().uuid("Invalid income account ID").optional(),
  isSystemAccount: z.boolean().optional(),
  status: z.nativeEnum(AccountStatus).optional(),
});

// Schema for getting accounts with filters and pagination
export const getAccountsSchema = z.object({
  page: z.number().min(1, "Page must be at least 1").optional(),
  limit: z
    .number()
    .min(1, "Limit must be at least 1")
    .max(100, "Limit cannot exceed 100")
    .optional(),
  from: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, "From date must be in YYYY-MM-DD format")
    .optional(),
  to: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, "To date must be in YYYY-MM-DD format")
    .optional(),
  accountName: z.string().optional(),
  accountNumber: z.string().optional(),
  accountCategory: z.nativeEnum(AccountCategory).optional(),
  accountType: z.string().optional(),
  accountDetailType: z.nativeEnum(AccountDetailType).optional(),
  status: z.nativeEnum(AccountStatus).optional(),
  filters: z
    .array(
      z.object({
        id: z.string(),
        value: z.union([z.string(), z.array(z.string())]),
        operator: z
          .enum([
            "iLike",
            "notILike",
            "eq",
            "ne",
            "isEmpty",
            "isNotEmpty",
            "in",
            "notIn",
            "gt",
            "gte",
            "lt",
            "lte",
          ])
          .default("iLike"),
        type: z.enum(["text", "select", "date", "number"]).default("text"),
        rowId: z.string(),
      })
    )
    .optional()
    .default([]),
  joinOperator: z.enum(["and", "or"]).default("and"),
  sort: z
    .array(
      z.object({
        id: z.string(),
        desc: z.boolean(),
      })
    )
    .optional()
    .default([{ id: "createdAt", desc: true }]),
});

// Bulk create schema for accounts
export const bulkCreateAccountsSchema = z.array(createAccountSchema);

// Schema for importing accounts (includes status)
export const importAccountSchema = z.object({
  accountName: z
    .string()
    .min(1, "Account name is required")
    .max(191, "Account name must be less than 191 characters"),
  accountNumber: z
    .string()
    .max(191, "Account number must be less than 191 characters")
    .optional(),
  accountCategory: z.nativeEnum(AccountCategory, {
    errorMap: () => ({ message: "Account category must be a valid category" }),
  }),
  accountType: z.nativeEnum(ChartAccountType, {
    errorMap: () => ({ message: "Account type must be a valid type" }),
  }),
  accountDetailType: z.nativeEnum(AccountDetailType, {
    errorMap: () => ({
      message: "Account detail type must be a valid detail type",
    }),
  }),
  parentAccountId: z.string().uuid("Invalid parent account ID").optional(),
  openingBalance: z
    .string()
    .regex(
      /^\d+(\.\d{1,2})?$/,
      "Opening balance must be a valid decimal with up to 2 decimal places"
    )
    .optional(),
  openingBalanceDate: z
    .string()
    .regex(
      /^\d{4}-\d{2}-\d{2}$/,
      "Opening balance date must be in YYYY-MM-DD format"
    )
    .optional(),
  description: z.string().optional(),
  defaultTaxId: z.string().uuid("Invalid tax ID").optional(),
  useForBillableExpenses: z.boolean().optional().default(false),
  incomeAccountId: z.string().uuid("Invalid income account ID").optional(),
  isSystemAccount: z.boolean().optional().default(false),
  status: z.nativeEnum(AccountStatus).optional().default(AccountStatus.ACTIVE),
});

// Bulk import schema for accounts
export const bulkImportAccountsSchema = z.array(importAccountSchema);

// Form schema for UI components (includes additional fields for form handling)
export const accountFormSchema = z.object({
  accountName: z
    .string()
    .min(1, "Account name is required")
    .max(191, "Account name must be less than 191 characters"),
  accountNumber: z
    .string()
    .max(191, "Account number must be less than 191 characters")
    .optional(),
  accountCategory: z.nativeEnum(AccountCategory, {
    errorMap: () => ({ message: "Account category must be a valid category" }),
  }),
  accountType: z.nativeEnum(ChartAccountType, {
    errorMap: () => ({ message: "Account type must be a valid type" }),
  }),
  accountDetailType: z.nativeEnum(AccountDetailType, {
    errorMap: () => ({
      message: "Account detail type must be a valid detail type",
    }),
  }),
  parentAccountId: z.string().uuid("Invalid parent account ID").optional(),
  openingBalance: z
    .string()
    .regex(
      /^\d+(\.\d{1,2})?$/,
      "Opening balance must be a valid decimal with up to 2 decimal places"
    )
    .optional(),
  openingBalanceDate: z
    .string()
    .regex(
      /^\d{4}-\d{2}-\d{2}$/,
      "Opening balance date must be in YYYY-MM-DD format"
    )
    .optional(),
  description: z.string().optional(),
  defaultTaxId: z.string().uuid("Invalid tax ID").optional(),
  useForBillableExpenses: z.boolean().optional().default(false),
  incomeAccountId: z.string().uuid("Invalid income account ID").optional(),
  isSystemAccount: z.boolean().optional().default(false),
  status: z.nativeEnum(AccountStatus).optional().default(AccountStatus.ACTIVE),
});

// Schema for account name availability check
export const checkAccountNameSchema = z.object({
  accountName: z.string().min(1, "Account name is required"),
});

// Schema for account number availability check
export const checkAccountNumberSchema = z.object({
  accountNumber: z.string().min(1, "Account number is required"),
});

// Schema for updating account status
export const updateAccountStatusSchema = z.object({
  status: z.nativeEnum(AccountStatus, {
    errorMap: () => ({ message: "Status must be a valid account status" }),
  }),
});

// Schema for bulk delete
export const bulkDeleteAccountsSchema = z.object({
  ids: z
    .array(z.string().uuid("Invalid account ID"))
    .min(1, "At least one account ID is required"),
});

// Type exports for use in components
export type CreateAccountSchema = z.infer<typeof createAccountSchema>;
export type UpdateAccountSchema = z.infer<typeof updateAccountSchema>;
export type GetAccountsSchema = z.infer<typeof getAccountsSchema>;
export type BulkCreateAccountsSchema = z.infer<typeof bulkCreateAccountsSchema>;
export type ImportAccountSchema = z.infer<typeof importAccountSchema>;
export type BulkImportAccountsSchema = z.infer<typeof bulkImportAccountsSchema>;
export type AccountFormSchema = z.infer<typeof accountFormSchema>;
export type CheckAccountNameSchema = z.infer<typeof checkAccountNameSchema>;
export type CheckAccountNumberSchema = z.infer<typeof checkAccountNumberSchema>;
export type UpdateAccountStatusSchema = z.infer<
  typeof updateAccountStatusSchema
>;
export type BulkDeleteAccountsSchema = z.infer<typeof bulkDeleteAccountsSchema>;
