import {
  AccountPaginatedResponse,
  AccountIdResponse,
  BulkAccountIdsResponse,
  BulkDeleteAccountResponse,
  AccountNameAvailabilityResponse,
  AccountNumberAvailabilityResponse,
  SimpleAccountResponse,
  AccountResponse,
  FilteredAccountsResponse,
  CreateAccountDto,
  UpdateAccountDto,
  BulkCreateAccountDto,
  AccountStatus,
  AccountTableData,
} from "@/types/account";
import { ApiResponse, ApiStatus } from "@/types/common";
import { GetAccountsSchema, ImportAccountSchema } from "./validations";

// Real API imports
import {
  createAccountApi,
  bulkCreateAccountsApi,
  getAccountsApi,
  getFilteredAccountsApi,
  checkAccountNameAvailabilityApi,
  checkAccountNumberAvailabilityApi,
  getAccountsSlimApi,
  getAccountApi,
  updateAccountApi,
  deleteAccountApi,
  bulkDeleteAccountsApi,
  updateAccountStatusApi,
} from "./api";

// Demo API imports
import {
  createDemoAccountApi,
  bulkCreateDemoAccountsApi,
  getDemoAccountsApi,
  getDemoFilteredAccountsApi,
  checkDemoAccountNameAvailabilityApi,
  checkDemoAccountNumberAvailabilityApi,
  getDemoAccountsSlimApi,
  getDemoAccountApi,
  updateDemoAccountApi,
  deleteDemoAccountApi,
  bulkDeleteDemoAccountsApi,
  updateDemoAccountStatusApi,
} from "./demo";

// Create a new account
export async function createAccount(
  data: CreateAccountDto,
  isDemo: boolean
): Promise<AccountIdResponse> {
  if (isDemo) {
    return await createDemoAccountApi(data);
  }
  return await createAccountApi(data);
}

// Bulk create accounts
export async function bulkCreateAccounts(
  accounts: BulkCreateAccountDto[],
  isDemo: boolean
): Promise<BulkAccountIdsResponse> {
  if (isDemo) {
    return await bulkCreateDemoAccountsApi(accounts);
  }
  return await bulkCreateAccountsApi(accounts);
}

// Get accounts with pagination and filtering
export async function getAccountsData(
  params: GetAccountsSchema,
  isDemo: boolean
): Promise<AccountPaginatedResponse> {
  if (isDemo) {
    return await getDemoAccountsApi(params);
  }
  return await getAccountsApi(params);
}

// Get filtered accounts by category, type, and/or detail type
export async function getFilteredAccountsData(
  accountCategory?: string,
  accountType?: string,
  accountDetailType?: string,
  isDemo: boolean = false
): Promise<FilteredAccountsResponse> {
  if (isDemo) {
    return await getDemoFilteredAccountsApi(
      accountCategory,
      accountType,
      accountDetailType
    );
  }
  return await getFilteredAccountsApi(
    accountCategory,
    accountType,
    accountDetailType
  );
}

// Check account name availability
export async function checkAccountNameAvailability(
  accountName: string,
  isDemo: boolean
): Promise<AccountNameAvailabilityResponse> {
  if (isDemo) {
    return await checkDemoAccountNameAvailabilityApi(accountName);
  }
  return await checkAccountNameAvailabilityApi(accountName);
}

// Check account number availability
export async function checkAccountNumberAvailability(
  accountNumber: string,
  isDemo: boolean
): Promise<AccountNumberAvailabilityResponse> {
  if (isDemo) {
    return await checkDemoAccountNumberAvailabilityApi(accountNumber);
  }
  return await checkAccountNumberAvailabilityApi(accountNumber);
}

// Get accounts in slim format
export async function getAccountsSlimData(
  isDemo: boolean
): Promise<SimpleAccountResponse> {
  if (isDemo) {
    return await getDemoAccountsSlimApi();
  }
  return await getAccountsSlimApi();
}

// Get a single account by ID
export async function getAccountData(
  id: string,
  isDemo: boolean
): Promise<AccountResponse> {
  if (isDemo) {
    return await getDemoAccountApi(id);
  }
  return await getAccountApi(id);
}

// Update an account
export async function updateAccount(
  id: string,
  data: UpdateAccountDto,
  isDemo: boolean
): Promise<AccountIdResponse> {
  if (isDemo) {
    return await updateDemoAccountApi(id, data);
  }
  return await updateAccountApi(id, data);
}

// Delete an account
export async function deleteAccount(
  id: string,
  isDemo: boolean
): Promise<ApiResponse<{ id: string; message: string }>> {
  if (isDemo) {
    return await deleteDemoAccountApi(id);
  }
  return await deleteAccountApi(id);
}

// Bulk delete accounts
export async function bulkDeleteAccounts(
  ids: string[],
  isDemo: boolean
): Promise<BulkDeleteAccountResponse> {
  if (isDemo) {
    return await bulkDeleteDemoAccountsApi({ ids });
  }
  return await bulkDeleteAccountsApi({ ids });
}

// Update account status
export async function updateAccountStatus(
  id: string,
  status: AccountStatus,
  isDemo: boolean
): Promise<AccountIdResponse> {
  if (isDemo) {
    return await updateDemoAccountStatusApi(id, status);
  }
  return await updateAccountStatusApi(id, status);
}

// Utility function to transform account data for table display
export function transformAccountsForTable(accounts: any[]): AccountTableData[] {
  return accounts.map((account) => ({
    id: account.id,
    accountName: account.accountName,
    accountNumber: account.accountNumber,
    accountCategory: account.accountCategory,
    accountType: account.accountType,
    accountDetailType: account.accountDetailType,
    parentAccountId: account.parentAccountId,
    openingBalance: account.openingBalance,
    description: account.description,
    useForBillableExpenses: account.useForBillableExpenses,
    isSystemAccount: account.isSystemAccount,
    status: account.status,
    createdAt: account.createdAt,
    updatedAt: account.updatedAt,
  }));
}

// Utility function for bulk importing accounts
export async function bulkImportAccounts(
  data: { accounts: ImportAccountSchema[] },
  isDemo: boolean
): Promise<BulkAccountIdsResponse> {
  try {
    // Transform import data to create data format
    const createAccountsData: BulkCreateAccountDto[] = data.accounts.map(
      (account) => {
        const accountData: BulkCreateAccountDto = {
          accountName: account.accountName,
          accountCategory: account.accountCategory,
          accountType: account.accountType,
          accountDetailType: account.accountDetailType,
        };

        // Add optional fields if they exist
        if (account.accountNumber)
          accountData.accountNumber = account.accountNumber;
        if (account.parentAccountId)
          accountData.parentAccountId = account.parentAccountId;
        if (account.openingBalance)
          accountData.openingBalance = account.openingBalance;
        if (account.openingBalanceDate)
          accountData.openingBalanceDate = account.openingBalanceDate;
        if (account.description) accountData.description = account.description;
        if (account.defaultTaxId)
          accountData.defaultTaxId = account.defaultTaxId;
        if (account.useForBillableExpenses !== undefined)
          accountData.useForBillableExpenses = account.useForBillableExpenses;
        if (account.incomeAccountId)
          accountData.incomeAccountId = account.incomeAccountId;
        if (account.isSystemAccount !== undefined)
          accountData.isSystemAccount = account.isSystemAccount;
        if (account.status) accountData.status = account.status;

        return accountData;
      }
    );

    if (isDemo) {
      return await bulkCreateDemoAccountsApi(createAccountsData);
    }
    return await bulkCreateAccountsApi(createAccountsData);
  } catch (error) {
    console.error("Error bulk importing accounts:", error);
    return {
      status: ApiStatus.FAIL,
      message: "Failed to bulk import accounts",
      data: null,
    };
  }
}
