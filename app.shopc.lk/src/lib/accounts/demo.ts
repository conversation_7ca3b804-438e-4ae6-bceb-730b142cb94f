import {
  AccountDto,
  AccountSlimDto,
  AccountListDto,
  FilteredAccountDto,
  PaginatedAccountsResponseDto,
  AccountNameAvailabilityResponseDto,
  AccountIdResponseDto,
  BulkAccountIdsResponseDto,
  BulkDeleteAccountResponseDto,
  FilteredAccountsResponseDto,
  CreateAccountDto,
  UpdateAccountDto,
  BulkCreateAccountDto,
  AccountStatus,
} from "@/types/account";
import { ApiStatus } from "@/types/common";
import { GetAccountsSchema } from "./validations";
import {
  AccountCategory,
  AccountDetailType,
  ChartAccountType,
} from "@/types/account.enum";

// Demo account data
const demoAccounts: AccountDto[] = [
  {
    id: "demo-account-1",
    businessId: "demo-business-1",
    accountName: "Cash Account",
    accountNumber: "1001",
    accountCategory: AccountCategory.ASSETS,
    accountType: ChartAccountType.CASH_AND_CASH_EQUIVALENTS,
    accountDetailType: AccountDetailType.BANK,
    parentAccountId: undefined,
    openingBalance: "10000.00",
    openingBalanceDate: "2024-01-01",
    description: "Main cash account for daily operations",
    defaultTaxId: undefined,
    useForBillableExpenses: false,
    incomeAccountId: undefined,
    isSystemAccount: false,
    status: AccountStatus.ACTIVE,
    createdBy: "demo-user-1",
    updatedBy: "demo-user-1",
    createdAt: new Date("2024-01-01T00:00:00Z"),
    updatedAt: new Date("2024-01-01T00:00:00Z"),
  },
  {
    id: "demo-account-2",
    businessId: "demo-business-1",
    accountName: "Accounts Receivable",
    accountNumber: "1200",
    accountCategory: AccountCategory.ASSETS,
    accountType: ChartAccountType.ACCOUNTS_RECEIVABLE,
    accountDetailType: AccountDetailType.ACCOUNT_RECEIVABLE,
    parentAccountId: undefined,
    openingBalance: "5000.00",
    openingBalanceDate: "2024-01-01",
    description: "Customer receivables account",
    defaultTaxId: undefined,
    useForBillableExpenses: false,
    incomeAccountId: undefined,
    isSystemAccount: false,
    status: AccountStatus.ACTIVE,
    createdBy: "demo-user-1",
    updatedBy: "demo-user-1",
    createdAt: new Date("2024-01-02T00:00:00Z"),
    updatedAt: new Date("2024-01-02T00:00:00Z"),
  },
  {
    id: "demo-account-3",
    businessId: "demo-business-1",
    accountName: "Office Equipment",
    accountNumber: "1500",
    accountCategory: AccountCategory.ASSETS,
    accountType: ChartAccountType.FIXED_ASSETS,
    accountDetailType: AccountDetailType.FURNITURE_AND_FIXTURES,
    parentAccountId: undefined,
    openingBalance: "15000.00",
    openingBalanceDate: "2024-01-01",
    description: "Office furniture and equipment",
    defaultTaxId: undefined,
    useForBillableExpenses: false,
    incomeAccountId: undefined,
    isSystemAccount: false,
    status: AccountStatus.ACTIVE,
    createdBy: "demo-user-1",
    updatedBy: "demo-user-1",
    createdAt: new Date("2024-01-03T00:00:00Z"),
    updatedAt: new Date("2024-01-03T00:00:00Z"),
  },
  {
    id: "demo-account-4",
    businessId: "demo-business-1",
    accountName: "Accounts Payable",
    accountNumber: "2001",
    accountCategory: AccountCategory.LIABILITIES,
    accountType: ChartAccountType.ACCOUNTS_PAYABLE,
    accountDetailType: AccountDetailType.ACCOUNT_PAYABLE,
    parentAccountId: undefined,
    openingBalance: "3000.00",
    openingBalanceDate: "2024-01-01",
    description: "Vendor payables account",
    defaultTaxId: undefined,
    useForBillableExpenses: false,
    incomeAccountId: undefined,
    isSystemAccount: false,
    status: AccountStatus.ACTIVE,
    createdBy: "demo-user-1",
    updatedBy: "demo-user-1",
    createdAt: new Date("2024-01-04T00:00:00Z"),
    updatedAt: new Date("2024-01-04T00:00:00Z"),
  },
  {
    id: "demo-account-5",
    businessId: "demo-business-1",
    accountName: "Sales Revenue",
    accountNumber: "4001",
    accountCategory: AccountCategory.INCOME,
    accountType: ChartAccountType.INCOME,
    accountDetailType: AccountDetailType.SALES_RETAIL,
    parentAccountId: undefined,
    openingBalance: "0.00",
    openingBalanceDate: "2024-01-01",
    description: "Revenue from retail sales",
    defaultTaxId: undefined,
    useForBillableExpenses: false,
    incomeAccountId: undefined,
    isSystemAccount: false,
    status: AccountStatus.ACTIVE,
    createdBy: "demo-user-1",
    updatedBy: "demo-user-1",
    createdAt: new Date("2024-01-05T00:00:00Z"),
    updatedAt: new Date("2024-01-05T00:00:00Z"),
  },
  {
    id: "demo-account-6",
    businessId: "demo-business-1",
    accountName: "Office Rent",
    accountNumber: "5001",
    accountCategory: AccountCategory.EXPENSES,
    accountType: ChartAccountType.EXPENSES,
    accountDetailType: AccountDetailType.RENT_LEASE_BUILDINGS,
    parentAccountId: undefined,
    openingBalance: "0.00",
    openingBalanceDate: "2024-01-01",
    description: "Monthly office rent expense",
    defaultTaxId: undefined,
    useForBillableExpenses: false,
    incomeAccountId: undefined,
    isSystemAccount: false,
    status: AccountStatus.ACTIVE,
    createdBy: "demo-user-1",
    updatedBy: "demo-user-1",
    createdAt: new Date("2024-01-06T00:00:00Z"),
    updatedAt: new Date("2024-01-06T00:00:00Z"),
  },
  {
    id: "demo-account-7",
    businessId: "demo-business-1",
    accountName: "Owner's Capital",
    accountNumber: "3001",
    accountCategory: AccountCategory.EQUITY,
    accountType: ChartAccountType.OWNERS_EQUITY,
    accountDetailType: AccountDetailType.OWNERS_EQUITY,
    parentAccountId: undefined,
    openingBalance: "25000.00",
    openingBalanceDate: "2024-01-01",
    description: "Owner's initial capital investment",
    defaultTaxId: undefined,
    useForBillableExpenses: false,
    incomeAccountId: undefined,
    isSystemAccount: false,
    status: AccountStatus.ACTIVE,
    createdBy: "demo-user-1",
    updatedBy: "demo-user-1",
    createdAt: new Date("2024-01-07T00:00:00Z"),
    updatedAt: new Date("2024-01-07T00:00:00Z"),
  },
  {
    id: "demo-account-8",
    businessId: "demo-business-1",
    accountName: "Utilities Expense",
    accountNumber: "5002",
    accountCategory: AccountCategory.EXPENSES,
    accountType: ChartAccountType.EXPENSES,
    accountDetailType: AccountDetailType.UTILITIES,
    parentAccountId: undefined,
    openingBalance: "0.00",
    openingBalanceDate: "2024-01-01",
    description: "Electricity, water, and other utilities",
    defaultTaxId: undefined,
    useForBillableExpenses: false,
    incomeAccountId: undefined,
    isSystemAccount: false,
    status: AccountStatus.ACTIVE,
    createdBy: "demo-user-1",
    updatedBy: "demo-user-1",
    createdAt: new Date("2024-01-08T00:00:00Z"),
    updatedAt: new Date("2024-01-08T00:00:00Z"),
  },
];

// Transform account data for different response types
function transformToAccountListDto(account: AccountDto): AccountListDto {
  return {
    id: account.id,
    accountName: account.accountName,
    accountNumber: account.accountNumber,
    accountCategory: account.accountCategory,
    accountType: account.accountType,
    accountDetailType: account.accountDetailType,
    parentAccountId: account.parentAccountId,
    openingBalance: account.openingBalance,
    description: account.description,
    useForBillableExpenses: account.useForBillableExpenses,
    isSystemAccount: account.isSystemAccount,
    status: account.status,
  };
}

function transformToAccountSlimDto(account: AccountDto): AccountSlimDto {
  return {
    id: account.id,
    accountName: account.accountName,
    accountNumber: account.accountNumber,
    accountCategory: account.accountCategory,
    accountType: account.accountType,
    status: account.status,
  };
}

function transformToFilteredAccountDto(
  account: AccountDto
): FilteredAccountDto {
  return {
    id: account.id,
    accountName: account.accountName,
    accountCategory: account.accountCategory,
    accountType: account.accountType,
    accountDetailType: account.accountDetailType,
  };
}

// Utility function to filter accounts based on parameters
function filterAccounts(
  accounts: AccountDto[],
  params: GetAccountsSchema
): AccountDto[] {
  let filtered = [...accounts];

  if (params.accountName) {
    filtered = filtered.filter((account) =>
      account.accountName
        .toLowerCase()
        .includes(params.accountName!.toLowerCase())
    );
  }

  if (params.accountNumber) {
    filtered = filtered.filter((account) =>
      account.accountNumber
        ?.toLowerCase()
        .includes(params.accountNumber!.toLowerCase())
    );
  }

  if (params.accountCategory) {
    filtered = filtered.filter(
      (account) => account.accountCategory === params.accountCategory
    );
  }

  if (params.accountType) {
    filtered = filtered.filter(
      (account) => account.accountType === params.accountType
    );
  }

  if (params.accountDetailType) {
    filtered = filtered.filter(
      (account) => account.accountDetailType === params.accountDetailType
    );
  }

  if (params.status) {
    filtered = filtered.filter((account) => account.status === params.status);
  }

  if (params.from) {
    const fromDate = new Date(params.from);
    filtered = filtered.filter(
      (account) => new Date(account.createdAt) >= fromDate
    );
  }

  if (params.to) {
    const toDate = new Date(params.to);
    filtered = filtered.filter(
      (account) => new Date(account.createdAt) <= toDate
    );
  }

  // Apply advanced filters
  if (params.filters && params.filters.length > 0) {
    params.filters.forEach((filter) => {
      switch (filter.id) {
        case "accountName":
          if (filter.operator === "iLike") {
            filtered = filtered.filter((account) =>
              account.accountName
                .toLowerCase()
                .includes(String(filter.value).toLowerCase())
            );
          } else if (filter.operator === "notILike") {
            filtered = filtered.filter(
              (account) =>
                !account.accountName
                  .toLowerCase()
                  .includes(String(filter.value).toLowerCase())
            );
          } else if (filter.operator === "eq") {
            filtered = filtered.filter(
              (account) => account.accountName === String(filter.value)
            );
          } else if (filter.operator === "ne") {
            filtered = filtered.filter(
              (account) => account.accountName !== String(filter.value)
            );
          } else if (filter.operator === "isEmpty") {
            filtered = filtered.filter(
              (account) => !account.accountName || account.accountName === ""
            );
          } else if (filter.operator === "isNotEmpty") {
            filtered = filtered.filter(
              (account) => account.accountName && account.accountName !== ""
            );
          }
          break;
        case "accountNumber":
          if (filter.operator === "iLike") {
            filtered = filtered.filter((account) =>
              account.accountNumber
                ?.toLowerCase()
                .includes(String(filter.value).toLowerCase())
            );
          } else if (filter.operator === "notILike") {
            filtered = filtered.filter(
              (account) =>
                !account.accountNumber
                  ?.toLowerCase()
                  .includes(String(filter.value).toLowerCase())
            );
          } else if (filter.operator === "eq") {
            filtered = filtered.filter(
              (account) => account.accountNumber === String(filter.value)
            );
          } else if (filter.operator === "ne") {
            filtered = filtered.filter(
              (account) => account.accountNumber !== String(filter.value)
            );
          } else if (filter.operator === "isEmpty") {
            filtered = filtered.filter(
              (account) =>
                !account.accountNumber || account.accountNumber === ""
            );
          } else if (filter.operator === "isNotEmpty") {
            filtered = filtered.filter(
              (account) => account.accountNumber && account.accountNumber !== ""
            );
          }
          break;
        case "accountCategory":
          if (filter.operator === "eq") {
            filtered = filtered.filter(
              (account) => account.accountCategory === filter.value
            );
          } else if (filter.operator === "ne") {
            filtered = filtered.filter(
              (account) => account.accountCategory !== filter.value
            );
          }
          break;
        case "accountType":
          if (filter.operator === "iLike") {
            filtered = filtered.filter((account) =>
              account.accountType
                .toLowerCase()
                .includes(String(filter.value).toLowerCase())
            );
          } else if (filter.operator === "eq") {
            filtered = filtered.filter(
              (account) => account.accountType === String(filter.value)
            );
          } else if (filter.operator === "ne") {
            filtered = filtered.filter(
              (account) => account.accountType !== String(filter.value)
            );
          }
          break;
        case "accountDetailType":
          if (filter.operator === "eq") {
            filtered = filtered.filter(
              (account) => account.accountDetailType === filter.value
            );
          } else if (filter.operator === "ne") {
            filtered = filtered.filter(
              (account) => account.accountDetailType !== filter.value
            );
          }
          break;
        case "status":
          if (filter.operator === "eq") {
            filtered = filtered.filter(
              (account) => account.status === filter.value
            );
          } else if (filter.operator === "ne") {
            filtered = filtered.filter(
              (account) => account.status !== filter.value
            );
          }
          break;
      }
    });
  }

  return filtered;
}

// Utility function to paginate results
function paginateResults<T>(
  items: T[],
  page: number = 1,
  limit: number = 10
): { data: T[]; meta: any } {
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedItems = items.slice(startIndex, endIndex);

  return {
    data: paginatedItems,
    meta: {
      total: items.length,
      page,
      totalPages: Math.ceil(items.length / limit),
    },
  };
}

// Demo API functions
export async function createDemoAccountApi(data: CreateAccountDto): Promise<{
  status: ApiStatus;
  message: string;
  data: AccountIdResponseDto | null;
}> {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 500));

  const newAccount: AccountDto = {
    id: `demo-account-${Date.now()}`,
    businessId: "demo-business-1",
    accountName: data.accountName,
    accountNumber: data.accountNumber,
    accountCategory: data.accountCategory,
    accountType: data.accountType,
    accountDetailType: data.accountDetailType,
    parentAccountId: data.parentAccountId,
    openingBalance: data.openingBalance,
    openingBalanceDate: data.openingBalanceDate,
    description: data.description,
    defaultTaxId: data.defaultTaxId,
    useForBillableExpenses: data.useForBillableExpenses || false,
    incomeAccountId: data.incomeAccountId,
    isSystemAccount: data.isSystemAccount || false,
    status: data.status || AccountStatus.ACTIVE,
    createdBy: "demo-user-1",
    updatedBy: "demo-user-1",
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  demoAccounts.push(newAccount);

  return {
    status: ApiStatus.SUCCESS,
    message: "Account created successfully",
    data: { id: newAccount.id },
  };
}

export async function bulkCreateDemoAccountsApi(
  accounts: BulkCreateAccountDto[]
): Promise<{
  status: ApiStatus;
  message: string;
  data: BulkAccountIdsResponseDto | null;
}> {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 800));

  const createdIds: string[] = [];

  for (const accountData of accounts) {
    const newAccount: AccountDto = {
      id: `demo-account-${Date.now()}-${Math.random()}`,
      businessId: "demo-business-1",
      accountName: accountData.accountName,
      accountNumber: accountData.accountNumber,
      accountCategory: accountData.accountCategory,
      accountType: accountData.accountType,
      accountDetailType: accountData.accountDetailType,
      parentAccountId: accountData.parentAccountId,
      openingBalance: accountData.openingBalance,
      openingBalanceDate: accountData.openingBalanceDate,
      description: accountData.description,
      defaultTaxId: accountData.defaultTaxId,
      useForBillableExpenses: accountData.useForBillableExpenses || false,
      incomeAccountId: accountData.incomeAccountId,
      isSystemAccount: accountData.isSystemAccount || false,
      status: accountData.status || AccountStatus.ACTIVE,
      createdBy: "demo-user-1",
      updatedBy: "demo-user-1",
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    demoAccounts.push(newAccount);
    createdIds.push(newAccount.id);
  }

  return {
    status: ApiStatus.SUCCESS,
    message: `${accounts.length} accounts created successfully`,
    data: { ids: createdIds },
  };
}

export async function getDemoAccountsApi(params: GetAccountsSchema): Promise<{
  status: ApiStatus;
  message: string;
  data: PaginatedAccountsResponseDto | null;
}> {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 300));

  let filteredAccounts = filterAccounts(demoAccounts, params);

  // Apply sorting on the original AccountDto data before transformation
  if (params.sort && params.sort.length > 0) {
    const sortField = params.sort[0];

    switch (sortField.id) {
      case "accountName":
        filteredAccounts.sort((a, b) => {
          const comparison = a.accountName.localeCompare(b.accountName);
          return sortField.desc ? -comparison : comparison;
        });
        break;
      case "accountNumber":
        filteredAccounts.sort((a, b) => {
          const aNum = a.accountNumber || "";
          const bNum = b.accountNumber || "";
          const comparison = aNum.localeCompare(bNum);
          return sortField.desc ? -comparison : comparison;
        });
        break;
      case "accountCategory":
        filteredAccounts.sort((a, b) => {
          const comparison = a.accountCategory.localeCompare(b.accountCategory);
          return sortField.desc ? -comparison : comparison;
        });
        break;
      case "accountType":
        filteredAccounts.sort((a, b) => {
          const comparison = a.accountType.localeCompare(b.accountType);
          return sortField.desc ? -comparison : comparison;
        });
        break;
      case "openingBalance":
        filteredAccounts.sort((a, b) => {
          const aBalance = parseFloat(a.openingBalance || "0");
          const bBalance = parseFloat(b.openingBalance || "0");
          const comparison = aBalance - bBalance;
          return sortField.desc ? -comparison : comparison;
        });
        break;
      case "createdAt":
        filteredAccounts.sort((a, b) => {
          const comparison = a.createdAt.getTime() - b.createdAt.getTime();
          return sortField.desc ? -comparison : comparison;
        });
        break;
      case "updatedAt":
        filteredAccounts.sort((a, b) => {
          const comparison = a.updatedAt.getTime() - b.updatedAt.getTime();
          return sortField.desc ? -comparison : comparison;
        });
        break;
      default:
        // Default sorting: createdAt descending, then ID ascending
        filteredAccounts.sort((a, b) => {
          const dateComparison = b.createdAt.getTime() - a.createdAt.getTime();
          if (dateComparison === 0) {
            return a.id.localeCompare(b.id);
          }
          return dateComparison;
        });
        break;
    }
  } else {
    // Default sorting when no sort parameter is provided: createdAt descending, then ID ascending
    filteredAccounts.sort((a, b) => {
      const dateComparison = b.createdAt.getTime() - a.createdAt.getTime();
      if (dateComparison === 0) {
        return a.id.localeCompare(b.id);
      }
      return dateComparison;
    });
  }

  // Convert to table data format after sorting
  const tableData = filteredAccounts.map(transformToAccountListDto);

  // Apply pagination
  const total = tableData.length;
  const totalPages = Math.ceil(total / (params.limit || 10));
  const startIndex = ((params.page || 1) - 1) * (params.limit || 10);
  const endIndex = startIndex + (params.limit || 10);
  const paginatedData = tableData.slice(startIndex, endIndex);

  return {
    status: ApiStatus.SUCCESS,
    message: "Accounts retrieved successfully",
    data: {
      data: paginatedData,
      meta: {
        total,
        page: params.page || 1,
        totalPages,
      },
    },
  };
}

export async function getDemoFilteredAccountsApi(
  accountCategory?: string,
  accountType?: string,
  accountDetailType?: string
): Promise<{
  status: ApiStatus;
  message: string;
  data: FilteredAccountsResponseDto | null;
}> {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 200));

  let filtered = [...demoAccounts];

  if (accountCategory) {
    filtered = filtered.filter(
      (account) => account.accountCategory === accountCategory
    );
  }

  if (accountType) {
    filtered = filtered.filter(
      (account) => account.accountType === accountType
    );
  }

  if (accountDetailType) {
    filtered = filtered.filter(
      (account) => account.accountDetailType === accountDetailType
    );
  }

  return {
    status: ApiStatus.SUCCESS,
    message: "Filtered accounts retrieved successfully",
    data: {
      data: filtered.map(transformToFilteredAccountDto),
      message: "Filtered accounts retrieved successfully",
    },
  };
}

export async function checkDemoAccountNameAvailabilityApi(
  accountName: string
): Promise<{
  status: ApiStatus;
  message: string;
  data: AccountNameAvailabilityResponseDto | null;
}> {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 200));

  const isAvailable = !demoAccounts.some(
    (account) => account.accountName.toLowerCase() === accountName.toLowerCase()
  );

  return {
    status: ApiStatus.SUCCESS,
    message: isAvailable
      ? "Account name is available"
      : "Account name is already taken",
    data: { available: isAvailable },
  };
}

export async function checkDemoAccountNumberAvailabilityApi(
  accountNumber: string
): Promise<{
  status: ApiStatus;
  message: string;
  data: AccountNameAvailabilityResponseDto | null;
}> {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 200));

  const isAvailable = !demoAccounts.some(
    (account) => account.accountNumber === accountNumber
  );

  return {
    status: ApiStatus.SUCCESS,
    message: isAvailable
      ? "Account number is available"
      : "Account number is already taken",
    data: { available: isAvailable },
  };
}

export async function getDemoAccountsSlimApi(): Promise<{
  status: ApiStatus;
  message: string;
  data: AccountSlimDto[] | null;
}> {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 200));

  return {
    status: ApiStatus.SUCCESS,
    message: "Accounts retrieved successfully",
    data: demoAccounts.map(transformToAccountSlimDto),
  };
}

export async function getDemoAccountApi(
  id: string
): Promise<{ status: ApiStatus; message: string; data: AccountDto | null }> {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 200));

  const account = demoAccounts.find((acc) => acc.id === id);

  if (!account) {
    return {
      status: ApiStatus.FAIL,
      message: "Account not found",
      data: null,
    };
  }

  return {
    status: ApiStatus.SUCCESS,
    message: "Account retrieved successfully",
    data: account,
  };
}

export async function updateDemoAccountApi(
  id: string,
  data: UpdateAccountDto
): Promise<{
  status: ApiStatus;
  message: string;
  data: AccountIdResponseDto | null;
}> {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 400));

  const accountIndex = demoAccounts.findIndex((acc) => acc.id === id);

  if (accountIndex === -1) {
    return {
      status: ApiStatus.FAIL,
      message: "Account not found",
      data: null,
    };
  }

  // Update the account
  const updatedAccount = {
    ...demoAccounts[accountIndex],
    ...data,
    updatedBy: "demo-user-1",
    updatedAt: new Date(),
  };

  demoAccounts[accountIndex] = updatedAccount;

  return {
    status: ApiStatus.SUCCESS,
    message: "Account updated successfully",
    data: { id: updatedAccount.id },
  };
}

export async function deleteDemoAccountApi(id: string): Promise<{
  status: ApiStatus;
  message: string;
  data: { id: string; message: string } | null;
}> {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 300));

  const accountIndex = demoAccounts.findIndex((acc) => acc.id === id);

  if (accountIndex === -1) {
    return {
      status: ApiStatus.FAIL,
      message: "Account not found",
      data: null,
    };
  }

  // Remove the account
  demoAccounts.splice(accountIndex, 1);

  return {
    status: ApiStatus.SUCCESS,
    message: "Account deleted successfully",
    data: { id, message: "Account deleted successfully" },
  };
}

export async function bulkDeleteDemoAccountsApi(data: {
  ids: string[];
}): Promise<{
  status: ApiStatus;
  message: string;
  data: BulkDeleteAccountResponseDto | null;
}> {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 500));

  const deletedIds: string[] = [];

  for (const id of data.ids) {
    const accountIndex = demoAccounts.findIndex((acc) => acc.id === id);
    if (accountIndex !== -1) {
      demoAccounts.splice(accountIndex, 1);
      deletedIds.push(id);
    }
  }

  return {
    status: ApiStatus.SUCCESS,
    message: `${deletedIds.length} accounts deleted successfully`,
    data: {
      deletedCount: deletedIds.length,
      deletedIds,
    },
  };
}

export async function updateDemoAccountStatusApi(
  id: string,
  status: AccountStatus
): Promise<{
  status: ApiStatus;
  message: string;
  data: AccountIdResponseDto | null;
}> {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 300));

  const accountIndex = demoAccounts.findIndex((acc) => acc.id === id);

  if (accountIndex === -1) {
    return {
      status: ApiStatus.FAIL,
      message: "Account not found",
      data: null,
    };
  }

  // Update the account status
  demoAccounts[accountIndex] = {
    ...demoAccounts[accountIndex],
    status,
    updatedBy: "demo-user-1",
    updatedAt: new Date(),
  };

  return {
    status: ApiStatus.SUCCESS,
    message: "Account status updated successfully",
    data: { id },
  };
}
