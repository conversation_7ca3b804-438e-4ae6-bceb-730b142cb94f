import {
  ExpenseCategoryPaginatedResponse,
  ExpenseCategoryIdResponse,
  BulkExpenseCategoryIdsResponse,
  BulkDeleteExpenseCategoryResponse,
  ExpenseCategoryNameAvailabilityResponse,
  SimpleExpenseCategoryResponse,
  ExpenseCategoryResponse,
  CreateExpenseCategoryDto,
  UpdateExpenseCategoryDto,
  BulkCreateExpenseCategoryDto,
  AccountStatus,
  BulkUpdateExpenseCategoryHierarchyDto,
  BulkUpdateExpenseCategoryHierarchyResponse,
  BulkUpdateExpenseCategoryStatusResponse,
  ExpenseCategoryTableData,
  ExpenseCategoryHierarchyResponse,
} from "@/types/expense-category";
import { ApiResponse, ApiStatus } from "@/types/common";
import {
  GetExpenseCategoriesSchema,
  CreateExpenseCategorySchema,
  UpdateExpenseCategorySchema,
  BulkCreateExpenseCategoriesSchema,
} from "./validations";

// Real API imports
import {
  createExpenseCategoryApi,
  bulkCreateExpenseCategoriesApi,
  getExpenseCategoriesApi,
  checkExpenseCategoryNameAvailabilityApi,
  getExpenseCategoriesSlimApi,
  getExpenseCategoryApi,
  updateExpenseCategoryApi,
  deleteExpenseCategoryApi,
  bulkDeleteExpenseCategoriesApi,
  updateExpenseCategoryStatusApi,
  bulkUpdateExpenseCategoryHierarchyApi,
  getExpenseCategoriesHierarchyApi,
  bulkUpdateExpenseCategoryStatusApi,
} from "./api";

// Demo API imports
import {
  getDemoExpenseCategoriesTableDataApi,
  getDemoExpenseCategoriesSlimApi,
  getDemoExpenseCategoryApi,
  checkDemoExpenseCategoryNameAvailabilityApi,
  createDemoExpenseCategoryApi,
  bulkCreateDemoExpenseCategoriesApi,
  updateDemoExpenseCategoryApi,
  deleteDemoExpenseCategoryApi,
  bulkDeleteDemoExpenseCategoriesApi,
  updateDemoExpenseCategoryStatusApi,
  bulkUpdateDemoExpenseCategoryHierarchyApi,
  getDemoExpenseCategoriesHierarchyApi,
  bulkUpdateDemoExpenseCategoryStatusApi,
} from "./demo";

// Get expense categories table data with pagination and filters
export async function getExpenseCategoriesTableData(
  params: GetExpenseCategoriesSchema,
  isDemo: boolean
): Promise<ExpenseCategoryPaginatedResponse> {
  if (isDemo) {
    return await getDemoExpenseCategoriesTableDataApi(params);
  } else {
    return await getExpenseCategoriesApi(params);
  }
}

// Get expense categories in slim format (for dropdowns/selects)
export async function getExpenseCategoriesSlim(
  isDemo: boolean,
  status?: AccountStatus
): Promise<SimpleExpenseCategoryResponse> {
  if (isDemo) {
    return await getDemoExpenseCategoriesSlimApi(status);
  } else {
    return await getExpenseCategoriesSlimApi(status);
  }
}

// Get a single expense category by ID
export async function getExpenseCategory(
  id: string,
  isDemo: boolean
): Promise<ExpenseCategoryResponse> {
  if (isDemo) {
    return await getDemoExpenseCategoryApi(id);
  } else {
    return await getExpenseCategoryApi(id);
  }
}

// Check expense category name availability
export async function checkExpenseCategoryNameAvailability(
  accountName: string,
  isDemo: boolean,
  excludeId?: string
): Promise<ExpenseCategoryNameAvailabilityResponse> {
  if (isDemo) {
    return await checkDemoExpenseCategoryNameAvailabilityApi(accountName, excludeId);
  } else {
    return await checkExpenseCategoryNameAvailabilityApi(accountName, excludeId);
  }
}

// Create a new expense category
export async function createExpenseCategory(
  data: CreateExpenseCategoryDto,
  isDemo: boolean
): Promise<ExpenseCategoryIdResponse> {
  if (isDemo) {
    return await createDemoExpenseCategoryApi(data);
  } else {
    return await createExpenseCategoryApi(data);
  }
}

// Bulk create expense categories
export async function bulkCreateExpenseCategories(
  expenseCategories: BulkCreateExpenseCategoryDto[],
  isDemo: boolean
): Promise<BulkExpenseCategoryIdsResponse> {
  if (isDemo) {
    return await bulkCreateDemoExpenseCategoriesApi(expenseCategories);
  } else {
    return await bulkCreateExpenseCategoriesApi(expenseCategories);
  }
}

// Update an expense category
export async function updateExpenseCategory(
  id: string,
  data: UpdateExpenseCategoryDto,
  isDemo: boolean
): Promise<ExpenseCategoryIdResponse> {
  if (isDemo) {
    return await updateDemoExpenseCategoryApi(id, data);
  } else {
    return await updateExpenseCategoryApi(id, data);
  }
}

// Delete an expense category
export async function deleteExpenseCategory(
  id: string,
  isDemo: boolean
): Promise<ApiResponse<{ success: boolean; message: string }>> {
  if (isDemo) {
    return await deleteDemoExpenseCategoryApi(id);
  } else {
    return await deleteExpenseCategoryApi(id);
  }
}

// Bulk delete expense categories
export async function bulkDeleteExpenseCategories(
  expenseCategoryIds: string[],
  isDemo: boolean
): Promise<BulkDeleteExpenseCategoryResponse> {
  if (isDemo) {
    return await bulkDeleteDemoExpenseCategoriesApi(expenseCategoryIds);
  } else {
    return await bulkDeleteExpenseCategoriesApi(expenseCategoryIds);
  }
}

// Update expense category status (convenience function)
export async function updateExpenseCategoryStatus(
  id: string,
  status: AccountStatus,
  isDemo: boolean
): Promise<ExpenseCategoryIdResponse> {
  if (isDemo) {
    return await updateDemoExpenseCategoryApi(id, { status });
  } else {
    return await updateExpenseCategoryStatusApi(id, status);
  }
}

// Bulk update expense category hierarchy (parent-child relationships)
export async function bulkUpdateExpenseCategoryHierarchy(
  data: BulkUpdateExpenseCategoryHierarchyDto,
  isDemo: boolean
): Promise<BulkUpdateExpenseCategoryHierarchyResponse> {
  if (isDemo) {
    return await bulkUpdateDemoExpenseCategoryHierarchyApi(data);
  } else {
    return await bulkUpdateExpenseCategoryHierarchyApi(data);
  }
}

// Utility functions for data transformation
export function transformExpenseCategoryForForm(expenseCategory: any) {
  return {
    accountName: expenseCategory.accountName || "",
    accountNumber: expenseCategory.accountNumber || "",
    accountCategory: expenseCategory.accountCategory,
    accountType: expenseCategory.accountType,
    accountDetailType: expenseCategory.accountDetailType,
    parentAccountId: expenseCategory.parentAccountId || null,
    openingBalance: expenseCategory.openingBalance || "0.00",
    openingBalanceDate: expenseCategory.openingBalanceDate || "",
    description: expenseCategory.description || "",
    defaultTaxId: expenseCategory.defaultTaxId || null,
    useForBillableExpenses: expenseCategory.useForBillableExpenses ?? false,
    incomeAccountId: expenseCategory.incomeAccountId || null,
    status: expenseCategory.status || AccountStatus.ACTIVE,
  };
}

// Get expense categories hierarchy data
export async function getExpenseCategoriesHierarchyData(
  isDemo: boolean
): Promise<ExpenseCategoryHierarchyResponse> {
  if (isDemo) {
    return await getDemoExpenseCategoriesHierarchyApi();
  } else {
    return await getExpenseCategoriesHierarchyApi();
  }
}

// Bulk update expense category status
export async function bulkUpdateExpenseCategoryStatus(
  expenseCategoryIds: string[],
  status: AccountStatus,
  isDemo: boolean
): Promise<BulkUpdateExpenseCategoryStatusResponse> {
  if (isDemo) {
    return await bulkUpdateDemoExpenseCategoryStatusApi(expenseCategoryIds, status);
  } else {
    return await bulkUpdateExpenseCategoryStatusApi(expenseCategoryIds, status);
  }
}
