import {
  ExpenseCategoryDto,
  ExpenseCategorySlimDto,
  ExpenseCategoryTableData,
  AccountStatus,
  AccountCategory,
  ChartAccountType,
  AccountDetailType,
  ExpenseCategoryPaginatedResponse,
  ExpenseCategoryIdResponse,
  BulkExpenseCategoryIdsResponse,
  BulkDeleteExpenseCategoryResponse,
  ExpenseCategoryNameAvailabilityResponse,
  SimpleExpenseCategoryResponse,
  ExpenseCategoryHierarchyResponse,
  ExpenseCategoryHierarchyData,
  ExpenseCategoryResponse,
  CreateExpenseCategoryDto,
  UpdateExpenseCategoryDto,
  BulkCreateExpenseCategoryDto,
  PaginatedExpenseCategoriesResponseDto,
  BulkUpdateExpenseCategoryHierarchyDto,
  BulkUpdateExpenseCategoryHierarchyResponse,
  BulkUpdateExpenseCategoryStatusResponse,
} from "@/types/expense-category";
import { ApiStatus, ApiResponse } from "@/types/common";
import { GetExpenseCategoriesSchema } from "./validations";

// Raw demo expense categories data
const rawDemoExpenseCategories = [
  {
    expenseCategoryId: "exp_cat_1",
    businessId: "biz_1",
    accountName: "Office Supplies",
    accountNumber: "5000",
    accountCategory: AccountCategory.EXPENSES,
    accountType: ChartAccountType.EXPENSES,
    accountDetailType: AccountDetailType.OFFICE_EXPENSES,
    parentAccountId: undefined,
    openingBalance: "0.00",
    openingBalanceDate: undefined,
    description: "Office supplies and stationery",
    defaultTaxId: undefined,
    useForBillableExpenses: false,
    incomeAccountId: undefined,
    isSystemAccount: false,
    status: AccountStatus.ACTIVE,
    createdByName: "John Doe",
    updatedByName: undefined,
    deletedAt: undefined,
    createdAt: new Date("2024-01-01T00:00:00Z"),
    updatedAt: new Date("2024-01-01T00:00:00Z"),
  },
  {
    expenseCategoryId: "exp_cat_2",
    businessId: "biz_1",
    accountName: "Travel Expenses",
    accountNumber: "5100",
    accountCategory: AccountCategory.EXPENSES,
    accountType: ChartAccountType.EXPENSES,
    accountDetailType: AccountDetailType.TRAVEL,
    parentAccountId: undefined,
    openingBalance: "0.00",
    openingBalanceDate: undefined,
    description: "Business travel and accommodation",
    defaultTaxId: undefined,
    useForBillableExpenses: true,
    incomeAccountId: undefined,
    isSystemAccount: false,
    status: AccountStatus.ACTIVE,
    createdByName: "Jane Smith",
    updatedByName: undefined,
    deletedAt: undefined,
    createdAt: new Date("2024-01-02T00:00:00Z"),
    updatedAt: new Date("2024-01-02T00:00:00Z"),
  },
  {
    expenseCategoryId: "exp_cat_3",
    businessId: "biz_1",
    accountName: "Utilities",
    accountNumber: "5200",
    accountCategory: AccountCategory.EXPENSES,
    accountType: ChartAccountType.EXPENSES,
    accountDetailType: AccountDetailType.UTILITIES,
    parentAccountId: undefined,
    openingBalance: "0.00",
    openingBalanceDate: undefined,
    description: "Electricity, water, gas, and internet",
    defaultTaxId: undefined,
    useForBillableExpenses: false,
    incomeAccountId: undefined,
    isSystemAccount: false,
    status: AccountStatus.ACTIVE,
    createdByName: "Bob Johnson",
    updatedByName: undefined,
    deletedAt: undefined,
    createdAt: new Date("2024-01-03T00:00:00Z"),
    updatedAt: new Date("2024-01-03T00:00:00Z"),
  },
  {
    expenseCategoryId: "exp_cat_4",
    businessId: "biz_1",
    accountName: "Marketing & Advertising",
    accountNumber: "5300",
    accountCategory: AccountCategory.EXPENSES,
    accountType: ChartAccountType.EXPENSES,
    accountDetailType: AccountDetailType.ADVERTISING_PROMOTIONAL,
    parentAccountId: undefined,
    openingBalance: "0.00",
    openingBalanceDate: undefined,
    description: "Marketing campaigns and promotional activities",
    defaultTaxId: undefined,
    useForBillableExpenses: false,
    incomeAccountId: undefined,
    isSystemAccount: false,
    status: AccountStatus.ACTIVE,
    createdByName: "Alice Brown",
    updatedByName: undefined,
    deletedAt: undefined,
    createdAt: new Date("2024-01-04T00:00:00Z"),
    updatedAt: new Date("2024-01-04T00:00:00Z"),
  },
  {
    expenseCategoryId: "exp_cat_5",
    businessId: "biz_1",
    accountName: "Professional Fees",
    accountNumber: "5400",
    accountCategory: AccountCategory.EXPENSES,
    accountType: ChartAccountType.EXPENSES,
    accountDetailType: AccountDetailType.LEGAL_PROFESSIONAL_FEES,
    parentAccountId: undefined,
    openingBalance: "0.00",
    openingBalanceDate: undefined,
    description: "Legal, accounting, and consulting fees",
    defaultTaxId: undefined,
    useForBillableExpenses: true,
    incomeAccountId: undefined,
    isSystemAccount: false,
    status: AccountStatus.INACTIVE,
    createdByName: "Charlie Wilson",
    updatedByName: undefined,
    deletedAt: undefined,
    createdAt: new Date("2024-01-05T00:00:00Z"),
    updatedAt: new Date("2024-01-05T00:00:00Z"),
  },
];

// Convert raw data to proper format
const demoExpenseCategories: ExpenseCategoryDto[] = rawDemoExpenseCategories;

// Get demo expense categories with pagination and filters
export async function getDemoExpenseCategoriesTableDataApi(
  params: GetExpenseCategoriesSchema
): Promise<ExpenseCategoryPaginatedResponse> {
  try {
    await new Promise((resolve) => setTimeout(resolve, 500)); // Simulate API delay

    let filteredCategories = [...demoExpenseCategories];

    // Apply search filter
    if (params.search) {
      const searchLower = params.search.toLowerCase();
      filteredCategories = filteredCategories.filter(
        (cat) =>
          cat.accountName.toLowerCase().includes(searchLower) ||
          cat.accountNumber?.toLowerCase().includes(searchLower) ||
          cat.description?.toLowerCase().includes(searchLower)
      );
    }

    // Apply status filter
    if (params.status) {
      filteredCategories = filteredCategories.filter(
        (cat) => cat.status === params.status
      );
    }

    // Apply account category filter
    if (params.accountCategory) {
      filteredCategories = filteredCategories.filter(
        (cat) => cat.accountCategory === params.accountCategory
      );
    }

    // Apply pagination
    const page = params.page || 1;
    const limit = params.limit || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedCategories = filteredCategories.slice(startIndex, endIndex);

    // Convert to table data format
    const tableData: ExpenseCategoryTableData[] = paginatedCategories.map(
      (cat) => ({
        expenseCategoryId: cat.expenseCategoryId,
        accountName: cat.accountName,
        accountNumber: cat.accountNumber,
        accountCategory: cat.accountCategory,
        accountType: cat.accountType,
        accountDetailType: cat.accountDetailType,
        parentAccountId: cat.parentAccountId,
        parentAccountName: undefined, // No parent names in demo data
        openingBalance: cat.openingBalance,
        openingBalanceDate: cat.openingBalanceDate,
        description: cat.description,
        defaultTaxId: cat.defaultTaxId,
        useForBillableExpenses: cat.useForBillableExpenses,
        incomeAccountId: cat.incomeAccountId,
        isSystemAccount: cat.isSystemAccount,
        status: cat.status,
        createdAt: cat.createdAt,
        updatedAt: cat.updatedAt,
      })
    );

    const responseData: PaginatedExpenseCategoriesResponseDto = {
      expenseCategories: tableData,
      page,
      limit,
      total: filteredCategories.length,
      totalPages: Math.ceil(filteredCategories.length / limit),
      hasNextPage: page < Math.ceil(filteredCategories.length / limit),
      hasPrevPage: page > 1,
    };

    return {
      status: ApiStatus.SUCCESS,
      message: "Expense categories retrieved successfully",
      data: responseData,
    };
  } catch (error) {
    return {
      status: ApiStatus.FAIL,
      message: "Failed to retrieve expense categories",
      data: null,
    };
  }
}

// Get demo expense categories in slim format
export async function getDemoExpenseCategoriesSlimApi(
  status?: AccountStatus
): Promise<SimpleExpenseCategoryResponse> {
  try {
    await new Promise((resolve) => setTimeout(resolve, 300)); // Simulate API delay

    let filteredCategories = [...demoExpenseCategories];

    // Apply status filter
    if (status) {
      filteredCategories = filteredCategories.filter(
        (cat) => cat.status === status
      );
    }

    const slimData: ExpenseCategorySlimDto[] = filteredCategories.map(
      (cat) => ({
        expenseCategoryId: cat.expenseCategoryId,
        accountName: cat.accountName,
        accountNumber: cat.accountNumber,
        status: cat.status,
      })
    );

    return {
      status: ApiStatus.SUCCESS,
      message: "Expense categories retrieved successfully",
      data: slimData,
    };
  } catch (error) {
    return {
      status: ApiStatus.FAIL,
      message: "Failed to retrieve expense categories",
      data: null,
    };
  }
}

// Get a single demo expense category by ID
export async function getDemoExpenseCategoryApi(
  id: string
): Promise<ExpenseCategoryResponse> {
  try {
    await new Promise((resolve) => setTimeout(resolve, 200)); // Simulate API delay

    const category = demoExpenseCategories.find(
      (cat) => cat.expenseCategoryId === id
    );

    if (!category) {
      return {
        status: ApiStatus.FAIL,
        message: "Expense category not found",
        data: null,
      };
    }

    return {
      status: ApiStatus.SUCCESS,
      message: "Expense category retrieved successfully",
      data: category,
    };
  } catch (error) {
    return {
      status: ApiStatus.FAIL,
      message: "Failed to retrieve expense category",
      data: null,
    };
  }
}

// Check demo expense category name availability
export async function checkDemoExpenseCategoryNameAvailabilityApi(
  accountName: string,
  excludeId?: string
): Promise<ExpenseCategoryNameAvailabilityResponse> {
  try {
    await new Promise((resolve) => setTimeout(resolve, 200)); // Simulate API delay

    const existingCategory = demoExpenseCategories.find(
      (cat) =>
        cat.accountName.toLowerCase() === accountName.toLowerCase() &&
        cat.expenseCategoryId !== excludeId
    );

    return {
      status: ApiStatus.SUCCESS,
      message: "Name availability checked successfully",
      data: { available: !existingCategory },
    };
  } catch (error) {
    return {
      status: ApiStatus.FAIL,
      message: "Failed to check name availability",
      data: null,
    };
  }
}

// Create a demo expense category
export async function createDemoExpenseCategoryApi(
  data: CreateExpenseCategoryDto
): Promise<ExpenseCategoryIdResponse> {
  try {
    await new Promise((resolve) => setTimeout(resolve, 500)); // Simulate API delay

    const newId = `exp_cat_${Date.now()}`;
    const newCategory: ExpenseCategoryDto = {
      expenseCategoryId: newId,
      businessId: "biz_1",
      accountName: data.accountName,
      accountNumber: data.accountNumber,
      accountCategory: data.accountCategory,
      accountType: data.accountType,
      accountDetailType: data.accountDetailType,
      parentAccountId: data.parentAccountId || undefined,
      openingBalance: data.openingBalance || "0.00",
      openingBalanceDate: data.openingBalanceDate || undefined,
      description: data.description,
      defaultTaxId: data.defaultTaxId || undefined,
      useForBillableExpenses: data.useForBillableExpenses || false,
      incomeAccountId: data.incomeAccountId || undefined,
      isSystemAccount: false,
      status: data.status || AccountStatus.ACTIVE,
      createdByName: "Demo User",
      updatedByName: undefined,
      deletedAt: undefined,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    demoExpenseCategories.push(newCategory);

    return {
      status: ApiStatus.SUCCESS,
      message: "Expense category created successfully",
      data: { expenseCategoryId: newId },
    };
  } catch (error) {
    return {
      status: ApiStatus.FAIL,
      message: "Failed to create expense category",
      data: null,
    };
  }
}

// Bulk create demo expense categories
export async function bulkCreateDemoExpenseCategoriesApi(
  expenseCategories: BulkCreateExpenseCategoryDto[]
): Promise<BulkExpenseCategoryIdsResponse> {
  try {
    await new Promise((resolve) => setTimeout(resolve, 800)); // Simulate API delay

    const newIds: string[] = [];

    for (const data of expenseCategories) {
      const newId = `exp_cat_${Date.now()}_${Math.random()
        .toString(36)
        .substring(2, 11)}`;
      const newCategory: ExpenseCategoryDto = {
        expenseCategoryId: newId,
        businessId: "biz_1",
        accountName: data.accountName,
        accountNumber: data.accountNumber,
        accountCategory: data.accountCategory,
        accountType: data.accountType,
        accountDetailType: data.accountDetailType,
        parentAccountId: data.parentAccountId || undefined,
          openingBalance: data.openingBalance || "0.00",
        openingBalanceDate: data.openingBalanceDate || undefined,
        description: data.description,
        defaultTaxId: data.defaultTaxId || undefined,
        useForBillableExpenses: data.useForBillableExpenses || false,
        incomeAccountId: data.incomeAccountId || undefined,
        isSystemAccount: false,
        status: data.status || AccountStatus.ACTIVE,
        createdByName: "Demo User",
        updatedByName: undefined,
        deletedAt: undefined,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      demoExpenseCategories.push(newCategory);
      newIds.push(newId);
    }

    return {
      status: ApiStatus.SUCCESS,
      message: "Expense categories created successfully",
      data: { expenseCategoryIds: newIds },
    };
  } catch (error) {
    return {
      status: ApiStatus.FAIL,
      message: "Failed to create expense categories",
      data: null,
    };
  }
}

// Update a demo expense category
export async function updateDemoExpenseCategoryApi(
  id: string,
  data: UpdateExpenseCategoryDto
): Promise<ExpenseCategoryIdResponse> {
  try {
    await new Promise((resolve) => setTimeout(resolve, 400)); // Simulate API delay

    const categoryIndex = demoExpenseCategories.findIndex(
      (cat) => cat.expenseCategoryId === id
    );

    if (categoryIndex === -1) {
      return {
        status: ApiStatus.FAIL,
        message: "Expense category not found",
        data: null,
      };
    }

    // Update the category
    const updatedCategory = {
      ...demoExpenseCategories[categoryIndex],
      ...data,
      updatedAt: new Date(),
      updatedByName: "Demo User",
    };

    demoExpenseCategories[categoryIndex] = updatedCategory;

    return {
      status: ApiStatus.SUCCESS,
      message: "Expense category updated successfully",
      data: { expenseCategoryId: id },
    };
  } catch (error) {
    return {
      status: ApiStatus.FAIL,
      message: "Failed to update expense category",
      data: null,
    };
  }
}

// Delete a demo expense category
export async function deleteDemoExpenseCategoryApi(
  id: string
): Promise<ApiResponse<{ success: boolean; message: string }>> {
  try {
    await new Promise((resolve) => setTimeout(resolve, 300)); // Simulate API delay

    const categoryIndex = demoExpenseCategories.findIndex(
      (cat) => cat.expenseCategoryId === id
    );

    if (categoryIndex === -1) {
      return {
        status: ApiStatus.FAIL,
        message: "Expense category not found",
        data: null,
      };
    }

    demoExpenseCategories.splice(categoryIndex, 1);

    return {
      status: ApiStatus.SUCCESS,
      message: "Expense category deleted successfully",
      data: { success: true, message: "Expense category deleted successfully" },
    };
  } catch (error) {
    return {
      status: ApiStatus.FAIL,
      message: "Failed to delete expense category",
      data: null,
    };
  }
}

// Bulk delete demo expense categories
export async function bulkDeleteDemoExpenseCategoriesApi(
  expenseCategoryIds: string[]
): Promise<BulkDeleteExpenseCategoryResponse> {
  try {
    await new Promise((resolve) => setTimeout(resolve, 600)); // Simulate API delay

    const deletedIds: string[] = [];

    for (const id of expenseCategoryIds) {
      const categoryIndex = demoExpenseCategories.findIndex(
        (cat) => cat.expenseCategoryId === id
      );

      if (categoryIndex !== -1) {
        demoExpenseCategories.splice(categoryIndex, 1);
        deletedIds.push(id);
      }
    }

    return {
      status: ApiStatus.SUCCESS,
      message: `${deletedIds.length} expense categories deleted successfully`,
      data: {
        deleted: deletedIds.length,
        message: `${deletedIds.length} expense categories deleted successfully`,
        deletedIds,
      },
    };
  } catch (error) {
    return {
      status: ApiStatus.FAIL,
      message: "Failed to delete expense categories",
      data: null,
    };
  }
}

// Update demo expense category status
export async function updateDemoExpenseCategoryStatusApi(
  id: string,
  status: AccountStatus
): Promise<ExpenseCategoryIdResponse> {
  return updateDemoExpenseCategoryApi(id, { status });
}

// Bulk update demo expense category hierarchy
export async function bulkUpdateDemoExpenseCategoryHierarchyApi(
  data: BulkUpdateExpenseCategoryHierarchyDto
): Promise<BulkUpdateExpenseCategoryHierarchyResponse> {
  try {
    await new Promise((resolve) => setTimeout(resolve, 500)); // Simulate API delay

    const updated: number[] = [];
    const failed: Array<{ id: string; error: string }> = [];

    for (const update of data.updates) {
      const categoryIndex = demoExpenseCategories.findIndex(
        (cat) => cat.expenseCategoryId === update.id
      );

      if (categoryIndex === -1) {
        failed.push({ id: update.id, error: "Expense category not found" });
        continue;
      }

      demoExpenseCategories[categoryIndex] = {
        ...demoExpenseCategories[categoryIndex],
        parentAccountId: update.parentAccountId || undefined,
        updatedAt: new Date(),
        updatedByName: "Demo User",
      };

      updated.push(categoryIndex);
    }

    return {
      status: ApiStatus.SUCCESS,
      message: `${updated.length} expense categories updated successfully`,
      data: {
        updated: updated.length,
        failed,
      },
    };
  } catch (error) {
    return {
      status: ApiStatus.FAIL,
      message: "Failed to update expense category hierarchy",
      data: null,
    };
  }
}

// Get demo expense categories hierarchy
export async function getDemoExpenseCategoriesHierarchyApi(): Promise<ExpenseCategoryHierarchyResponse> {
  try {
    await new Promise((resolve) => setTimeout(resolve, 300)); // Simulate API delay

    const hierarchyData: ExpenseCategoryHierarchyData[] =
      demoExpenseCategories.map((cat) => ({
        expenseCategoryId: cat.expenseCategoryId,
        accountName: cat.accountName,
        parentAccountId: cat.parentAccountId,
      }));

    return {
      status: ApiStatus.SUCCESS,
      message: "Expense categories hierarchy retrieved successfully",
      data: hierarchyData,
    };
  } catch (error) {
    return {
      status: ApiStatus.FAIL,
      message: "Failed to retrieve expense categories hierarchy",
      data: null,
    };
  }
}

// Bulk update demo expense category status
export async function bulkUpdateDemoExpenseCategoryStatusApi(
  expenseCategoryIds: string[],
  status: AccountStatus
): Promise<BulkUpdateExpenseCategoryStatusResponse> {
  try {
    await new Promise((resolve) => setTimeout(resolve, 600)); // Simulate API delay

    const updatedIds: string[] = [];
    const failed: Array<{ expenseCategoryId: string; error: string }> = [];

    for (const id of expenseCategoryIds) {
      const categoryIndex = demoExpenseCategories.findIndex(
        (cat) => cat.expenseCategoryId === id
      );

      if (categoryIndex === -1) {
        failed.push({
          expenseCategoryId: id,
          error: "Expense category not found",
        });
        continue;
      }

      demoExpenseCategories[categoryIndex] = {
        ...demoExpenseCategories[categoryIndex],
        status,
        updatedAt: new Date(),
        updatedByName: "Demo User",
      };

      updatedIds.push(id);
    }

    return {
      status: ApiStatus.SUCCESS,
      message: `${updatedIds.length} expense categories updated successfully`,
      data: {
        updated: updatedIds.length,
        message: `${updatedIds.length} expense categories updated successfully`,
        updatedIds,
        failed: failed.length > 0 ? failed : undefined,
      },
    };
  } catch (error) {
    return {
      status: ApiStatus.FAIL,
      message: "Failed to update expense category status",
      data: null,
    };
  }
}
