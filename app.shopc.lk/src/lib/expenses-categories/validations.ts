import { z } from "zod";
import { AccountStatus, AccountCategory, ChartAccountType, AccountDetailType } from "@/types/expense-category";

// Backend DTO: CreateExpenseCategoryDto
export const createExpenseCategorySchema = z.object({
  accountName: z.string().min(1, "Account name is required"),
  accountNumber: z.string().optional(),
  accountCategory: z.nativeEnum(AccountCategory),
  accountType: z.nativeEnum(ChartAccountType),
  accountDetailType: z.nativeEnum(AccountDetailType),
  parentAccountId: z.string().uuid("Invalid parent account ID").optional(),
  openingBalance: z.string().optional().default("0.00"),
  openingBalanceDate: z.string().optional(),
  description: z.string().optional(),
  defaultTaxId: z.string().uuid("Invalid tax ID").optional(),
  useForBillableExpenses: z.boolean().optional().default(false),
  incomeAccountId: z.string().uuid("Invalid income account ID").optional(),
  status: z
    .enum([AccountStatus.ACTIVE, AccountStatus.INACTIVE])
    .optional()
    .default(AccountStatus.ACTIVE),
});

// Backend DTO: UpdateExpenseCategoryDto
export const updateExpenseCategorySchema = z.object({
  accountName: z.string().min(1, "Account name is required").optional(),
  accountNumber: z.string().optional(),
  accountCategory: z.nativeEnum(AccountCategory).optional(),
  accountType: z.nativeEnum(ChartAccountType).optional(),
  accountDetailType: z.nativeEnum(AccountDetailType).optional(),
  parentAccountId: z.string().uuid("Invalid parent account ID").optional(),
  openingBalance: z.string().optional(),
  openingBalanceDate: z.string().optional(),
  description: z.string().optional(),
  defaultTaxId: z.string().uuid("Invalid tax ID").optional(),
  useForBillableExpenses: z.boolean().optional(),
  incomeAccountId: z.string().uuid("Invalid income account ID").optional(),
  status: z.enum([AccountStatus.ACTIVE, AccountStatus.INACTIVE]).optional(),
});

// Schema for getting expense categories with pagination and filters
export const getExpenseCategoriesSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).default(10),
  from: z.string().optional(),
  to: z.string().optional(),
  search: z.string().optional(),
  status: z.enum([AccountStatus.ACTIVE, AccountStatus.INACTIVE]).optional(),
  accountCategory: z.nativeEnum(AccountCategory).optional(),
  filters: z
    .array(
      z.object({
        id: z.string(),
        value: z.union([z.string(), z.array(z.string())]),
        operator: z
          .enum([
            "iLike",
            "notILike",
            "eq",
            "ne",
            "isEmpty",
            "isNotEmpty",
            "lt",
            "lte",
            "gt",
            "gte",
            "isBetween",
            "isRelativeToToday",
          ])
          .default("iLike"),
        type: z
          .enum(["text", "number", "date", "boolean", "select", "multi-select"])
          .default("text"),
        rowId: z.string().optional(),
      })
    )
    .optional()
    .default([]),
  joinOperator: z.enum(["and", "or"]).default("and"),
  sort: z
    .array(
      z.object({
        id: z.string(),
        desc: z.boolean(),
      })
    )
    .optional()
    .default([{ id: "createdAt", desc: true }]),
});

// Schema for updating expense category status
export const updateExpenseCategoryStatusSchema = z.object({
  expenseCategoryId: z.string().min(1, "Expense category ID is required"),
  status: z.enum([AccountStatus.ACTIVE, AccountStatus.INACTIVE]),
});

// Bulk create schema for expense categories
export const bulkCreateExpenseCategoriesSchema = z.array(createExpenseCategorySchema);

// Form schema for UI components (includes additional fields for form handling)
export const expenseCategoryFormSchema = z.object({
  accountName: z.string().min(1, "Account name is required"),
  accountNumber: z.string().optional(),
  accountCategory: z.nativeEnum(AccountCategory),
  accountType: z.nativeEnum(ChartAccountType),
  accountDetailType: z.nativeEnum(AccountDetailType),
  parentAccountId: z.string().uuid("Invalid parent account ID").optional(),
  openingBalance: z.string().optional().default("0.00"),
  openingBalanceDate: z.string().optional(),
  description: z.string().optional(),
  defaultTaxId: z.string().uuid("Invalid tax ID").optional(),
  useForBillableExpenses: z.boolean().optional().default(false),
  incomeAccountId: z.string().uuid("Invalid income account ID").optional(),
  status: z
    .enum([AccountStatus.ACTIVE, AccountStatus.INACTIVE])
    .optional()
    .default(AccountStatus.ACTIVE),
});

// Schema for expense category name availability check
export const checkExpenseCategoryNameSchema = z.object({
  accountName: z.string().min(1, "Account name is required"),
});

// Schema for bulk hierarchy update
export const bulkUpdateExpenseCategoryHierarchySchema = z.object({
  updates: z.array(
    z.object({
      id: z.string().min(1, "Expense category ID is required"),
      parentAccountId: z.string().nullable(), // null means root level, string means new parent
    })
  ),
});

// Schema for bulk status update
export const bulkUpdateExpenseCategoryStatusSchema = z.object({
  expenseCategoryIds: z.array(z.string().uuid("Invalid expense category ID")),
  status: z.enum([AccountStatus.ACTIVE, AccountStatus.INACTIVE]),
});

// Schema for bulk delete
export const bulkDeleteExpenseCategoriesSchema = z.object({
  expenseCategoryIds: z.array(z.string().uuid("Invalid expense category ID")),
});

export type GetExpenseCategoriesSchema = z.infer<typeof getExpenseCategoriesSchema>;
export type CreateExpenseCategorySchema = z.infer<typeof createExpenseCategorySchema>;
export type UpdateExpenseCategorySchema = z.infer<typeof updateExpenseCategorySchema>;
export type UpdateExpenseCategoryStatusSchema = z.infer<
  typeof updateExpenseCategoryStatusSchema
>;
export type BulkCreateExpenseCategoriesSchema = z.infer<
  typeof bulkCreateExpenseCategoriesSchema
>;
export type ExpenseCategoryFormSchema = z.infer<typeof expenseCategoryFormSchema>;
export type CheckExpenseCategoryNameSchema = z.infer<typeof checkExpenseCategoryNameSchema>;
export type BulkUpdateExpenseCategoryHierarchySchema = z.infer<
  typeof bulkUpdateExpenseCategoryHierarchySchema
>;
export type BulkUpdateExpenseCategoryStatusSchema = z.infer<
  typeof bulkUpdateExpenseCategoryStatusSchema
>;
export type BulkDeleteExpenseCategoriesSchema = z.infer<
  typeof bulkDeleteExpenseCategoriesSchema
>;
