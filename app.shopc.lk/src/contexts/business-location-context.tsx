"use client";

import {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
  useCallback,
} from "react";
import { useAuth } from "./auth-contexts";
import { LocationSlimDto } from "@/types/location";
import { useLocations, useLocationsSlim } from "@/lib/locations/hooks";
import { AllowedBusinessLocation } from "@/types/auth";
import Cookies from "js-cookie";

// Cookie configuration for selected locations
const SELECTED_LOCATIONS_COOKIE = "selectedLocationIds";
const COOKIE_EXPIRES_DAYS = 30; // 30 days expiration

interface BusinessLocationContextType {
  locations: LocationSlimDto[];
  selectedLocations: LocationSlimDto[];
  selectedLocationIds: string[];
  toggleLocation: (id: string) => void;
  selectAllLocations: () => void;
  clearAllLocations: () => void;
  isLoading: boolean;
}

export const BusinessLocationContext = createContext<
  BusinessLocationContextType | undefined
>(undefined);

export function BusinessLocationProvider({
  children,
}: {
  children: ReactNode;
  isDemo?: boolean;
}) {
  const [selectedLocationIds, setSelectedLocationIds] = useState<string[]>([]);
  const { user } = useAuth();
  const { data: locationsResponse, isLoading } = useLocationsSlim();

  // Get all locations from API
  const allLocations = locationsResponse?.data || [];

  // Get user's allowed locations from auth context
  const userAllowedLocations: AllowedBusinessLocation[] =
    user?.business?.allowedLocations || [];

  // Filter locations based on user permissions
  // const locations: LocationSlimDto[] =
  //   userAllowedLocations.length > 0
  //     ? allLocations.filter((location: LocationSlimDto) =>
  //         userAllowedLocations.some(
  //           (allowed: AllowedBusinessLocation) => allowed.id === location.id
  //         )
  //       )
  //     : allLocations;

  const locations: LocationSlimDto[] = allLocations as LocationSlimDto[];

  useEffect(() => {
    // If we have locations and none are selected, select the first one by default
    if (locations.length > 0 && selectedLocationIds.length === 0) {
      setSelectedLocationIds([locations[0].id]);
    } else if (locations.length > 0 && selectedLocationIds.length > 0) {
      // Filter out any selected locations that are no longer available
      const validLocationIds = selectedLocationIds.filter((id) =>
        locations.some((loc: LocationSlimDto) => loc.id === id)
      );

      if (validLocationIds.length === 0) {
        // If no valid locations remain, select the first available location
        setSelectedLocationIds([locations[0].id]);
      } else if (validLocationIds.length !== selectedLocationIds.length) {
        // Update to only include valid locations
        setSelectedLocationIds(validLocationIds);
      }
    }
  }, [locations, selectedLocationIds]);

  const selectedLocations = locations.filter((loc: LocationSlimDto) =>
    selectedLocationIds.includes(loc.id)
  );

  const toggleLocation = useCallback(
    (id: string) => {
      // Only toggle location if it's in the allowed list
      const isAllowed = locations.some((loc: LocationSlimDto) => loc.id === id);
      if (!isAllowed) return;

      setSelectedLocationIds((prev) => {
        if (prev.includes(id)) {
          // Don't allow deselecting if it's the last selected location
          if (prev.length === 1) return prev;
          const newSelection = prev.filter((locId) => locId !== id);
          Cookies.set(SELECTED_LOCATIONS_COOKIE, JSON.stringify(newSelection), {
            expires: COOKIE_EXPIRES_DAYS,
          });
          return newSelection;
        } else {
          const newSelection = [...prev, id];
          Cookies.set(SELECTED_LOCATIONS_COOKIE, JSON.stringify(newSelection), {
            expires: COOKIE_EXPIRES_DAYS,
          });
          return newSelection;
        }
      });
    },
    [locations]
  );

  const selectAllLocations = useCallback(() => {
    const allLocationIds = locations.map((loc) => loc.id);
    setSelectedLocationIds(allLocationIds);
    Cookies.set(SELECTED_LOCATIONS_COOKIE, JSON.stringify(allLocationIds), {
      expires: COOKIE_EXPIRES_DAYS,
    });
  }, [locations]);

  const clearAllLocations = useCallback(() => {
    // Keep at least one location selected (the first one)
    if (locations.length > 0) {
      const firstLocationId = [locations[0].id];
      setSelectedLocationIds(firstLocationId);
      Cookies.set(SELECTED_LOCATIONS_COOKIE, JSON.stringify(firstLocationId), {
        expires: COOKIE_EXPIRES_DAYS,
      });
    }
  }, [locations]);

  // Load from cookies on initial mount
  useEffect(() => {
    const savedLocationIds = Cookies.get(SELECTED_LOCATIONS_COOKIE);
    if (savedLocationIds) {
      try {
        const parsedIds = JSON.parse(savedLocationIds);
        if (Array.isArray(parsedIds) && parsedIds.length > 0) {
          // Filter to only include valid location IDs
          const validIds = parsedIds.filter((id) =>
            locations.some((loc: LocationSlimDto) => loc.id === id)
          );
          if (validIds.length > 0) {
            setSelectedLocationIds(validIds);
          }
        }
      } catch (error) {
        console.error("Failed to parse saved location IDs:", error);
        // Clear invalid cookie data
        Cookies.remove(SELECTED_LOCATIONS_COOKIE);
      }
    }
  }, [locations]);

  return (
    <BusinessLocationContext.Provider
      value={{
        locations,
        selectedLocations,
        selectedLocationIds,
        toggleLocation,
        selectAllLocations,
        clearAllLocations,
        isLoading,
      }}
    >
      {children}
    </BusinessLocationContext.Provider>
  );
}

export function useBusinessLocation() {
  const context = useContext(BusinessLocationContext);
  if (context === undefined) {
    throw new Error(
      "useBusinessLocation must be used within a BusinessLocationProvider"
    );
  }
  return context;
}
