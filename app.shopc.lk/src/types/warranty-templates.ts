import { ApiResponse, ApiStatus } from "./common";

export enum WarrantyStatus {
  ACTIVE = "active",
  INACTIVE = "inactive",
}

export enum DurationType {
  DAYS = "days",
  MONTHS = "months",
  YEARS = "years",
  LIFETIME = "lifetime",
}

export enum WarrantyType {
  MANUFACTURER = "manufacturer",
  EXTENDED = "extended",
  SERVICE = "service",
  REPAIR = "repair",
}

export enum CoverageType {
  FULL = "full",
  LIMITED = "limited",
  PARTS_ONLY = "parts_only",
  LABOR_ONLY = "labor_only",
}

// Backend DTO: WarrantyTemplateDto
export interface WarrantyTemplateDto {
  id: string;
  businessId: string;
  templateCode: string;
  templateName: string;
  warrantyType: WarrantyType;
  duration?: number;
  durationType: DurationType;
  coverageType: CoverageType;
  coverageDetails?: string;
  maxClaims?: number;
  termsConditions?: string;
  isTransferable: boolean;
  autoApply: boolean;
  applicableCategories?: string;
  status: WarrantyStatus;
  createdBy: string;
  updatedBy?: string;
  deletedBy?: string;
  deletedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  productsCount?: number;
}

// Backend DTO: CreateWarrantyTemplateDto
export interface CreateWarrantyTemplateDto {
  templateCode: string;
  templateName: string;
  warrantyType: WarrantyType;
  duration?: number;
  durationType: DurationType;
  coverageType: CoverageType;
  coverageDetails?: string;
  maxClaims?: number;
  termsConditions?: string;
  isTransferable?: boolean;
  autoApply?: boolean;
  applicableCategories?: string;
  isActive?: boolean;
  status?: WarrantyStatus;
}

// Backend DTO: UpdateWarrantyTemplateDto
export interface UpdateWarrantyTemplateDto {
  templateCode?: string;
  templateName?: string;
  warrantyType?: WarrantyType;
  duration?: number;
  durationType?: DurationType;
  coverageType?: CoverageType;
  coverageDetails?: string;
  maxClaims?: number;
  termsConditions?: string;
  isTransferable?: boolean;
  autoApply?: boolean;
  applicableCategories?: string;
  status?: WarrantyStatus;
}

// Backend DTO: WarrantyTemplateSlimDto
export interface WarrantyTemplateSlimDto {
  id: string;
  name: string;
}

// Backend DTO: WarrantyTemplateListDto (for table display)
export interface WarrantyTemplateListDto {
  id: string;
  templateCode: string;
  templateName: string;
  warrantyType: WarrantyType;
  duration?: number;
  durationType: DurationType;
  coverageType: CoverageType;
  maxClaims?: number;
  isTransferable: boolean;
  autoApply: boolean;
  status: WarrantyStatus;
  productsCount?: number;
  createdAt: Date;
  updatedAt: Date;
}

// Backend DTO: PaginationMetaDto
export interface PaginationMetaDto {
  total: number;
  page: number;
  totalPages: number;
}

// Backend DTO: PaginatedWarrantyTemplatesResponseDto
export interface PaginatedWarrantyTemplatesResponseDto {
  data: WarrantyTemplateTableData[];
  meta: PaginationMetaDto;
}

// Backend DTO: DeleteWarrantyTemplateResponseDto
export interface DeleteWarrantyTemplateResponseDto {
  success: boolean;
  message: string;
}

// Backend DTO: WarrantyTemplateNameAvailabilityResponseDto
export interface WarrantyTemplateNameAvailabilityResponseDto {
  available: boolean;
}

// Backend DTO: WarrantyTemplateIdResponseDto
export interface WarrantyTemplateIdResponseDto {
  id: string;
}

// Backend DTO: BulkWarrantyTemplateIdsResponseDto
export interface BulkWarrantyTemplateIdsResponseDto {
  ids: string[];
}

// Backend DTO: BulkCreateWarrantyTemplateDto
export interface BulkCreateWarrantyTemplateDto {
  warrantyTemplates: CreateWarrantyTemplateDto[];
}

// Backend DTO: BulkUpdateWarrantyTemplateStatusDto
export interface BulkUpdateWarrantyTemplateStatusDto {
  ids: string[];
  status: WarrantyStatus;
}

// Backend DTO: BulkUpdateWarrantyTemplateStatusResponseDto
export interface BulkUpdateWarrantyTemplateStatusResponseDto {
  updated: number;
  message: string;
}

// Backend DTO: BulkDeleteWarrantyTemplateDto
export interface BulkDeleteWarrantyTemplateDto {
  ids: string[];
}

// Backend DTO: BulkDeleteWarrantyTemplateResponseDto
export interface BulkDeleteWarrantyTemplateResponseDto {
  deleted: number;
  message: string;
}

// Backend DTO: WarrantyTemplateAutocompleteDto
export interface WarrantyTemplateAutocompleteDto {
  id: string;
  templateName: string;
}

// Backend DTO: CheckWarrantyTemplateNameDto
export interface CheckWarrantyTemplateNameDto {
  name: string;
  excludeId?: string;
}

// API Response wrappers
export interface WarrantyTemplateResponse
  extends ApiResponse<WarrantyTemplateDto> {}

export interface WarrantyTemplateTableResponse
  extends ApiResponse<WarrantyTemplateDto[]> {}

export interface WarrantyTemplatePaginatedResponse
  extends ApiResponse<PaginatedWarrantyTemplatesResponseDto | null> {}

export interface SimpleWarrantyTemplateResponse
  extends ApiResponse<WarrantyTemplateSlimDto[]> {}

export interface WarrantyTemplateNameAvailabilityResponse
  extends ApiResponse<WarrantyTemplateNameAvailabilityResponseDto> {}

export interface WarrantyTemplateIdResponse
  extends ApiResponse<WarrantyTemplateIdResponseDto> {}

export interface BulkWarrantyTemplateIdsResponse
  extends ApiResponse<BulkWarrantyTemplateIdsResponseDto> {}

// Table data interface with timestamp fields for sorting and filtering
export interface WarrantyTemplateTableData extends WarrantyTemplateDto {
  createdAt: Date;
  updatedAt: Date;
}

// Legacy aliases for backward compatibility (to be removed gradually)
export type WarrantyTemplate = WarrantyTemplateDto;
export type SimpleWarrantyTemplateData = WarrantyTemplateSlimDto;
export type WarrantyTemplatePaginatedData =
  PaginatedWarrantyTemplatesResponseDto;

// Form values interface for UI components
export interface WarrantyTemplateFormValues {
  templateCode: string;
  templateName: string;
  warrantyType: WarrantyType;
  duration?: number;
  durationType: DurationType;
  coverageType: CoverageType;
  coverageDetails?: string;
  maxClaims?: number;
  termsConditions?: string;
  isTransferable?: boolean;
  autoApply?: boolean;
  applicableCategories?: string;
  status?: WarrantyStatus;
}

// Search and filter interfaces
export interface GetWarrantyTemplatesParams {
  page?: number;
  limit?: number;
  from?: string;
  to?: string;
  templateName?: string;
  templateCode?: string;
  warrantyType?: string;
  status?: string;
  filters?: string;
  joinOperator?: "and" | "or";
  sort?: string;
}

// Advanced filter type for warranty template table
export interface WarrantyTemplateAdvancedFilter {
  id: string;
  value: string;
  operator: "iLike" | "notILike" | "eq" | "ne" | "isEmpty" | "isNotEmpty";
  type: "text" | "select" | "date";
  rowId: string;
}

// Sort configuration for warranty template table
export interface WarrantyTemplateSortConfig {
  id: string;
  desc: boolean;
}

// Duration display helper interface
export interface WarrantyTemplateDurationDisplay {
  value: number | null;
  unit: string;
  display: string;
}

// Coverage display helper interface
export interface WarrantyTemplateCoverageDisplay {
  type: CoverageType;
  details?: string;
  display: string;
}
