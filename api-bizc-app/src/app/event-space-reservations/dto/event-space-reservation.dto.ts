import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  EventReservationStatus,
  EventType,
  DiscountType,
  TaxType,
  ReservationSource,
} from '../../drizzle/schema/event-space-reservations.schema';
import { TaskStatus, TaskPriority } from '../../drizzle/schema/tasks.schema';
import { PaymentStatus } from '@app/shared/types/common.enum';

export class EventSpaceReservationAssetDto {
  @ApiProperty({ description: 'Asset assignment ID' })
  id: string;

  @ApiProperty({ description: 'Asset ID' })
  assetId: string;

  @ApiProperty({ description: 'Asset name' })
  assetName: string;

  @ApiPropertyOptional({ description: 'Assignment notes' })
  notes?: string;

  @ApiProperty({ description: 'Assigned by staff member ID' })
  assignedById: string;

  @ApiProperty({ description: 'Assigned by staff member name' })
  assignedByName: string;

  @ApiProperty({ description: 'Assignment timestamp' })
  assignedAt: Date;

  @ApiPropertyOptional({ description: 'Return timestamp' })
  returnedAt?: Date;

  @ApiPropertyOptional({ description: 'Condition when assigned' })
  conditionOnAssignment?: string;

  @ApiPropertyOptional({ description: 'Condition when returned' })
  conditionOnReturn?: string;
}

export class EventSpaceReservationTaskDto {
  @ApiProperty({ description: 'Task assignment ID' })
  id: string;

  @ApiProperty({ description: 'Staff member ID' })
  staffMemberId: string;

  @ApiProperty({ description: 'Staff member name' })
  staffMemberName: string;

  @ApiProperty({ description: 'Task ID' })
  taskId: string;

  @ApiProperty({ description: 'Task title' })
  taskTitle: string;

  @ApiPropertyOptional({ description: 'Task description' })
  taskDescription?: string;

  @ApiPropertyOptional({ description: 'Task due date' })
  taskDueDate?: Date;

  @ApiProperty({
    enum: TaskPriority,
    enumName: 'TaskPriority',
    description: 'Task priority',
  })
  taskPriority: TaskPriority;

  @ApiProperty({
    enum: TaskStatus,
    enumName: 'TaskStatus',
    description: 'Task status',
  })
  taskStatus: TaskStatus;

  @ApiPropertyOptional({ description: 'Task assigned to staff member ID' })
  taskAssignedTo?: string;

  @ApiPropertyOptional({ description: 'Task assigned to staff member name' })
  taskAssignedToName?: string;
}

export class EventSpaceReservationDto {
  @ApiProperty({ description: 'Unique identifier for the reservation' })
  id: string;

  @ApiProperty({ description: 'Business ID the reservation belongs to' })
  businessId: string;

  @ApiProperty({ description: 'Reservation number' })
  reservationNumber: string;

  @ApiPropertyOptional({ description: 'External booking reference' })
  referenceNumber?: string;

  @ApiProperty({ description: 'Event space ID' })
  eventSpaceId: string;

  @ApiProperty({ description: 'Event space name' })
  eventSpaceName: string;

  @ApiPropertyOptional({ description: 'Customer ID' })
  customerId?: string;

  @ApiPropertyOptional({ description: 'Customer name' })
  customerName?: string;

  @ApiProperty({ description: 'Event name' })
  eventName: string;

  @ApiProperty({
    enum: EventType,
    enumName: 'EventType',
    description: 'Type of event',
  })
  eventType: EventType;

  @ApiPropertyOptional({ description: 'Event description' })
  eventDescription?: string;

  @ApiProperty({ description: 'Event start time' })
  eventStartTime: Date;

  @ApiProperty({ description: 'Event end time' })
  eventEndTime: Date;

  @ApiProperty({ description: 'Setup start time' })
  setupStartTime: Date;

  @ApiProperty({ description: 'Breakdown end time' })
  breakdownEndTime: Date;

  @ApiPropertyOptional({ description: 'Actual setup start time' })
  actualSetupStartTime?: Date;

  @ApiPropertyOptional({ description: 'Actual event start time' })
  actualEventStartTime?: Date;

  @ApiPropertyOptional({ description: 'Actual event end time' })
  actualEventEndTime?: Date;

  @ApiPropertyOptional({ description: 'Actual breakdown end time' })
  actualBreakdownEndTime?: Date;

  @ApiProperty({ description: 'Expected guest count' })
  expectedGuestCount: number;

  @ApiProperty({ description: 'Confirmed guest count' })
  confirmedGuestCount: number;

  @ApiProperty({ description: 'Whether individual guest tracking is required' })
  requiresIndividualGuestTracking: boolean;

  @ApiPropertyOptional({ description: 'Guest list deadline' })
  guestListDeadline?: Date;

  @ApiProperty({ description: 'Whether walk-ins are allowed' })
  allowWalkIns: boolean;

  @ApiProperty({
    enum: EventReservationStatus,
    enumName: 'EventReservationStatus',
    description: 'Reservation status',
  })
  status: EventReservationStatus;

  @ApiPropertyOptional({
    enum: ReservationSource,
    enumName: 'ReservationSource',
    description: 'Source of the reservation',
  })
  reservationSource?: ReservationSource;

  @ApiProperty({
    enum: PaymentStatus,
    enumName: 'PaymentStatus',
    description: 'Payment status',
  })
  paymentStatus: PaymentStatus;

  @ApiProperty({ description: 'Subtotal amount' })
  subtotal: string;

  @ApiProperty({ description: 'Total amount' })
  total: string;

  @ApiProperty({ description: 'Deposit required' })
  depositRequired: string;

  @ApiProperty({ description: 'Deposit paid' })
  depositPaid: string;

  @ApiProperty({ description: 'Balance due' })
  balanceDue: string;

  @ApiPropertyOptional({
    enum: DiscountType,
    enumName: 'DiscountType',
    description: 'Type of discount',
  })
  discountType?: DiscountType;

  @ApiProperty({ description: 'Discount value' })
  discountValue: string;

  @ApiProperty({ description: 'Discount amount' })
  discountAmount: string;

  @ApiPropertyOptional({ description: 'Reason for discount' })
  discountReason?: string;

  @ApiProperty({
    enum: TaxType,
    enumName: 'TaxType',
    description: 'Tax type',
  })
  taxType: TaxType;

  @ApiPropertyOptional({ description: 'Default tax rate ID' })
  defaultTaxRateId?: string;

  @ApiProperty({ description: 'Tax amount' })
  taxAmount: string;

  @ApiPropertyOptional({ description: 'Additional notes' })
  notes?: string;

  @ApiPropertyOptional({ description: 'Cancellation reason' })
  cancellationReason?: string;

  @ApiPropertyOptional({ description: 'Cancellation date' })
  cancellationDate?: Date;

  @ApiProperty({ description: 'Whether confirmation has been sent' })
  confirmationSent: boolean;

  @ApiPropertyOptional({ description: 'When confirmation was sent' })
  confirmationSentAt?: Date;

  @ApiProperty({ description: 'Whether reminder has been sent' })
  reminderSent: boolean;

  @ApiPropertyOptional({ description: 'When reminder was sent' })
  reminderSentAt?: Date;

  @ApiProperty({
    description: 'Asset assignments for this reservation',
    type: [EventSpaceReservationAssetDto],
  })
  assets: EventSpaceReservationAssetDto[];

  @ApiProperty({
    description: 'Task assignments for this reservation',
    type: [EventSpaceReservationTaskDto],
  })
  tasks: EventSpaceReservationTaskDto[];

  @ApiProperty({
    example: 'John Doe',
    description: 'Name of the user who created the reservation',
  })
  createdBy: string;

  @ApiPropertyOptional({
    example: 'Jane Smith',
    description: 'Name of the user who last updated the reservation',
  })
  updatedBy?: string;

  @ApiProperty({
    example: '2023-01-01T00:00:00Z',
    description: 'Creation timestamp',
  })
  createdAt: Date;

  @ApiProperty({
    example: '2023-01-01T00:00:00Z',
    description: 'Last update timestamp',
  })
  updatedAt: Date;
}
