import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  EventReservationStatus,
  EventType,
} from '../../drizzle/schema/event-space-reservations.schema';
import { PaymentStatus } from '@app/shared/types/common.enum';

export class EventSpaceReservationListDto {
  @ApiProperty({ description: 'Unique identifier for the reservation' })
  id: string;

  @ApiProperty({ description: 'Reservation number' })
  reservationNumber: string;

  @ApiPropertyOptional({ description: 'External booking reference' })
  referenceNumber?: string;

  @ApiProperty({ description: 'Event space name' })
  eventSpaceName: string;

  @ApiPropertyOptional({ description: 'Customer name' })
  customerName?: string;

  @ApiProperty({ description: 'Event name' })
  eventName: string;

  @ApiProperty({
    enum: EventType,
    enumName: 'EventType',
    description: 'Type of event',
  })
  eventType: EventType;

  @ApiProperty({ description: 'Event start time' })
  eventStartTime: Date;

  @ApiProperty({ description: 'Event end time' })
  eventEndTime: Date;

  @ApiProperty({ description: 'Expected guest count' })
  expectedGuestCount: number;

  @ApiProperty({
    enum: EventReservationStatus,
    enumName: 'EventReservationStatus',
    description: 'Reservation status',
  })
  status: EventReservationStatus;

  @ApiProperty({
    enum: PaymentStatus,
    enumName: 'PaymentStatus',
    description: 'Payment status',
  })
  paymentStatus: PaymentStatus;

  @ApiProperty({ description: 'Total amount' })
  total: string;

  @ApiProperty({ description: 'Number of assigned assets' })
  assetsCount: number;

  @ApiProperty({ description: 'Number of assigned tasks' })
  tasksCount: number;

  @ApiProperty({
    example: 'John Doe',
    description: 'Name of the user who created the reservation',
  })
  createdBy: string;

  @ApiPropertyOptional({
    example: 'Jane Smith',
    description: 'Name of the user who last updated the reservation',
  })
  updatedBy?: string;

  @ApiProperty({
    example: '2023-01-01T00:00:00Z',
    description: 'Creation timestamp',
  })
  createdAt: Date;

  @ApiProperty({
    example: '2023-01-01T00:00:00Z',
    description: 'Last update timestamp',
  })
  updatedAt: Date;
}
