import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
  IsUUID,
  IsBoolean,
  IsEnum,
  Min,
  IsArray,
  IsDateString,
  IsDecimal,
  IsInt,
} from 'class-validator';
import { Transform } from 'class-transformer';
import {
  EventReservationStatus,
  EventType,
  DiscountType,
  TaxType,
  ReservationSource,
} from '../../drizzle/schema/event-space-reservations.schema';
import { TaskStatus, TaskPriority } from '../../drizzle/schema/tasks.schema';
import { PaymentStatus } from '@app/shared/types/common.enum';

export class CreateEventSpaceReservationDto {
  @ApiProperty({ description: 'Reservation number', maxLength: 191 })
  @IsNotEmpty()
  @IsString()
  @MaxLength(191)
  reservationNumber: string;

  @ApiPropertyOptional({
    description: 'External booking reference',
    maxLength: 191,
  })
  @IsOptional()
  @IsString()
  @MaxLength(191)
  referenceNumber?: string;

  @ApiProperty({
    description: 'Event space ID',
  })
  @IsNotEmpty()
  @IsUUID()
  eventSpaceId: string;

  @ApiPropertyOptional({
    description: 'Customer ID',
  })
  @IsOptional()
  @IsUUID()
  customerId?: string;

  @ApiProperty({ description: 'Event name', maxLength: 191 })
  @IsNotEmpty()
  @IsString()
  @MaxLength(191)
  eventName: string;

  @ApiProperty({
    enum: EventType,
    enumName: 'EventType',
    description: 'Type of event',
  })
  @IsEnum(EventType)
  eventType: EventType;

  @ApiPropertyOptional({
    description: 'Event description',
  })
  @IsOptional()
  @IsString()
  eventDescription?: string;

  @ApiProperty({
    description: 'Event start time',
    example: '2024-12-25T18:00:00Z',
  })
  @IsNotEmpty()
  @IsDateString()
  eventStartTime: string;

  @ApiProperty({
    description: 'Event end time',
    example: '2024-12-25T23:00:00Z',
  })
  @IsNotEmpty()
  @IsDateString()
  eventEndTime: string;

  @ApiProperty({
    description: 'Setup start time',
    example: '2024-12-25T16:00:00Z',
  })
  @IsNotEmpty()
  @IsDateString()
  setupStartTime: string;

  @ApiProperty({
    description: 'Breakdown end time',
    example: '2024-12-26T01:00:00Z',
  })
  @IsNotEmpty()
  @IsDateString()
  breakdownEndTime: string;

  @ApiPropertyOptional({
    description: 'Actual setup start time',
    example: '2024-12-25T16:15:00Z',
  })
  @IsOptional()
  @IsDateString()
  actualSetupStartTime?: string;

  @ApiPropertyOptional({
    description: 'Actual event start time',
    example: '2024-12-25T18:05:00Z',
  })
  @IsOptional()
  @IsDateString()
  actualEventStartTime?: string;

  @ApiPropertyOptional({
    description: 'Actual event end time',
    example: '2024-12-25T23:10:00Z',
  })
  @IsOptional()
  @IsDateString()
  actualEventEndTime?: string;

  @ApiPropertyOptional({
    description: 'Actual breakdown end time',
    example: '2024-12-26T01:30:00Z',
  })
  @IsOptional()
  @IsDateString()
  actualBreakdownEndTime?: string;

  @ApiProperty({
    description: 'Expected guest count',
    example: 150,
  })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  expectedGuestCount: number;

  @ApiPropertyOptional({
    description: 'Confirmed guest count',
    example: 145,
    default: 0,
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  confirmedGuestCount?: number;

  @ApiPropertyOptional({
    description: 'Whether individual guest tracking is required',
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  requiresIndividualGuestTracking?: boolean;

  @ApiPropertyOptional({
    description: 'Guest list deadline',
    example: '2024-12-20T23:59:59Z',
  })
  @IsOptional()
  @IsDateString()
  guestListDeadline?: string;

  @ApiPropertyOptional({
    description: 'Whether walk-ins are allowed',
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  allowWalkIns?: boolean;

  @ApiPropertyOptional({
    enum: EventReservationStatus,
    enumName: 'EventReservationStatus',
    description: 'Reservation status',
    default: EventReservationStatus.INQUIRY,
  })
  @IsOptional()
  @IsEnum(EventReservationStatus)
  status?: EventReservationStatus;

  @ApiPropertyOptional({
    enum: ReservationSource,
    enumName: 'ReservationSource',
    description: 'Source of the reservation',
  })
  @IsOptional()
  @IsEnum(ReservationSource)
  reservationSource?: ReservationSource;

  @ApiPropertyOptional({
    enum: PaymentStatus,
    enumName: 'PaymentStatus',
    description: 'Payment status',
    default: PaymentStatus.PENDING,
  })
  @IsOptional()
  @IsEnum(PaymentStatus)
  paymentStatus?: PaymentStatus;

  @ApiPropertyOptional({
    description: 'Subtotal amount',
    example: '1500.00',
    default: '0.00',
  })
  @IsOptional()
  @IsDecimal({ decimal_digits: '2' })
  subtotal?: string;

  @ApiPropertyOptional({
    description: 'Total amount',
    example: '1725.00',
    default: '0.00',
  })
  @IsOptional()
  @IsDecimal({ decimal_digits: '2' })
  total?: string;

  @ApiPropertyOptional({
    description: 'Deposit required',
    example: '500.00',
    default: '0.00',
  })
  @IsOptional()
  @IsDecimal({ decimal_digits: '2' })
  depositRequired?: string;

  @ApiPropertyOptional({
    description: 'Deposit paid',
    example: '500.00',
    default: '0.00',
  })
  @IsOptional()
  @IsDecimal({ decimal_digits: '2' })
  depositPaid?: string;

  @ApiPropertyOptional({
    description: 'Balance due',
    example: '1225.00',
    default: '0.00',
  })
  @IsOptional()
  @IsDecimal({ decimal_digits: '2' })
  balanceDue?: string;

  @ApiPropertyOptional({
    enum: DiscountType,
    enumName: 'DiscountType',
    description: 'Type of discount',
  })
  @IsOptional()
  @IsEnum(DiscountType)
  discountType?: DiscountType;

  @ApiPropertyOptional({
    description: 'Discount value (percentage or fixed amount)',
    example: '10.00',
    default: '0.00',
  })
  @IsOptional()
  @IsDecimal({ decimal_digits: '2' })
  discountValue?: string;

  @ApiPropertyOptional({
    description: 'Calculated discount amount',
    example: '150.00',
    default: '0.00',
  })
  @IsOptional()
  @IsDecimal({ decimal_digits: '2' })
  discountAmount?: string;

  @ApiPropertyOptional({
    description: 'Reason for discount',
  })
  @IsOptional()
  @IsString()
  discountReason?: string;

  @ApiPropertyOptional({
    enum: TaxType,
    enumName: 'TaxType',
    description: 'Tax type',
    default: TaxType.INCLUSIVE,
  })
  @IsOptional()
  @IsEnum(TaxType)
  taxType?: TaxType;

  @ApiPropertyOptional({
    description: 'Default tax rate ID',
  })
  @IsOptional()
  @IsUUID()
  defaultTaxRateId?: string;

  @ApiPropertyOptional({
    description: 'Tax amount',
    example: '225.00',
    default: '0.00',
  })
  @IsOptional()
  @IsDecimal({ decimal_digits: '2' })
  taxAmount?: string;

  @ApiPropertyOptional({
    description: 'Additional notes',
  })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiPropertyOptional({
    description: 'Cancellation reason',
  })
  @IsOptional()
  @IsString()
  cancellationReason?: string;

  @ApiPropertyOptional({
    description: 'Cancellation date',
    example: '2024-12-20T10:00:00Z',
  })
  @IsOptional()
  @IsDateString()
  cancellationDate?: string;

  @ApiPropertyOptional({
    description: 'Whether confirmation has been sent',
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  confirmationSent?: boolean;

  @ApiPropertyOptional({
    description: 'When confirmation was sent',
    example: '2024-12-15T14:30:00Z',
  })
  @IsOptional()
  @IsDateString()
  confirmationSentAt?: string;

  @ApiPropertyOptional({
    description: 'Whether reminder has been sent',
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  reminderSent?: boolean;

  @ApiPropertyOptional({
    description: 'When reminder was sent',
    example: '2024-12-23T10:00:00Z',
  })
  @IsOptional()
  @IsDateString()
  reminderSentAt?: string;

  // Asset assignments
  @ApiPropertyOptional({
    description: 'Array of asset IDs to assign to this reservation',
    type: [String],
    example: [
      '123e4567-e89b-12d3-a456-************',
      '123e4567-e89b-12d3-a456-************',
    ],
  })
  @IsOptional()
  @IsArray()
  @IsUUID('4', { each: true })
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      try {
        const parsed = JSON.parse(value);
        return Array.isArray(parsed) ? parsed : [value];
      } catch {
        return [value];
      }
    }
    if (Array.isArray(value)) {
      return value;
    }
    return value;
  })
  assetIds?: string[];

  // Task creation and assignment
  @ApiPropertyOptional({
    description:
      'Array of tasks to create or link for this event space reservation',
    type: Array,
    example: [
      {
        // Create new task
        title: 'Setup sound system',
        description: 'Install and test sound equipment for the event',
        dueDate: '2024-12-25',
        priority: 'HIGH',
        status: 'PENDING',
        assignedTo: '123e4567-e89b-12d3-a456-************',
      },
      {
        // Link existing task to staff member
        taskId: '123e4567-e89b-12d3-a456-************',
        staffMemberId: '123e4567-e89b-12d3-a456-************',
      },
    ],
  })
  @IsOptional()
  @IsArray()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      try {
        return JSON.parse(value);
      } catch {
        return [];
      }
    }
    return value;
  })
  tasks?: Array<{
    // For creating new tasks
    title?: string;
    description?: string;
    dueDate?: string;
    priority?: TaskPriority;
    status?: TaskStatus;
    assignedTo?: string;
    // For linking existing tasks
    taskId?: string;
    staffMemberId?: string;
  }>;
}
