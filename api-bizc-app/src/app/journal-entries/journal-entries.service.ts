import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateJournalEntryDto } from './dto/create-journal-entry.dto';
import { UpdateJournalEntryDto } from './dto/update-journal-entry.dto';
import { JournalEntryDto } from './dto/journal-entry.dto';
import { JournalEntrySlimDto } from './dto/journal-entry-slim.dto';
import { JournalEntryListDto } from './dto/journal-entry-list.dto';
import {
  journalEntries,
  journalEntryLines,
  JournalEntryStatus,
} from '../drizzle/schema/journal-entries.schema';
import { users } from '../drizzle/schema/users.schema';
import { accounts } from '../drizzle/schema/accounts.schema';
import { taxes } from '../drizzle/schema/taxes.schema';
import {
  and,
  eq,
  isNull,
  desc,
  asc,
  count,
  inArray,
  like,
  or,
  gte,
  lte,
  sql,
} from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { ActivityLogName } from '../shared/types';

@Injectable()
export class JournalEntriesService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private activityLogService: ActivityLogService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createJournalEntryDto: CreateJournalEntryDto,
  ): Promise<JournalEntryDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Validate that debits equal credits
      const totalDebits = createJournalEntryDto.journalEntryLines.reduce(
        (sum, line) => sum + parseFloat(line.debitAmount),
        0,
      );
      const totalCredits = createJournalEntryDto.journalEntryLines.reduce(
        (sum, line) => sum + parseFloat(line.creditAmount),
        0,
      );

      if (Math.abs(totalDebits - totalCredits) > 0.01) {
        throw new BadRequestException(
          'Journal entry is not balanced: total debits must equal total credits',
        );
      }

      // Check if journal number already exists
      const existingJournalEntry = await this.db
        .select()
        .from(journalEntries)
        .where(
          and(
            eq(journalEntries.businessId, businessId),
            eq(
              journalEntries.journalNumber,
              createJournalEntryDto.journalNumber,
            ),
            isNull(journalEntries.deletedAt),
          ),
        )
        .limit(1);

      if (existingJournalEntry.length > 0) {
        throw new ConflictException(
          `Journal number '${createJournalEntryDto.journalNumber}' already exists`,
        );
      }

      // Validate that all accounts exist
      const accountIds = createJournalEntryDto.journalEntryLines.map(
        (line) => line.accountId,
      );
      const existingAccounts = await this.db
        .select({ id: accounts.id })
        .from(accounts)
        .where(
          and(
            inArray(accounts.id, accountIds),
            eq(accounts.businessId, businessId),
            isNull(accounts.deletedAt),
          ),
        );

      if (existingAccounts.length !== accountIds.length) {
        throw new BadRequestException('One or more accounts do not exist');
      }

      // Create journal entry and lines in a transaction
      const result = await this.db.transaction(async (tx) => {
        // Create journal entry
        const [journalEntry] = await tx
          .insert(journalEntries)
          .values({
            businessId,
            journalNumber: createJournalEntryDto.journalNumber,
            journalDate: createJournalEntryDto.journalDate,
            description: createJournalEntryDto.description,
            memo: createJournalEntryDto.memo,
            referenceType: createJournalEntryDto.referenceType,
            referenceId: createJournalEntryDto.referenceId,
            status: createJournalEntryDto.status || JournalEntryStatus.DRAFT,
            createdBy: userId,
            updatedBy: userId,
          })
          .returning();

        // Create journal entry lines
        const lineValues = createJournalEntryDto.journalEntryLines.map(
          (line) => ({
            journalId: journalEntry.id,
            lineNumber: line.lineNumber,
            sortOrder: line.sortOrder,
            accountId: line.accountId,
            debitAmount: line.debitAmount,
            creditAmount: line.creditAmount,
            description: line.description,
            nameReference: line.nameReference,
            taxId: line.taxId,
            entityType: line.entityType,
            entityId: line.entityId,
            createdBy: userId,
            updatedBy: userId,
          }),
        );

        await tx.insert(journalEntryLines).values(lineValues);

        return journalEntry;
      });

      // Log activity
      await this.activityLogService.log(
        ActivityLogName.CREATE,
        `Created journal entry: ${createJournalEntryDto.journalNumber}`,
        { id: result.id, type: 'journal_entry' },
        { id: userId, type: 'user' },
        { businessId },
      );

      return this.findOne(userId, businessId, result.id);
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create journal entry: ${error.message}`,
      );
    }
  }

  async findOne(
    _userId: string,
    businessId: string | null,
    id: string,
  ): Promise<JournalEntryDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const journalEntryResult = await this.db
        .select({
          id: journalEntries.id,
          businessId: journalEntries.businessId,
          journalNumber: journalEntries.journalNumber,
          journalDate: journalEntries.journalDate,
          description: journalEntries.description,
          memo: journalEntries.memo,
          referenceType: journalEntries.referenceType,
          referenceId: journalEntries.referenceId,
          status: journalEntries.status,
          createdBy: sql<string>`CONCAT(${users.firstName}, ' ', ${users.lastName})`,
          updatedBy: sql<string>`CASE
            WHEN ${journalEntries.updatedBy} IS NOT NULL
            THEN CONCAT(updatedByUser.first_name, ' ', updatedByUser.last_name)
            ELSE NULL
          END`,
          createdAt: journalEntries.createdAt,
          updatedAt: journalEntries.updatedAt,
        })
        .from(journalEntries)
        .leftJoin(users, eq(journalEntries.createdBy, users.id))
        .leftJoin(
          sql`${users} AS updatedByUser`,
          eq(journalEntries.updatedBy, sql`updatedByUser.id`),
        )
        .where(
          and(
            eq(journalEntries.id, id),
            eq(journalEntries.businessId, businessId),
            isNull(journalEntries.deletedAt),
          ),
        )
        .limit(1);

      if (journalEntryResult.length === 0) {
        throw new NotFoundException('Journal entry not found');
      }

      const journalEntry = journalEntryResult[0];

      // Get journal entry lines with account and tax information
      const lines = await this.db
        .select({
          id: journalEntryLines.id,
          journalId: journalEntryLines.journalId,
          lineNumber: journalEntryLines.lineNumber,
          sortOrder: journalEntryLines.sortOrder,
          accountId: journalEntryLines.accountId,
          accountName: accounts.accountName,
          accountCode: accounts.accountNumber,
          debitAmount: journalEntryLines.debitAmount,
          creditAmount: journalEntryLines.creditAmount,
          description: journalEntryLines.description,
          nameReference: journalEntryLines.nameReference,
          taxId: journalEntryLines.taxId,
          taxName: taxes.taxName,
          entityType: journalEntryLines.entityType,
          entityId: journalEntryLines.entityId,
          createdBy: sql<string>`CONCAT(lineCreatedBy.first_name, ' ', lineCreatedBy.last_name)`,
          updatedBy: sql<string>`CASE
            WHEN ${journalEntryLines.updatedBy} IS NOT NULL
            THEN CONCAT(lineUpdatedBy.first_name, ' ', lineUpdatedBy.last_name)
            ELSE NULL
          END`,
          createdAt: journalEntryLines.createdAt,
          updatedAt: journalEntryLines.updatedAt,
        })
        .from(journalEntryLines)
        .leftJoin(accounts, eq(journalEntryLines.accountId, accounts.id))
        .leftJoin(taxes, eq(journalEntryLines.taxId, taxes.id))
        .leftJoin(
          sql`${users} AS lineCreatedBy`,
          eq(journalEntryLines.createdBy, sql`lineCreatedBy.id`),
        )
        .leftJoin(
          sql`${users} AS lineUpdatedBy`,
          eq(journalEntryLines.updatedBy, sql`lineUpdatedBy.id`),
        )
        .where(
          and(
            eq(journalEntryLines.journalId, journalEntry.id),
            isNull(journalEntryLines.deletedAt),
          ),
        )
        .orderBy(asc(journalEntryLines.sortOrder));

      // Calculate totals
      const totalDebitAmount = lines
        .reduce((sum, line) => sum + parseFloat(line.debitAmount), 0)
        .toFixed(2);
      const totalCreditAmount = lines
        .reduce((sum, line) => sum + parseFloat(line.creditAmount), 0)
        .toFixed(2);

      return {
        ...journalEntry,
        journalEntryLines: lines,
        totalDebitAmount,
        totalCreditAmount,
        isBalanced:
          Math.abs(
            parseFloat(totalDebitAmount) - parseFloat(totalCreditAmount),
          ) < 0.01,
        createdAt: journalEntry.createdAt.toISOString(),
        updatedAt: journalEntry.updatedAt.toISOString(),
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to retrieve journal entry: ${error.message}`,
      );
    }
  }

  async findAllOptimized(
    _userId: string,
    businessId: string | null,
    page?: number,
    limit?: number,
    from?: string,
    to?: string,
    journalNumber?: string,
    description?: string,
    status?: string,
    referenceType?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{ data: JournalEntryListDto[]; meta: any }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const pageNumber = page || 1;
      const pageSize = limit || 10;
      const offset = (pageNumber - 1) * pageSize;

      // Build where conditions
      const conditions = [
        eq(journalEntries.businessId, businessId),
        isNull(journalEntries.deletedAt),
      ];

      // Date range filters
      if (from) {
        conditions.push(gte(journalEntries.journalDate, from));
      }
      if (to) {
        conditions.push(lte(journalEntries.journalDate, to));
      }

      // Text filters
      if (journalNumber) {
        conditions.push(
          like(journalEntries.journalNumber, `%${journalNumber}%`),
        );
      }
      if (description) {
        conditions.push(like(journalEntries.description, `%${description}%`));
      }
      if (status) {
        conditions.push(eq(journalEntries.status, status as any));
      }
      if (referenceType) {
        conditions.push(eq(journalEntries.referenceType, referenceType as any));
      }

      // Advanced filters
      if (filters) {
        try {
          const parsedFilters = JSON.parse(filters);
          const filterConditions = [];

          for (const filter of parsedFilters) {
            const { field, operator, value } = filter;

            switch (field) {
              case 'journalNumber':
                if (operator === 'contains') {
                  filterConditions.push(
                    like(journalEntries.journalNumber, `%${value}%`),
                  );
                } else if (operator === 'equals') {
                  filterConditions.push(
                    eq(journalEntries.journalNumber, value),
                  );
                }
                break;
              case 'description':
                if (operator === 'contains') {
                  filterConditions.push(
                    like(journalEntries.description, `%${value}%`),
                  );
                }
                break;
              case 'status':
                filterConditions.push(eq(journalEntries.status, value));
                break;
              case 'referenceType':
                filterConditions.push(eq(journalEntries.referenceType, value));
                break;
            }
          }

          if (filterConditions.length > 0) {
            if (joinOperator === 'or') {
              conditions.push(or(...filterConditions));
            } else {
              conditions.push(...filterConditions);
            }
          }
        } catch {
          // Invalid JSON filters, ignore
        }
      }

      // Build sort order
      let orderBy: any;
      if (sort) {
        const [field, direction] = sort.split(':');
        const isDesc = direction === 'desc';

        switch (field) {
          case 'journalNumber':
            orderBy = isDesc
              ? desc(journalEntries.journalNumber)
              : asc(journalEntries.journalNumber);
            break;
          case 'journalDate':
            orderBy = isDesc
              ? desc(journalEntries.journalDate)
              : asc(journalEntries.journalDate);
            break;
          case 'description':
            orderBy = isDesc
              ? desc(journalEntries.description)
              : asc(journalEntries.description);
            break;
          case 'status':
            orderBy = isDesc
              ? desc(journalEntries.status)
              : asc(journalEntries.status);
            break;
          case 'createdAt':
            orderBy = isDesc
              ? desc(journalEntries.createdAt)
              : asc(journalEntries.createdAt);
            break;
          default:
            orderBy = desc(journalEntries.createdAt);
        }
      } else {
        orderBy = desc(journalEntries.createdAt);
      }

      // Get total count
      const [{ totalCount }] = await this.db
        .select({ totalCount: count() })
        .from(journalEntries)
        .where(and(...conditions));

      // Get journal entries with aggregated line data
      const journalEntriesData = await this.db
        .select({
          id: journalEntries.id,
          journalNumber: journalEntries.journalNumber,
          journalDate: journalEntries.journalDate,
          description: journalEntries.description,
          memo: journalEntries.memo,
          referenceType: journalEntries.referenceType,
          referenceId: journalEntries.referenceId,
          status: journalEntries.status,
          createdBy: sql<string>`CONCAT(${users.firstName}, ' ', ${users.lastName})`,
          updatedBy: sql<string>`CASE
            WHEN ${journalEntries.updatedBy} IS NOT NULL
            THEN CONCAT(updatedByUser.first_name, ' ', updatedByUser.last_name)
            ELSE NULL
          END`,
          createdAt: journalEntries.createdAt,
          updatedAt: journalEntries.updatedAt,
          totalDebitAmount: sql<string>`COALESCE(SUM(${journalEntryLines.debitAmount}), 0)`,
          totalCreditAmount: sql<string>`COALESCE(SUM(${journalEntryLines.creditAmount}), 0)`,
          linesCount: sql<number>`COUNT(${journalEntryLines.id})`,
        })
        .from(journalEntries)
        .leftJoin(users, eq(journalEntries.createdBy, users.id))
        .leftJoin(
          sql`${users} AS updatedByUser`,
          eq(journalEntries.updatedBy, sql`updatedByUser.id`),
        )
        .leftJoin(
          journalEntryLines,
          and(
            eq(journalEntryLines.journalId, journalEntries.id),
            isNull(journalEntryLines.deletedAt),
          ),
        )
        .where(and(...conditions))
        .groupBy(
          journalEntries.id,
          journalEntries.journalNumber,
          journalEntries.journalDate,
          journalEntries.description,
          journalEntries.memo,
          journalEntries.referenceType,
          journalEntries.referenceId,
          journalEntries.status,
          journalEntries.createdBy,
          journalEntries.updatedBy,
          journalEntries.createdAt,
          journalEntries.updatedAt,
          users.firstName,
          users.lastName,
          sql`updatedByUser.first_name`,
          sql`updatedByUser.last_name`,
        )
        .orderBy(orderBy)
        .limit(pageSize)
        .offset(offset);

      const data = journalEntriesData.map((entry) => ({
        ...entry,
        isBalanced:
          Math.abs(
            parseFloat(entry.totalDebitAmount) -
              parseFloat(entry.totalCreditAmount),
          ) < 0.01,
        createdAt: entry.createdAt.toISOString(),
        updatedAt: entry.updatedAt.toISOString(),
      }));

      const totalPages = Math.ceil(totalCount / pageSize);

      return {
        data,
        meta: {
          currentPage: pageNumber,
          totalPages,
          pageSize,
          totalCount,
          hasNextPage: pageNumber < totalPages,
          hasPreviousPage: pageNumber > 1,
        },
      };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to retrieve journal entries: ${error.message}`,
      );
    }
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateJournalEntryDto: UpdateJournalEntryDto,
  ): Promise<JournalEntryDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if journal entry exists
      const existingJournalEntry = await this.db
        .select()
        .from(journalEntries)
        .where(
          and(
            eq(journalEntries.id, id),
            eq(journalEntries.businessId, businessId),
            isNull(journalEntries.deletedAt),
          ),
        )
        .limit(1);

      if (existingJournalEntry.length === 0) {
        throw new NotFoundException('Journal entry not found');
      }

      // If journal number is being updated, check for conflicts
      if (
        updateJournalEntryDto.journalNumber &&
        updateJournalEntryDto.journalNumber !==
          existingJournalEntry[0].journalNumber
      ) {
        const conflictingEntry = await this.db
          .select()
          .from(journalEntries)
          .where(
            and(
              eq(journalEntries.businessId, businessId),
              eq(
                journalEntries.journalNumber,
                updateJournalEntryDto.journalNumber,
              ),
              isNull(journalEntries.deletedAt),
            ),
          )
          .limit(1);

        if (conflictingEntry.length > 0) {
          throw new ConflictException(
            `Journal number '${updateJournalEntryDto.journalNumber}' already exists`,
          );
        }
      }

      // If journal entry lines are provided, validate balance
      if (updateJournalEntryDto.journalEntryLines) {
        const totalDebits = updateJournalEntryDto.journalEntryLines.reduce(
          (sum: number, line: any) => sum + parseFloat(line.debitAmount || '0'),
          0,
        );
        const totalCredits = updateJournalEntryDto.journalEntryLines.reduce(
          (sum: number, line: any) =>
            sum + parseFloat(line.creditAmount || '0'),
          0,
        );

        if (Math.abs(totalDebits - totalCredits) > 0.01) {
          throw new BadRequestException(
            'Journal entry is not balanced: total debits must equal total credits',
          );
        }

        // Validate that all accounts exist
        const accountIds = updateJournalEntryDto.journalEntryLines
          .map((line: any) => line.accountId)
          .filter(Boolean);

        if (accountIds.length > 0) {
          const existingAccounts = await this.db
            .select({ id: accounts.id })
            .from(accounts)
            .where(
              and(
                inArray(accounts.id, accountIds),
                eq(accounts.businessId, businessId),
                isNull(accounts.deletedAt),
              ),
            );

          if (existingAccounts.length !== accountIds.length) {
            throw new BadRequestException('One or more accounts do not exist');
          }
        }
      }

      // Update journal entry and lines in a transaction
      await this.db.transaction(async (tx) => {
        // Update journal entry
        const updateData: any = {
          updatedBy: userId,
          updatedAt: new Date(),
        };

        if (updateJournalEntryDto.journalNumber !== undefined) {
          updateData.journalNumber = updateJournalEntryDto.journalNumber;
        }
        if (updateJournalEntryDto.journalDate !== undefined) {
          updateData.journalDate = updateJournalEntryDto.journalDate;
        }
        if (updateJournalEntryDto.description !== undefined) {
          updateData.description = updateJournalEntryDto.description;
        }
        if (updateJournalEntryDto.memo !== undefined) {
          updateData.memo = updateJournalEntryDto.memo;
        }
        if (updateJournalEntryDto.referenceType !== undefined) {
          updateData.referenceType = updateJournalEntryDto.referenceType;
        }
        if (updateJournalEntryDto.referenceId !== undefined) {
          updateData.referenceId = updateJournalEntryDto.referenceId;
        }
        if (updateJournalEntryDto.status !== undefined) {
          updateData.status = updateJournalEntryDto.status;
        }

        await tx
          .update(journalEntries)
          .set(updateData)
          .where(eq(journalEntries.id, id));

        // If journal entry lines are provided, replace all existing lines
        if (updateJournalEntryDto.journalEntryLines) {
          // Soft delete existing lines
          await tx
            .update(journalEntryLines)
            .set({
              deletedBy: userId,
              deletedAt: new Date(),
            })
            .where(
              and(
                eq(journalEntryLines.journalId, id),
                isNull(journalEntryLines.deletedAt),
              ),
            );

          // Create new lines
          const lineValues = updateJournalEntryDto.journalEntryLines.map(
            (line: any) => ({
              journalId: id,
              lineNumber: line.lineNumber || 1,
              sortOrder: line.sortOrder || 1,
              accountId: line.accountId,
              debitAmount: line.debitAmount || '0.00',
              creditAmount: line.creditAmount || '0.00',
              description: line.description,
              nameReference: line.nameReference,
              taxId: line.taxId,
              entityType: line.entityType,
              entityId: line.entityId,
              createdBy: userId,
              updatedBy: userId,
            }),
          );

          await tx.insert(journalEntryLines).values(lineValues);
        }
      });

      // Log activity
      await this.activityLogService.log(
        ActivityLogName.UPDATE,
        `Updated journal entry: ${updateJournalEntryDto.journalNumber || existingJournalEntry[0].journalNumber}`,
        { id: id, type: 'journal_entry' },
        { id: userId, type: 'user' },
        { businessId },
      );

      return this.findOne(userId, businessId, id);
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update journal entry: ${error.message}`,
      );
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<{ message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if journal entry exists
      const existingJournalEntry = await this.db
        .select({
          id: journalEntries.id,
          journalNumber: journalEntries.journalNumber,
        })
        .from(journalEntries)
        .where(
          and(
            eq(journalEntries.id, id),
            eq(journalEntries.businessId, businessId),
            isNull(journalEntries.deletedAt),
          ),
        )
        .limit(1);

      if (existingJournalEntry.length === 0) {
        throw new NotFoundException('Journal entry not found');
      }

      // Soft delete journal entry and its lines in a transaction
      await this.db.transaction(async (tx) => {
        // Soft delete journal entry lines
        await tx
          .update(journalEntryLines)
          .set({
            deletedBy: userId,
            deletedAt: new Date(),
          })
          .where(
            and(
              eq(journalEntryLines.journalId, id),
              isNull(journalEntryLines.deletedAt),
            ),
          );

        // Soft delete journal entry
        await tx
          .update(journalEntries)
          .set({
            deletedBy: userId,
            deletedAt: new Date(),
          })
          .where(eq(journalEntries.id, id));
      });

      // Log activity
      await this.activityLogService.log(
        ActivityLogName.DELETE,
        `Deleted journal entry: ${existingJournalEntry[0].journalNumber}`,
        { id: id, type: 'journal_entry' },
        { id: userId, type: 'user' },
        { businessId },
      );

      return {
        message: `Journal entry ${existingJournalEntry[0].journalNumber} deleted successfully`,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to delete journal entry: ${error.message}`,
      );
    }
  }

  async findAllSlim(
    _userId: string,
    businessId: string | null,
  ): Promise<JournalEntrySlimDto[]> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const journalEntriesData = await this.db
        .select({
          id: journalEntries.id,
          journalNumber: journalEntries.journalNumber,
          description: journalEntries.description,
          status: journalEntries.status,
        })
        .from(journalEntries)
        .where(
          and(
            eq(journalEntries.businessId, businessId),
            isNull(journalEntries.deletedAt),
          ),
        )
        .orderBy(desc(journalEntries.createdAt));

      return journalEntriesData;
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to retrieve journal entries: ${error.message}`,
      );
    }
  }

  async checkJournalNumberAvailability(
    _userId: string,
    businessId: string | null,
    journalNumber: string,
    excludeId?: string,
  ): Promise<{ available: boolean; journalNumber: string; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const conditions = [
        eq(journalEntries.businessId, businessId),
        eq(journalEntries.journalNumber, journalNumber),
        isNull(journalEntries.deletedAt),
      ];

      if (excludeId) {
        conditions.push(sql`${journalEntries.id} != ${excludeId}`);
      }

      const existingEntry = await this.db
        .select({ id: journalEntries.id })
        .from(journalEntries)
        .where(and(...conditions))
        .limit(1);

      const available = existingEntry.length === 0;

      return {
        available,
        journalNumber,
        message: available
          ? 'Journal number is available'
          : 'Journal number already exists',
      };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to check journal number availability: ${error.message}`,
      );
    }
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    createJournalEntriesDto: CreateJournalEntryDto[],
  ): Promise<JournalEntryDto[]> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!createJournalEntriesDto || createJournalEntriesDto.length === 0) {
        throw new BadRequestException(
          'No journal entries provided for creation',
        );
      }

      // Validate all journal entries
      const journalNumbers = createJournalEntriesDto.map(
        (entry) => entry.journalNumber,
      );

      // Check for duplicate journal numbers within the batch
      const duplicates = journalNumbers.filter(
        (num, index) => journalNumbers.indexOf(num) !== index,
      );
      if (duplicates.length > 0) {
        throw new ConflictException(
          `Duplicate journal numbers in batch: ${duplicates.join(', ')}`,
        );
      }

      // Check for existing journal numbers
      const existingEntries = await this.db
        .select({ journalNumber: journalEntries.journalNumber })
        .from(journalEntries)
        .where(
          and(
            eq(journalEntries.businessId, businessId),
            inArray(journalEntries.journalNumber, journalNumbers),
            isNull(journalEntries.deletedAt),
          ),
        );

      if (existingEntries.length > 0) {
        const existingNumbers = existingEntries.map(
          (entry) => entry.journalNumber,
        );
        throw new ConflictException(
          `Journal numbers already exist: ${existingNumbers.join(', ')}`,
        );
      }

      // Validate balance for each journal entry
      for (const entry of createJournalEntriesDto) {
        const totalDebits = entry.journalEntryLines.reduce(
          (sum, line) => sum + parseFloat(line.debitAmount),
          0,
        );
        const totalCredits = entry.journalEntryLines.reduce(
          (sum, line) => sum + parseFloat(line.creditAmount),
          0,
        );

        if (Math.abs(totalDebits - totalCredits) > 0.01) {
          throw new BadRequestException(
            `Journal entry '${entry.journalNumber}' is not balanced: total debits must equal total credits`,
          );
        }
      }

      // Validate all accounts exist
      const allAccountIds = createJournalEntriesDto.flatMap((entry) =>
        entry.journalEntryLines.map((line) => line.accountId),
      );
      const uniqueAccountIds = [...new Set(allAccountIds)];

      const existingAccounts = await this.db
        .select({ id: accounts.id })
        .from(accounts)
        .where(
          and(
            inArray(accounts.id, uniqueAccountIds),
            eq(accounts.businessId, businessId),
            isNull(accounts.deletedAt),
          ),
        );

      if (existingAccounts.length !== uniqueAccountIds.length) {
        throw new BadRequestException('One or more accounts do not exist');
      }

      // Create all journal entries and their lines in a transaction
      const createdEntries = await this.db.transaction(async (tx) => {
        const results = [];

        for (const entry of createJournalEntriesDto) {
          // Create journal entry
          const [journalEntry] = await tx
            .insert(journalEntries)
            .values({
              businessId,
              journalNumber: entry.journalNumber,
              journalDate: entry.journalDate,
              description: entry.description,
              memo: entry.memo,
              referenceType: entry.referenceType,
              referenceId: entry.referenceId,
              status: entry.status || JournalEntryStatus.DRAFT,
              createdBy: userId,
              updatedBy: userId,
            })
            .returning();

          // Create journal entry lines
          const lineValues = entry.journalEntryLines.map((line) => ({
            journalId: journalEntry.id,
            lineNumber: line.lineNumber,
            sortOrder: line.sortOrder,
            accountId: line.accountId,
            debitAmount: line.debitAmount,
            creditAmount: line.creditAmount,
            description: line.description,
            nameReference: line.nameReference,
            taxId: line.taxId,
            entityType: line.entityType,
            entityId: line.entityId,
            createdBy: userId,
            updatedBy: userId,
          }));

          await tx.insert(journalEntryLines).values(lineValues);
          results.push(journalEntry);
        }

        return results;
      });

      // Log activity for each created entry
      for (const entry of createdEntries) {
        await this.activityLogService.log(
          ActivityLogName.CREATE,
          `Bulk created journal entry: ${entry.journalNumber}`,
          { id: entry.id, type: 'journal_entry' },
          { id: userId, type: 'user' },
          { businessId },
        );
      }

      // Return full journal entry data
      const fullEntries = await Promise.all(
        createdEntries.map((entry) =>
          this.findOne(userId, businessId, entry.id),
        ),
      );

      return fullEntries;
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk create journal entries: ${error.message}`,
      );
    }
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createJournalEntriesDto: CreateJournalEntryDto[],
  ): Promise<{ ids: string[] }> {
    const journalEntries = await this.bulkCreate(
      userId,
      businessId,
      createJournalEntriesDto,
    );
    return { ids: journalEntries.map((entry) => entry.id) };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    journalEntryIds: string[],
  ): Promise<{
    deleted: number;
    message: string;
    deletedIds: string[];
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!journalEntryIds || journalEntryIds.length === 0) {
        throw new BadRequestException(
          'No journal entry IDs provided for deletion',
        );
      }

      // Get all journal entries that exist and belong to the business
      const existingEntries = await this.db
        .select({
          id: journalEntries.id,
          journalNumber: journalEntries.journalNumber,
          businessId: journalEntries.businessId,
        })
        .from(journalEntries)
        .where(
          and(
            inArray(journalEntries.id, journalEntryIds),
            eq(journalEntries.businessId, businessId),
            isNull(journalEntries.deletedAt),
          ),
        );

      if (existingEntries.length === 0) {
        throw new NotFoundException('No journal entries found for deletion');
      }

      const existingIds = existingEntries.map((entry) => entry.id);

      // Soft delete journal entries and their lines in a transaction
      await this.db.transaction(async (tx) => {
        // Soft delete journal entry lines
        await tx
          .update(journalEntryLines)
          .set({
            deletedBy: userId,
            deletedAt: new Date(),
          })
          .where(
            and(
              inArray(journalEntryLines.journalId, existingIds),
              isNull(journalEntryLines.deletedAt),
            ),
          );

        // Soft delete journal entries
        await tx
          .update(journalEntries)
          .set({
            deletedBy: userId,
            deletedAt: new Date(),
          })
          .where(inArray(journalEntries.id, existingIds));
      });

      // Log activity for each deleted entry
      for (const entry of existingEntries) {
        await this.activityLogService.log(
          ActivityLogName.DELETE,
          `Bulk deleted journal entry: ${entry.journalNumber}`,
          { id: entry.id, type: 'journal_entry' },
          { id: userId, type: 'user' },
          { businessId },
        );
      }

      return {
        deleted: existingEntries.length,
        message: `${existingEntries.length} journal entries deleted successfully`,
        deletedIds: existingIds,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk delete journal entries: ${error.message}`,
      );
    }
  }
}
