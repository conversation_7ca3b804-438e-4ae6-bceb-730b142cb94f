import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  JournalEntryStatus,
  JournalReferenceType,
} from '../../drizzle/schema/journal-entries.schema';
import { JournalEntryLineDto } from './journal-entry-line.dto';

export class JournalEntryDto {
  @ApiProperty({
    description: 'Unique identifier for the journal entry',
  })
  id: string;

  @ApiProperty({
    description: 'Business ID the journal entry belongs to',
  })
  businessId: string;

  @ApiProperty({
    description: 'Journal number',
    example: 'JE-2024-001',
  })
  journalNumber: string;

  @ApiProperty({
    description: 'Journal date',
    example: '2024-01-15',
  })
  journalDate: string;

  @ApiProperty({
    description: 'Journal entry description',
    example: 'Monthly rent payment',
  })
  description: string;

  @ApiPropertyOptional({
    description: 'Additional memo or notes',
    example: 'Payment for office rent - January 2024',
    nullable: true,
  })
  memo?: string;

  @ApiProperty({
    description: 'Reference type for this journal entry',
    enum: JournalReferenceType,
    enumName: 'JournalReferenceType',
    example: JournalReferenceType.MANUAL,
  })
  referenceType: JournalReferenceType;

  @ApiPropertyOptional({
    description: 'Reference ID if linked to another entity',
    example: 12345,
    nullable: true,
  })
  referenceId?: number;

  @ApiProperty({
    description: 'Journal entry status',
    enum: JournalEntryStatus,
    enumName: 'JournalEntryStatus',
    example: JournalEntryStatus.DRAFT,
  })
  status: JournalEntryStatus;

  @ApiProperty({
    description: 'Journal entry lines',
    type: [JournalEntryLineDto],
  })
  journalEntryLines: JournalEntryLineDto[];

  @ApiProperty({
    description: 'Total debit amount',
    example: '1000.00',
  })
  totalDebitAmount: string;

  @ApiProperty({
    description: 'Total credit amount',
    example: '1000.00',
  })
  totalCreditAmount: string;

  @ApiProperty({
    description: 'Whether the journal entry is balanced (debits = credits)',
    example: true,
  })
  isBalanced: boolean;

  @ApiProperty({
    example: 'John Doe',
    description: 'Name of the user who created the journal entry',
  })
  createdBy: string;

  @ApiPropertyOptional({
    example: 'Jane Smith',
    description: 'Name of the user who last updated the journal entry',
    nullable: true,
  })
  updatedBy?: string;

  @ApiProperty({
    example: '2023-01-01T00:00:00Z',
    description: 'Creation timestamp',
  })
  createdAt: string;

  @ApiProperty({
    example: '2023-01-01T00:00:00Z',
    description: 'Last update timestamp',
  })
  updatedAt: string;
}
