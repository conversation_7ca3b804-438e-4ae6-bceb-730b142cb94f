import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsUUID } from 'class-validator';

export class BulkDeleteJournalEntryDto {
  @ApiProperty({
    description: 'Array of journal entry IDs to delete',
    type: [String],
    example: [
      '550e8400-e29b-41d4-a716-************',
      '550e8400-e29b-41d4-a716-************',
    ],
  })
  @IsArray()
  @IsUUID('4', { each: true })
  ids: string[];
}
