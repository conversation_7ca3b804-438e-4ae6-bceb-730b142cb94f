import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  JournalEntryStatus,
  JournalReferenceType,
} from '../../drizzle/schema/journal-entries.schema';

export class JournalEntryListDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Journal Entry ID',
  })
  id: string;

  @ApiProperty({
    example: 'JE-2024-001',
    description: 'Journal number',
  })
  journalNumber: string;

  @ApiProperty({
    example: '2024-01-15',
    description: 'Journal date',
  })
  journalDate: string;

  @ApiProperty({
    example: 'Monthly rent payment',
    description: 'Journal entry description',
  })
  description: string;

  @ApiPropertyOptional({
    example: 'Payment for office rent - January 2024',
    description: 'Additional memo or notes',
    nullable: true,
  })
  memo?: string;

  @ApiProperty({
    description: 'Reference type for this journal entry',
    enum: JournalReferenceType,
    enumName: 'JournalReferenceType',
    example: JournalReferenceType.MANUAL,
  })
  referenceType: JournalReferenceType;

  @ApiPropertyOptional({
    description: 'Reference ID if linked to another entity',
    example: 12345,
    nullable: true,
  })
  referenceId?: number;

  @ApiProperty({
    description: 'Journal entry status',
    enum: JournalEntryStatus,
    enumName: 'JournalEntryStatus',
    example: JournalEntryStatus.DRAFT,
  })
  status: JournalEntryStatus;

  @ApiProperty({
    description: 'Total debit amount',
    example: '1000.00',
  })
  totalDebitAmount: string;

  @ApiProperty({
    description: 'Total credit amount',
    example: '1000.00',
  })
  totalCreditAmount: string;

  @ApiProperty({
    description: 'Whether the journal entry is balanced (debits = credits)',
    example: true,
  })
  isBalanced: boolean;

  @ApiProperty({
    description: 'Number of journal entry lines',
    example: 2,
  })
  linesCount: number;

  @ApiProperty({
    example: 'John Doe',
    description: 'Name of the user who created the journal entry',
  })
  createdBy: string;

  @ApiPropertyOptional({
    example: 'Jane Smith',
    description: 'Name of the user who last updated the journal entry',
    nullable: true,
  })
  updatedBy?: string;

  @ApiProperty({
    example: '2023-01-01T00:00:00Z',
    description: 'Creation timestamp',
  })
  createdAt: string;

  @ApiProperty({
    example: '2023-01-01T00:00:00Z',
    description: 'Last update timestamp',
  })
  updatedAt: string;
}
