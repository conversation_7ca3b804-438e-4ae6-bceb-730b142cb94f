import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { EntityType } from '../../drizzle/schema/journal-entries.schema';

export class JournalEntryLineDto {
  @ApiProperty({
    description: 'Unique identifier for the journal entry line',
  })
  id: string;

  @ApiProperty({
    description: 'Journal entry ID this line belongs to',
  })
  journalId: string;

  @ApiProperty({
    description: 'Line number for ordering',
    example: 1,
  })
  lineNumber: number;

  @ApiProperty({
    description: 'Sort order for display',
    example: 1,
  })
  sortOrder: number;

  @ApiProperty({
    description: 'Account ID for this journal entry line',
  })
  accountId: string;

  @ApiProperty({
    description: 'Account name',
    example: 'Cash',
  })
  accountName: string;

  @ApiProperty({
    description: 'Account code',
    example: '1000',
  })
  accountCode: string;

  @ApiProperty({
    description: 'Debit amount',
    example: '100.00',
  })
  debitAmount: string;

  @ApiProperty({
    description: 'Credit amount',
    example: '0.00',
  })
  creditAmount: string;

  @ApiPropertyOptional({
    description: 'Description for this journal entry line',
    example: 'Payment for services',
    nullable: true,
  })
  description?: string;

  @ApiPropertyOptional({
    description: 'Name reference for this line',
    example: 'Customer ABC',
    nullable: true,
  })
  nameReference?: string;

  @ApiPropertyOptional({
    description: 'Tax ID if applicable',
    nullable: true,
  })
  taxId?: string;

  @ApiPropertyOptional({
    description: 'Tax name if applicable',
    example: 'VAT 10%',
    nullable: true,
  })
  taxName?: string;

  @ApiPropertyOptional({
    description: 'Entity type',
    enum: EntityType,
    enumName: 'EntityType',
    nullable: true,
  })
  entityType?: EntityType;

  @ApiPropertyOptional({
    description: 'Entity ID',
    example: 123,
    nullable: true,
  })
  entityId?: number;

  @ApiProperty({
    example: 'John Doe',
    description: 'Name of the user who created the journal entry line',
  })
  createdBy: string;

  @ApiPropertyOptional({
    example: 'Jane Smith',
    description: 'Name of the user who last updated the journal entry line',
    nullable: true,
  })
  updatedBy?: string;

  @ApiProperty({
    example: '2023-01-01T00:00:00Z',
    description: 'Creation timestamp',
  })
  createdAt: string;

  @ApiProperty({
    example: '2023-01-01T00:00:00Z',
    description: 'Last update timestamp',
  })
  updatedAt: string;
}
