import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  IsNumber,
  Min,
  IsEnum,
  IsDecimal,
  ValidateIf,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { EntityType } from '../../drizzle/schema/journal-entries.schema';

export class CreateJournalEntryLineDto {
  @ApiProperty({
    description: 'Line number for ordering',
    example: 1,
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  @Transform(({ value }) => parseInt(value))
  lineNumber: number;

  @ApiProperty({
    description: 'Sort order for display',
    example: 1,
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  @Transform(({ value }) => parseInt(value))
  sortOrder: number;

  @ApiProperty({
    description: 'Account ID for this journal entry line',
  })
  @IsNotEmpty()
  @IsUUID()
  accountId: string;

  @ApiProperty({
    description: 'Debit amount',
    example: '100.00',
  })
  @IsNotEmpty()
  @IsDecimal({ decimal_digits: '0,2' })
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value;
    }
    return value?.toString() || '0.00';
  })
  debitAmount: string;

  @ApiProperty({
    description: 'Credit amount',
    example: '0.00',
  })
  @IsNotEmpty()
  @IsDecimal({ decimal_digits: '0,2' })
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value;
    }
    return value?.toString() || '0.00';
  })
  creditAmount: string;

  @ApiPropertyOptional({
    description: 'Description for this journal entry line',
    example: 'Payment for services',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Name reference for this line',
    example: 'Customer ABC',
  })
  @IsOptional()
  @IsString()
  nameReference?: string;

  @ApiPropertyOptional({
    description: 'Tax ID if applicable',
  })
  @IsOptional()
  @IsUUID()
  taxId?: string;

  @ApiPropertyOptional({
    description: 'Entity type',
    enum: EntityType,
    enumName: 'EntityType',
  })
  @IsOptional()
  @IsEnum(EntityType)
  entityType?: EntityType;

  @ApiPropertyOptional({
    description: 'Entity ID',
    example: 123,
  })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => value ? parseInt(value) : undefined)
  entityId?: number;
}
