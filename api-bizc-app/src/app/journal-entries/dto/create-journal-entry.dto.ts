import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
  IsEnum,
  IsNumber,
  IsArray,
  ValidateNested,
  ArrayMinSize,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import {
  JournalEntryStatus,
  JournalReferenceType,
} from '../../drizzle/schema/journal-entries.schema';
import { CreateJournalEntryLineDto } from './create-journal-entry-line.dto';

export class CreateJournalEntryDto {
  @ApiProperty({
    description: 'Journal number',
    maxLength: 191,
    example: 'JE-2024-001',
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(191)
  journalNumber: string;

  @ApiProperty({
    description: 'Journal date in YYYY-MM-DD format',
    example: '2024-01-15',
  })
  @IsNotEmpty()
  @IsString()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      // Ensure it's in YYYY-MM-DD format
      const date = new Date(value);
      if (isNaN(date.getTime())) {
        return value; // Let validation handle invalid dates
      }
      return date.toISOString().split('T')[0];
    }
    return value;
  })
  journalDate: string;

  @ApiProperty({
    description: 'Journal entry description',
    example: 'Monthly rent payment',
  })
  @IsNotEmpty()
  @IsString()
  description: string;

  @ApiPropertyOptional({
    description: 'Additional memo or notes',
    example: 'Payment for office rent - January 2024',
  })
  @IsOptional()
  @IsString()
  memo?: string;

  @ApiProperty({
    description: 'Reference type for this journal entry',
    enum: JournalReferenceType,
    enumName: 'JournalReferenceType',
    example: JournalReferenceType.MANUAL,
  })
  @IsNotEmpty()
  @IsEnum(JournalReferenceType)
  referenceType: JournalReferenceType;

  @ApiPropertyOptional({
    description: 'Reference ID if linked to another entity',
    example: 12345,
  })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => value ? parseInt(value) : undefined)
  referenceId?: number;

  @ApiPropertyOptional({
    description: 'Journal entry status',
    enum: JournalEntryStatus,
    enumName: 'JournalEntryStatus',
    example: JournalEntryStatus.DRAFT,
  })
  @IsOptional()
  @IsEnum(JournalEntryStatus)
  status?: JournalEntryStatus;

  @ApiProperty({
    description: 'Journal entry lines',
    type: [CreateJournalEntryLineDto],
    example: [
      {
        lineNumber: 1,
        sortOrder: 1,
        accountId: '550e8400-e29b-41d4-a716-************',
        debitAmount: '1000.00',
        creditAmount: '0.00',
        description: 'Rent expense',
      },
      {
        lineNumber: 2,
        sortOrder: 2,
        accountId: '550e8400-e29b-41d4-a716-************',
        debitAmount: '0.00',
        creditAmount: '1000.00',
        description: 'Cash payment',
      },
    ],
  })
  @IsNotEmpty()
  @IsArray()
  @ArrayMinSize(2, { message: 'Journal entry must have at least 2 lines' })
  @ValidateNested({ each: true })
  @Type(() => CreateJournalEntryLineDto)
  journalEntryLines: CreateJournalEntryLineDto[];
}
