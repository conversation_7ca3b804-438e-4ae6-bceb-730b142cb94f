import { ApiProperty } from '@nestjs/swagger';

export class BulkDeleteJournalEntryResponseDto {
  @ApiProperty({
    example: 5,
    description: 'Number of journal entries deleted',
  })
  deleted: number;

  @ApiProperty({
    example: '5 journal entries deleted successfully',
    description: 'Success message',
  })
  message: string;

  @ApiProperty({
    type: [String],
    example: [
      '550e8400-e29b-41d4-a716-446655440000',
      '550e8400-e29b-41d4-a716-446655440001',
    ],
    description: 'Array of deleted journal entry IDs',
  })
  deletedIds: string[];
}
