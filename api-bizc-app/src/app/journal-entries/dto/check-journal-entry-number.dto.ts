import { ApiProperty } from '@nestjs/swagger';

export class JournalEntryNumberAvailabilityResponseDto {
  @ApiProperty({
    example: true,
    description: 'Whether the journal number is available',
  })
  available: boolean;

  @ApiProperty({
    example: 'JE-2024-001',
    description: 'The journal number that was checked',
  })
  journalNumber: string;

  @ApiProperty({
    example: 'Journal number is available',
    description: 'Message about availability',
  })
  message: string;
}
