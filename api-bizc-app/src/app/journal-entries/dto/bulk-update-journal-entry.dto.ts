import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsArray, IsUUID } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { UpdateJournalEntryDto } from './update-journal-entry.dto';

export class BulkUpdateJournalEntryItemDto extends UpdateJournalEntryDto {
  @ApiProperty({
    description: 'Journal Entry ID to update',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsNotEmpty()
  @IsUUID()
  id: string;
}

export class BulkUpdateJournalEntryDto {
  @ApiProperty({
    description: 'JSON string containing array of journal entry update objects',
    example:
      '[{"id":"550e8400-e29b-41d4-a716-************","description":"Updated rent payment","status":"posted"}]',
  })
  @IsNotEmpty({ message: 'Journal entries are required' })
  @Transform(({ value }) => {
    try {
      return typeof value === 'string' ? JSON.parse(value) : value;
    } catch {
      return value;
    }
  })
  @IsArray({ message: 'Journal entries must be an array' })
  @Type(() => BulkUpdateJournalEntryItemDto)
  journalEntries: BulkUpdateJournalEntryItemDto[];
}
