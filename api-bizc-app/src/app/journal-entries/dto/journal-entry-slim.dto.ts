import { ApiProperty } from '@nestjs/swagger';
import { JournalEntryStatus } from '../../drizzle/schema/journal-entries.schema';

export class JournalEntrySlimDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Journal Entry ID',
  })
  id: string;

  @ApiProperty({
    example: 'JE-2024-001',
    description: 'Journal number',
  })
  journalNumber: string;

  @ApiProperty({
    example: 'Monthly rent payment',
    description: 'Journal entry description',
  })
  description: string;

  @ApiProperty({
    description: 'Journal entry status',
    enum: JournalEntryStatus,
    enumName: 'JournalEntryStatus',
    example: JournalEntryStatus.DRAFT,
  })
  status: JournalEntryStatus;
}
