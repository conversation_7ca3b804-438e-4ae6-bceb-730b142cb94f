import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  MaxLength,
  IsEnum,
  IsNumber,
  IsArray,
  ValidateNested,
  ArrayMinSize,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import {
  JournalEntryStatus,
  JournalReferenceType,
} from '../../drizzle/schema/journal-entries.schema';
import { UpdateJournalEntryLineDto } from './update-journal-entry-line.dto';

export class UpdateJournalEntryDto {
  @ApiPropertyOptional({
    description: 'Journal number',
    maxLength: 191,
    example: 'JE-2024-001',
  })
  @IsOptional()
  @IsString()
  @MaxLength(191)
  journalNumber?: string;

  @ApiPropertyOptional({
    description: 'Journal date in YYYY-MM-DD format',
    example: '2024-01-15',
  })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      // Ensure it's in YYYY-MM-DD format
      const date = new Date(value);
      if (isNaN(date.getTime())) {
        return value; // Let validation handle invalid dates
      }
      return date.toISOString().split('T')[0];
    }
    return value;
  })
  journalDate?: string;

  @ApiPropertyOptional({
    description: 'Journal entry description',
    example: 'Monthly rent payment',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Additional memo or notes',
    example: 'Payment for office rent - January 2024',
  })
  @IsOptional()
  @IsString()
  memo?: string;

  @ApiPropertyOptional({
    description: 'Reference type for this journal entry',
    enum: JournalReferenceType,
    enumName: 'JournalReferenceType',
    example: JournalReferenceType.MANUAL,
  })
  @IsOptional()
  @IsEnum(JournalReferenceType)
  referenceType?: JournalReferenceType;

  @ApiPropertyOptional({
    description: 'Reference ID if linked to another entity',
    example: 12345,
  })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => value ? parseInt(value) : undefined)
  referenceId?: number;

  @ApiPropertyOptional({
    description: 'Journal entry status',
    enum: JournalEntryStatus,
    enumName: 'JournalEntryStatus',
    example: JournalEntryStatus.DRAFT,
  })
  @IsOptional()
  @IsEnum(JournalEntryStatus)
  status?: JournalEntryStatus;

  @ApiPropertyOptional({
    description: 'Journal entry lines (if provided, will replace all existing lines)',
    type: [UpdateJournalEntryLineDto],
    example: [
      {
        lineNumber: 1,
        sortOrder: 1,
        accountId: '550e8400-e29b-41d4-a716-************',
        debitAmount: '1000.00',
        creditAmount: '0.00',
        description: 'Rent expense',
      },
      {
        lineNumber: 2,
        sortOrder: 2,
        accountId: '550e8400-e29b-41d4-a716-************',
        debitAmount: '0.00',
        creditAmount: '1000.00',
        description: 'Cash payment',
      },
    ],
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(2, { message: 'Journal entry must have at least 2 lines' })
  @ValidateNested({ each: true })
  @Type(() => UpdateJournalEntryLineDto)
  journalEntryLines?: UpdateJournalEntryLineDto[];
}
