import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsArray } from 'class-validator';
import { Transform } from 'class-transformer';
import { CreateJournalEntryDto } from './create-journal-entry.dto';

export class BulkCreateJournalEntryDto {
  @ApiProperty({
    description: 'JSON string containing array of journal entry objects',
    example:
      '[{"journalNumber":"JE-2024-001","journalDate":"2024-01-15","description":"Rent payment","referenceType":"manual","journalEntryLines":[{"lineNumber":1,"sortOrder":1,"accountId":"550e8400-e29b-41d4-a716-************","debitAmount":"1000.00","creditAmount":"0.00"},{"lineNumber":2,"sortOrder":2,"accountId":"550e8400-e29b-41d4-a716-************","debitAmount":"0.00","creditAmount":"1000.00"}]}]',
  })
  @IsNotEmpty({ message: 'Journal entries are required' })
  @Transform(({ value }) => {
    try {
      return typeof value === 'string' ? JSON.parse(value) : value;
    } catch {
      return value;
    }
  })
  @IsArray({ message: 'Journal entries must be an array' })
  journalEntries: CreateJournalEntryDto[];
}
