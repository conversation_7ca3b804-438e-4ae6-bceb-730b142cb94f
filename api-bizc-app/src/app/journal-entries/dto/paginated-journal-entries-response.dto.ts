import { ApiProperty } from '@nestjs/swagger';
import { JournalEntryListDto } from './journal-entry-list.dto';
import { PaginationMeta } from '../../shared/dto/pagination-meta.dto';

export class PaginatedJournalEntriesResponseDto {
  @ApiProperty({
    type: [JournalEntryListDto],
    description: 'Array of journal entries',
  })
  data: JournalEntryListDto[];

  @ApiProperty({
    type: PaginationMeta,
    description: 'Pagination metadata',
  })
  meta: PaginationMeta;
}
