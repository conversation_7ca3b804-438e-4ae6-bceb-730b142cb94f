import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  Query,
  UseGuards,
} from '@nestjs/common';
import { JournalEntriesService } from './journal-entries.service';
import { CreateJournalEntryDto } from './dto/create-journal-entry.dto';
import { UpdateJournalEntryDto } from './dto/update-journal-entry.dto';
import { JournalEntryDto } from './dto/journal-entry.dto';
import { JournalEntrySlimDto } from './dto/journal-entry-slim.dto';
import { JournalEntryIdResponseDto } from './dto/journal-entry-id-response.dto';
import { BulkJournalEntryIdsResponseDto } from './dto/bulk-journal-entry-ids-response.dto';
import { BulkCreateJournalEntryDto } from './dto/bulk-create-journal-entry.dto';
import { BulkUpdateJournalEntryDto } from './dto/bulk-update-journal-entry.dto';
import { BulkDeleteJournalEntryDto } from './dto/bulk-delete-journal-entry.dto';
import { BulkDeleteJournalEntryResponseDto } from './dto/bulk-delete-journal-entry-response.dto';
import { PaginatedJournalEntriesResponseDto } from './dto/paginated-journal-entries-response.dto';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { JournalEntryNumberAvailabilityResponseDto } from './dto/check-journal-entry-number.dto';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { Permission } from '../shared/types/permission.enum';

@ApiTags('journal-entries')
@Controller('journal-entries')
@UseGuards(PermissionsGuard)
export class JournalEntriesController {
  constructor(private readonly journalEntriesService: JournalEntriesService) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.JOURNAL_ENTRY_CREATE)
  @ApiOperation({
    summary: 'Create a new journal entry',
    description:
      'Creates a new journal entry with its associated journal entry lines',
  })
  @ApiResponse({
    status: 201,
    description: 'Journal entry created successfully',
    type: JournalEntryDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data or unbalanced journal entry',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Journal number already exists',
  })
  create(
    @Request() req,
    @Body() createJournalEntryDto: CreateJournalEntryDto,
  ): Promise<JournalEntryDto> {
    return this.journalEntriesService.create(
      req.user.id,
      req.user.activeBusinessId,
      createJournalEntryDto,
    );
  }

  @Post('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.JOURNAL_ENTRY_CREATE)
  @ApiOperation({
    summary: 'Bulk create journal entries',
    description: 'Creates multiple journal entries in a single transaction',
  })
  @ApiBody({
    description: 'Bulk create journal entries data',
    type: BulkCreateJournalEntryDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Journal entries created successfully',
    type: BulkJournalEntryIdsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Journal numbers already exist',
  })
  bulkCreate(
    @Request() req,
    @Body() bulkCreateJournalEntryDto: BulkCreateJournalEntryDto,
  ): Promise<BulkJournalEntryIdsResponseDto> {
    return this.journalEntriesService.bulkCreateAndReturnIds(
      req.user.id,
      req.user.activeBusinessId,
      bulkCreateJournalEntryDto.journalEntries,
    );
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.JOURNAL_ENTRY_READ)
  @ApiOperation({
    summary: 'Get all journal entries for the active business',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'from',
    description: 'Start date filter (YYYY-MM-DD)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'to',
    description: 'End date filter (YYYY-MM-DD)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'journalNumber',
    description: 'Filter by journal number',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'description',
    description: 'Filter by description',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'status',
    description: 'Filter by status',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'referenceType',
    description: 'Filter by reference type',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'filters',
    description: 'Advanced filters as JSON string',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'joinOperator',
    description: 'Join operator for filters (and/or)',
    required: false,
    enum: ['and', 'or'],
  })
  @ApiQuery({
    name: 'sort',
    description: 'Sort field and direction (field:asc/desc)',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Returns paginated journal entries',
    type: PaginatedJournalEntriesResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('journalNumber') journalNumber?: string,
    @Query('description') description?: string,
    @Query('status') status?: string,
    @Query('referenceType') referenceType?: string,
    @Query('filters') filters?: string,
    @Query('joinOperator') joinOperator?: 'and' | 'or',
    @Query('sort') sort?: string,
  ): Promise<PaginatedJournalEntriesResponseDto> {
    return this.journalEntriesService.findAllOptimized(
      req.user.id,
      req.user.activeBusinessId,
      page ? parseInt(page.toString()) : undefined,
      limit ? parseInt(limit.toString()) : undefined,
      from,
      to,
      journalNumber,
      description,
      status,
      referenceType,
      filters,
      joinOperator,
      sort,
    );
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.JOURNAL_ENTRY_READ)
  @ApiOperation({
    summary: 'Get all journal entries in slim format',
    description:
      'Returns a simplified list of journal entries for dropdowns and selections',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns slim journal entries',
    type: [JournalEntrySlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllSlim(@Request() req): Promise<JournalEntrySlimDto[]> {
    return this.journalEntriesService.findAllSlim(
      req.user.id,
      req.user.activeBusinessId,
    );
  }

  @Get('check-journal-number/:journalNumber')
  @ApiBearerAuth()
  @RequirePermissions(Permission.JOURNAL_ENTRY_READ)
  @ApiOperation({
    summary: 'Check if journal number is available',
    description: 'Checks if a journal number is available for use',
  })
  @ApiParam({
    name: 'journalNumber',
    description: 'Journal number to check',
    type: String,
  })
  @ApiQuery({
    name: 'excludeId',
    description: 'Journal entry ID to exclude from check (for updates)',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Returns journal number availability',
    type: JournalEntryNumberAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkJournalNumberAvailability(
    @Request() req,
    @Param('journalNumber') journalNumber: string,
    @Query('excludeId') excludeId?: string,
  ): Promise<JournalEntryNumberAvailabilityResponseDto> {
    return this.journalEntriesService.checkJournalNumberAvailability(
      req.user.id,
      req.user.activeBusinessId,
      journalNumber,
      excludeId,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.JOURNAL_ENTRY_READ)
  @ApiOperation({ summary: 'Get a journal entry by ID' })
  @ApiParam({
    name: 'id',
    description: 'Journal Entry ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the journal entry with its lines',
    type: JournalEntryDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Journal entry not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to this journal entry',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findOne(@Request() req, @Param('id') id: string): Promise<JournalEntryDto> {
    return this.journalEntriesService.findOne(
      req.user.id,
      req.user.activeBusinessId,
      id,
    );
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.JOURNAL_ENTRY_UPDATE)
  @ApiOperation({
    summary: 'Update a journal entry',
    description: 'Updates a journal entry and optionally replaces its lines',
  })
  @ApiParam({
    name: 'id',
    description: 'Journal Entry ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Journal entry updated successfully',
    type: JournalEntryDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 404,
    description: 'Journal entry not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to this journal entry',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Journal number already exists',
  })
  update(
    @Request() req,
    @Param('id') id: string,
    @Body() updateJournalEntryDto: UpdateJournalEntryDto,
  ): Promise<JournalEntryDto> {
    return this.journalEntriesService.update(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateJournalEntryDto,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.JOURNAL_ENTRY_DELETE)
  @ApiOperation({ summary: 'Bulk delete journal entries' })
  @ApiBody({
    description: 'Array of journal entry IDs to delete',
    type: BulkDeleteJournalEntryDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Journal entries deleted successfully',
    type: BulkDeleteJournalEntryResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 404,
    description: 'No journal entries found for deletion',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  bulkDelete(
    @Request() req,
    @Body() bulkDeleteJournalEntryDto: BulkDeleteJournalEntryDto,
  ): Promise<BulkDeleteJournalEntryResponseDto> {
    return this.journalEntriesService.bulkDelete(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteJournalEntryDto.ids,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.JOURNAL_ENTRY_DELETE)
  @ApiOperation({ summary: 'Delete a journal entry' })
  @ApiParam({
    name: 'id',
    description: 'Journal Entry ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Journal entry deleted successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Journal entry JE-2024-001 deleted successfully',
        },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Journal entry not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to this journal entry',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  remove(
    @Request() req,
    @Param('id') id: string,
  ): Promise<{ message: string }> {
    return this.journalEntriesService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
    );
  }
}
