/**
 * Product-related enums used across the application
 */

/**
 * Service Status
 * Status of a service in the system
 */
export enum ServiceStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

/**
 * Warranty Status
 * Status of a warranty in the system
 */
export enum WarrantyStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  TRANSFERRED = 'transferred',
  CANCELLED = 'cancelled',
}

/**
 * Duration Type
 * Time unit used for durations (like warranty periods)
 */
export enum DurationType {
  DAYS = 'days',
  MONTHS = 'months',
  YEARS = 'years',
  LIFETIME = 'lifetime',
}

/**
 * Brand Status
 * Status of a brand in the system
 */
export enum BrandStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

/**
 * Customer Group Status
 * Status of a customer group in the system
 */
export enum CustomerGroupStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

/**
 * Expense Category Status
 * Status of an expense category in the system
 */
export enum ExpenseCategoryStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

/**
 * Category Status
 * Status of a category in the system
 */
export enum CategoryStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

export enum LeaveTypeStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

export enum VehicleCategoryStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

export enum RoomTypeStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

export enum ReservationTypeStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

/**
 * Reservation Type Category
 * Category of a reservation type (accommodation or event)
 */
export enum ReservationTypeCategory {
  ACCOMMODATION = 'accommodation',
  EVENT = 'event',
}

export enum EquipmentTypeStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

/**
 * Payment Method Status
 * Status of a payment method in the system
 */
export enum PaymentMethodStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

/**
 * Referral Status
 * Status of a referral in the system
 */
export enum ReferralStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

/**
 * Newsletter Subscriber Status
 * Status of a newsletter subscriber in the system
 */
export enum NewsletterSubscriberStatus {
  ACTIVE = 'active',
  UNSUBSCRIBED = 'unsubscribed',
  BOUNCED = 'bounced',
  PENDING = 'pending',
  DELETED = 'deleted',
  INACTIVE = 'inactive',
}

/**
 * Phone Newsletter Subscriber Status
 * Status of a phone newsletter subscriber in the system
 */
export enum PhoneNewsletterSubscriberStatus {
  ACTIVE = 'active',
  UNSUBSCRIBED = 'unsubscribed',
  BLOCKED = 'blocked',
  PENDING = 'pending',
  INVALID = 'invalid',
  DELETED = 'deleted',
  INACTIVE = 'inactive',
}

/**
 * Phone Type
 * Type of phone number
 */
export enum PhoneType {
  MOBILE = 'mobile',
  LANDLINE = 'landline',
  VOIP = 'voip',
  UNKNOWN = 'unknown',
}

/**
 * Subscription Source
 * Where the subscriber signed up from
 */
export enum SubscriptionSource {
  WEBSITE = 'website',
  MOBILE_APP = 'mobile_app',
  SOCIAL_MEDIA = 'social_media',
  EMAIL_CAMPAIGN = 'email_campaign',
  SMS_CAMPAIGN = 'sms_campaign',
  QR_CODE = 'qr_code',
  REFERRAL = 'referral',
  POPUP = 'popup',
  LANDING_PAGE = 'landing_page',
  CHECKOUT = 'checkout',
  STORE = 'store',
  EVENT = 'event',
  MANUAL = 'manual',
  IMPORT = 'import',
  API = 'api',
  OTHER = 'other',
}

/**
 * Product Status
 * Status of a product in the system
 */
export enum ProductStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DELETED = 'deleted',
  DRAFT = 'draft',
  OUT_OF_STOCK = 'out_of_stock',
}

/**
 * Product Sub Type
 * Type of product in the system
 */
export enum ProductSubType {
  SINGLE = 'single',
  VARIABLE = 'variable',
  MODIFIER = 'modifier',
  COMBO = 'combo',
}

export enum ProductType {
  SINGLE = 'single',
  VARIABLE = 'variable',
  MODIFIER = 'modifier',
  COMBO = 'combo',
  INGREDIENT = 'ingredient',
  MENU_ITEM = 'menu_item',
  RAW_MATERIAL = 'raw_material',
  WORK_IN_PROGRESS = 'work_in_progress',
  FINISHED_GOOD = 'finished_good',
}

/**
 * Unit Type
 * Units of measurement for products
 */
export enum UnitType {
  KG = 'kg',
  ML = 'ml',
  UNIT = 'unit',
}

/**
 * Barcode Type
 * Type of barcode used for products
 */
export enum BarcodeType {
  C39 = 'C39',
  C128 = 'C128',
  EAN13 = 'EAN13',
  EAN8 = 'EAN8',
  UPCA = 'UPCA',
  UPCE = 'UPCE',
}

/**
 * Expiry Period Type
 * Unit of time for product expiry periods
 */
export enum ExpiryPeriodType {
  DAYS = 'days',
  MONTHS = 'months',
}

/**
 * Tracking Type
 * Type of inventory tracking for products
 */
export enum TrackingType {
  NONE = 'none',
  SERIAL = 'serial',
  IMEI = 'imei',
  BATCH = 'batch',
}

/**
 * Serial Number Status
 * Status of a serial number in the system
 */
export enum SerialNumberStatus {
  AVAILABLE = 'available',
  SOLD = 'sold',
  RESERVED = 'reserved',
  DAMAGED = 'damaged',
  RETURNED = 'returned',
  WARRANTY = 'warranty',
}

/**
 * Standard Payment Method
 * Common payment methods for businesses
 */
export enum StandardPaymentMethod {
  CASH = 'cash',
  CREDIT_CARD = 'credit_card',
  DEBIT_CARD = 'debit_card',
  BANK_TRANSFER = 'bank_transfer',
  DIGITAL_WALLET = 'digital_wallet',
  CHECK = 'check',
  MOBILE_PAYMENT = 'mobile_payment',
  CRYPTOCURRENCY = 'cryptocurrency',
  GIFT_CARD = 'gift_card',
  STORE_CREDIT = 'store_credit',
  PAYPAL = 'paypal',
  APPLE_PAY = 'apple_pay',
  GOOGLE_PAY = 'google_pay',
  VENMO = 'venmo',
  ZELLE = 'zelle',
  WIRE_TRANSFER = 'wire_transfer',
  MONEY_ORDER = 'money_order',
  CASHIERS_CHECK = 'cashiers_check',
}

/**
 * Standard Unit of Measure
 * Common units of measurement for products
 */
export enum StandardUnitOfMeasure {
  // Count/Quantity
  PIECES = 'pcs',
  UNITS = 'units',
  EACH = 'each',
  DOZEN = 'dozen',
  PAIR = 'pair',

  // Weight
  GRAMS = 'g',
  KILOGRAMS = 'kg',
  POUNDS = 'lb',
  OUNCES = 'oz',
  TONS = 'tons',

  // Volume/Liquid
  MILLILITERS = 'ml',
  LITERS = 'l',
  GALLONS = 'gal',
  FLUID_OUNCES = 'fl_oz',
  CUPS = 'cups',
  PINTS = 'pints',
  QUARTS = 'quarts',

  // Length
  MILLIMETERS = 'mm',
  CENTIMETERS = 'cm',
  METERS = 'm',
  INCHES = 'in',
  FEET = 'ft',
  YARDS = 'yd',

  // Area
  SQUARE_METERS = 'sqm',
  SQUARE_FEET = 'sqft',
  SQUARE_INCHES = 'sqin',

  // Time
  HOURS = 'hrs',
  DAYS = 'days',
  WEEKS = 'weeks',
  MONTHS = 'months',
}

/**
 * Price Type
 * Types of pricing for products
 */
export enum PriceType {
  FIXED = 'fixed',
  PERCENT_DISCOUNT = 'percent_discount',
  AMOUNT_DISCOUNT = 'amount_discount',
}
