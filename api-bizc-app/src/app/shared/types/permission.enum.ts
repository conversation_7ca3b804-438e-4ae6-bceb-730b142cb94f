export enum Permission {
  // Business User permissions
  BUSINESS_USER_CREATE = 'business-user:create',
  BUSINESS_USER_READ = 'business-user:read',
  BUSINESS_USER_UPDATE = 'business-user:update',
  BUSINESS_USER_DELETE = 'business-user:delete',
  BUSINESS_USER_INVITE = 'business-user:invite',

  // User permissions
  USER_CREATE = 'user:create',
  USER_READ = 'user:read',
  USER_UPDATE = 'user:update',
  USER_DELETE = 'user:delete',

  // Brand permissions
  BRAND_CREATE = 'brand:create',
  BRAND_READ = 'brand:read',
  BRAND_UPDATE = 'brand:update',
  BRAND_DELETE = 'brand:delete',

  // Asset permissions
  ASSET_CREATE = 'asset:create',
  ASSET_READ = 'asset:read',
  ASSET_UPDATE = 'asset:update',
  ASSET_DELETE = 'asset:delete',

  // Customer Group permissions
  CUSTOMER_GROUP_CREATE = 'customer-group:create',
  CUSTOMER_GROUP_READ = 'customer-group:read',
  CUSTOMER_GROUP_UPDATE = 'customer-group:update',
  CUSTOMER_GROUP_DELETE = 'customer-group:delete',

  // Customer permissions
  CUSTOMER_CREATE = 'customer:create',
  CUSTOMER_READ = 'customer:read',
  CUSTOMER_UPDATE = 'customer:update',
  CUSTOMER_DELETE = 'customer:delete',

  // Tax permissions
  TAX_CREATE = 'tax:create',
  TAX_READ = 'tax:read',
  TAX_UPDATE = 'tax:update',
  TAX_DELETE = 'tax:delete',

  // Business permissions
  BUSINESS_CREATE = 'business:create',
  BUSINESS_READ = 'business:read',
  BUSINESS_UPDATE = 'business:update',
  BUSINESS_DELETE = 'business:delete',

  // Category permissions
  CATEGORY_CREATE = 'category:create',
  CATEGORY_READ = 'category:read',
  CATEGORY_UPDATE = 'category:update',
  CATEGORY_DELETE = 'category:delete',

  // Repair Category permissions
  REPAIR_CATEGORY_CREATE = 'repair-category:create',
  REPAIR_CATEGORY_READ = 'repair-category:read',
  REPAIR_CATEGORY_UPDATE = 'repair-category:update',
  REPAIR_CATEGORY_DELETE = 'repair-category:delete',

  // Campaign permissions
  CAMPAIGN_CREATE = 'campaign:create',
  CAMPAIGN_READ = 'campaign:read',
  CAMPAIGN_UPDATE = 'campaign:update',
  CAMPAIGN_DELETE = 'campaign:delete',

  // Game permissions
  GAME_CREATE = 'game:create',
  GAME_READ = 'game:read',
  GAME_UPDATE = 'game:update',
  GAME_DELETE = 'game:delete',

  // Room Type permissions
  ROOM_TYPE_CREATE = 'room-type:create',
  ROOM_TYPE_READ = 'room-type:read',
  ROOM_TYPE_UPDATE = 'room-type:update',
  ROOM_TYPE_DELETE = 'room-type:delete',

  // Reservation Type permissions
  RESERVATION_TYPE_CREATE = 'reservation-type:create',
  RESERVATION_TYPE_READ = 'reservation-type:read',
  RESERVATION_TYPE_UPDATE = 'reservation-type:update',
  RESERVATION_TYPE_DELETE = 'reservation-type:delete',

  // Vehicle Type permissions
  VEHICLE_TYPE_CREATE = 'vehicle-type:create',
  VEHICLE_TYPE_READ = 'vehicle-type:read',
  VEHICLE_TYPE_UPDATE = 'vehicle-type:update',
  VEHICLE_TYPE_DELETE = 'vehicle-type:delete',

  // Vehicle permissions
  VEHICLE_CREATE = 'vehicle:create',
  VEHICLE_READ = 'vehicle:read',
  VEHICLE_UPDATE = 'vehicle:update',
  VEHICLE_DELETE = 'vehicle:delete',

  // Vehicle Reservation permissions
  VEHICLE_RESERVATION_CREATE = 'vehicle-reservation:create',
  VEHICLE_RESERVATION_READ = 'vehicle-reservation:read',
  VEHICLE_RESERVATION_UPDATE = 'vehicle-reservation:update',
  VEHICLE_RESERVATION_DELETE = 'vehicle-reservation:delete',

  // Bill of Materials permissions
  BILL_OF_MATERIALS_CREATE = 'bill-of-materials:create',
  BILL_OF_MATERIALS_READ = 'bill-of-materials:read',
  BILL_OF_MATERIALS_UPDATE = 'bill-of-materials:update',
  BILL_OF_MATERIALS_DELETE = 'bill-of-materials:delete',

  // Service permissions
  SERVICE_CREATE = 'service:create',
  SERVICE_READ = 'service:read',
  SERVICE_UPDATE = 'service:update',
  SERVICE_DELETE = 'service:delete',

  // Service Order Status permissions
  SERVICE_ORDER_STATUS_CREATE = 'service-order-status:create',
  SERVICE_ORDER_STATUS_READ = 'service-order-status:read',
  SERVICE_ORDER_STATUS_UPDATE = 'service-order-status:update',
  SERVICE_ORDER_STATUS_DELETE = 'service-order-status:delete',

  // Service Order Priority permissions
  SERVICE_ORDER_PRIORITY_CREATE = 'service-order-priority:create',
  SERVICE_ORDER_PRIORITY_READ = 'service-order-priority:read',
  SERVICE_ORDER_PRIORITY_UPDATE = 'service-order-priority:update',
  SERVICE_ORDER_PRIORITY_DELETE = 'service-order-priority:delete',

  // Service Order permissions
  SERVICE_ORDER_CREATE = 'service-order:create',
  SERVICE_ORDER_READ = 'service-order:read',
  SERVICE_ORDER_UPDATE = 'service-order:update',
  SERVICE_ORDER_DELETE = 'service-order:delete',

  // Service Time Slot permissions
  SERVICE_TIME_SLOT_CREATE = 'service-time-slot:create',
  SERVICE_TIME_SLOT_READ = 'service-time-slot:read',
  SERVICE_TIME_SLOT_UPDATE = 'service-time-slot:update',
  SERVICE_TIME_SLOT_DELETE = 'service-time-slot:delete',

  // Work Order Priority permissions
  WORK_ORDER_PRIORITY_CREATE = 'work-order-priority:create',
  WORK_ORDER_PRIORITY_READ = 'work-order-priority:read',
  WORK_ORDER_PRIORITY_UPDATE = 'work-order-priority:update',
  WORK_ORDER_PRIORITY_DELETE = 'work-order-priority:delete',

  // Work Order Status permissions
  WORK_ORDER_STATUS_CREATE = 'work-order-status:create',
  WORK_ORDER_STATUS_READ = 'work-order-status:read',
  WORK_ORDER_STATUS_UPDATE = 'work-order-status:update',
  WORK_ORDER_STATUS_DELETE = 'work-order-status:delete',

  // Work Order permissions
  WORK_ORDER_CREATE = 'work-order:create',
  WORK_ORDER_READ = 'work-order:read',
  WORK_ORDER_UPDATE = 'work-order:update',
  WORK_ORDER_DELETE = 'work-order:delete',

  // Warranty permissions
  WARRANTY_CREATE = 'warranty:create',
  WARRANTY_READ = 'warranty:read',
  WARRANTY_UPDATE = 'warranty:update',
  WARRANTY_DELETE = 'warranty:delete',

  // Staff permissions
  STAFF_CREATE = 'staff:create',
  STAFF_READ = 'staff:read',
  STAFF_UPDATE = 'staff:update',
  STAFF_DELETE = 'staff:delete',

  // Attendance permissions
  ATTENDANCE_CREATE = 'attendance:create',
  ATTENDANCE_READ = 'attendance:read',
  ATTENDANCE_UPDATE = 'attendance:update',
  ATTENDANCE_DELETE = 'attendance:delete',

  // Performance Review permissions
  PERFORMANCE_REVIEW_CREATE = 'performance-review:create',
  PERFORMANCE_REVIEW_READ = 'performance-review:read',
  PERFORMANCE_REVIEW_UPDATE = 'performance-review:update',
  PERFORMANCE_REVIEW_DELETE = 'performance-review:delete',

  // Department permissions
  DEPARTMENT_CREATE = 'department:create',
  DEPARTMENT_READ = 'department:read',
  DEPARTMENT_UPDATE = 'department:update',
  DEPARTMENT_DELETE = 'department:delete',

  // Designation permissions
  DESIGNATION_CREATE = 'designation:create',
  DESIGNATION_READ = 'designation:read',
  DESIGNATION_UPDATE = 'designation:update',
  DESIGNATION_DELETE = 'designation:delete',

  // Activity Log permissions
  ACTIVITY_LOG_READ = 'activity-log:read',
  ACTIVITY_LOG_DELETE = 'activity-log:delete',

  // Role permissions
  ROLE_CREATE = 'role:create',
  ROLE_READ = 'role:read',
  ROLE_UPDATE = 'role:update',
  ROLE_DELETE = 'role:delete',

  // Business Role permissions
  BUSINESS_ROLE_CREATE = 'business-role:create',
  BUSINESS_ROLE_READ = 'business-role:read',
  BUSINESS_ROLE_UPDATE = 'business-role:update',
  BUSINESS_ROLE_DELETE = 'business-role:delete',

  // location permissions
  LOCATION_CREATE = 'location:create',
  LOCATION_READ = 'location:read',
  LOCATION_UPDATE = 'location:update',
  LOCATION_DELETE = 'location:delete',
  LOCATION_ASSIGN_TO_USER = 'location:assign-to-user',
  LOCATION_UNASSIGN_FROM_USER = 'location:unassign-from-user',

  // Variation permissions
  VARIATION_CREATE = 'variation:create',
  VARIATION_READ = 'variation:read',
  VARIATION_UPDATE = 'variation:update',
  VARIATION_DELETE = 'variation:delete',

  // Expense Category permissions
  EXPENSE_CATEGORY_CREATE = 'expense-category:create',
  EXPENSE_CATEGORY_READ = 'expense-category:read',
  EXPENSE_CATEGORY_UPDATE = 'expense-category:update',
  EXPENSE_CATEGORY_DELETE = 'expense-category:delete',

  // Expense permissions
  EXPENSE_CREATE = 'expense:create',
  EXPENSE_READ = 'expense:read',
  EXPENSE_UPDATE = 'expense:update',
  EXPENSE_DELETE = 'expense:delete',

  // Asset Category permissions
  ASSET_CATEGORY_CREATE = 'asset-category:create',
  ASSET_CATEGORY_READ = 'asset-category:read',
  ASSET_CATEGORY_UPDATE = 'asset-category:update',
  ASSET_CATEGORY_DELETE = 'asset-category:delete',

  // Asset Type permissions
  ASSET_TYPE_CREATE = 'asset-type:create',
  ASSET_TYPE_READ = 'asset-type:read',
  ASSET_TYPE_UPDATE = 'asset-type:update',
  ASSET_TYPE_DELETE = 'asset-type:delete',

  // Asset Transaction permissions
  ASSET_TRANSACTION_CREATE = 'asset-transaction:create',
  ASSET_TRANSACTION_READ = 'asset-transaction:read',
  ASSET_TRANSACTION_UPDATE = 'asset-transaction:update',
  ASSET_TRANSACTION_DELETE = 'asset-transaction:delete',

  // Meal Period permissions
  MEAL_PERIOD_CREATE = 'meal-period:create',
  MEAL_PERIOD_READ = 'meal-period:read',
  MEAL_PERIOD_UPDATE = 'meal-period:update',
  MEAL_PERIOD_DELETE = 'meal-period:delete',

  // Repair Device Model permissions
  REPAIR_DEVICE_MODEL_CREATE = 'repair-device-model:create',
  REPAIR_DEVICE_MODEL_READ = 'repair-device-model:read',
  REPAIR_DEVICE_MODEL_UPDATE = 'repair-device-model:update',
  REPAIR_DEVICE_MODEL_DELETE = 'repair-device-model:delete',

  // Custom Field permissions
  CUSTOM_FIELD_CREATE = 'custom-field:create',
  CUSTOM_FIELD_READ = 'custom-field:read',
  CUSTOM_FIELD_UPDATE = 'custom-field:update',
  CUSTOM_FIELD_DELETE = 'custom-field:delete',

  // Payment Account Type permissions
  PAYMENT_ACCOUNT_TYPE_CREATE = 'payment-account-type:create',
  PAYMENT_ACCOUNT_TYPE_READ = 'payment-account-type:read',
  PAYMENT_ACCOUNT_TYPE_UPDATE = 'payment-account-type:update',
  PAYMENT_ACCOUNT_TYPE_DELETE = 'payment-account-type:delete',

  // Supplier permissions
  SUPPLIER_CREATE = 'supplier:create',
  SUPPLIER_READ = 'supplier:read',
  SUPPLIER_UPDATE = 'supplier:update',
  SUPPLIER_DELETE = 'supplier:delete',

  // Restaurant Time Slot permissions
  RESTAURANT_TIME_SLOT_CREATE = 'restaurant-time-slot:create',
  RESTAURANT_TIME_SLOT_READ = 'restaurant-time-slot:read',
  RESTAURANT_TIME_SLOT_UPDATE = 'restaurant-time-slot:update',
  RESTAURANT_TIME_SLOT_DELETE = 'restaurant-time-slot:delete',

  // Payment Account permissions
  PAYMENT_ACCOUNT_CREATE = 'payment-account:create',
  PAYMENT_ACCOUNT_READ = 'payment-account:read',
  PAYMENT_ACCOUNT_UPDATE = 'payment-account:update',
  PAYMENT_ACCOUNT_DELETE = 'payment-account:delete',

  // Payment Method permissions
  PAYMENT_METHOD_CREATE = 'payment-method:create',
  PAYMENT_METHOD_READ = 'payment-method:read',
  PAYMENT_METHOD_UPDATE = 'payment-method:update',
  PAYMENT_METHOD_DELETE = 'payment-method:delete',

  // File Upload permissions
  FILE_UPLOAD = 'file:upload',
  FILE_DELETE = 'file:delete',
  FILE_READ = 'file:read',

  // Vehicle Fine permissions
  VEHICLE_FINE_CREATE = 'vehicle-fine:create',
  VEHICLE_FINE_READ = 'vehicle-fine:read',
  VEHICLE_FINE_UPDATE = 'vehicle-fine:update',
  VEHICLE_FINE_DELETE = 'vehicle-fine:delete',

  // Vehicle Damage permissions
  VEHICLE_DAMAGE_CREATE = 'vehicle-damage:create',
  VEHICLE_DAMAGE_READ = 'vehicle-damage:read',
  VEHICLE_DAMAGE_UPDATE = 'vehicle-damage:update',
  VEHICLE_DAMAGE_DELETE = 'vehicle-damage:delete',

  // Newsletter Subscriber permissions
  NEWSLETTER_SUBSCRIBER_CREATE = 'newsletter-subscriber:create',
  NEWSLETTER_SUBSCRIBER_READ = 'newsletter-subscriber:read',
  NEWSLETTER_SUBSCRIBER_UPDATE = 'newsletter-subscriber:update',
  NEWSLETTER_SUBSCRIBER_DELETE = 'newsletter-subscriber:delete',

  // Template permissions
  TEMPLATE_CREATE = 'template:create',
  TEMPLATE_READ = 'template:read',
  TEMPLATE_UPDATE = 'template:update',
  TEMPLATE_DELETE = 'template:delete',

  // Provider permissions
  PROVIDER_CREATE = 'provider:create',
  PROVIDER_READ = 'provider:read',
  PROVIDER_UPDATE = 'provider:update',
  PROVIDER_DELETE = 'provider:delete',

  // Account permissions
  ACCOUNT_CREATE = 'account:create',
  ACCOUNT_READ = 'account:read',
  ACCOUNT_UPDATE = 'account:update',
  ACCOUNT_DELETE = 'account:delete',

  // Project permissions
  PROJECT_CREATE = 'project:create',
  PROJECT_READ = 'project:read',
  PROJECT_UPDATE = 'project:update',
  PROJECT_DELETE = 'project:delete',

  // Task permissions
  TASK_CREATE = 'task:create',
  TASK_READ = 'task:read',
  TASK_UPDATE = 'task:update',
  TASK_DELETE = 'task:delete',

  // Lead permissions
  LEAD_CREATE = 'lead:create',
  LEAD_READ = 'lead:read',
  LEAD_UPDATE = 'lead:update',
  LEAD_DELETE = 'lead:delete',

  // Estimate permissions
  ESTIMATE_CREATE = 'estimate:create',
  ESTIMATE_READ = 'estimate:read',
  ESTIMATE_UPDATE = 'estimate:update',
  ESTIMATE_DELETE = 'estimate:delete',

  // Recurring Activity permissions
  RECURRING_ACTIVITY_CREATE = 'recurring-activity:create',
  RECURRING_ACTIVITY_READ = 'recurring-activity:read',
  RECURRING_ACTIVITY_UPDATE = 'recurring-activity:update',
  RECURRING_ACTIVITY_DELETE = 'recurring-activity:delete',

  // Modifier permissions
  MODIFIER_CREATE = 'modifier:create',
  MODIFIER_READ = 'modifier:read',
  MODIFIER_UPDATE = 'modifier:update',
  MODIFIER_DELETE = 'modifier:delete',

  // Discount Plan permissions
  DISCOUNT_PLAN_CREATE = 'discount-plan:create',
  DISCOUNT_PLAN_READ = 'discount-plan:read',
  DISCOUNT_PLAN_UPDATE = 'discount-plan:update',
  DISCOUNT_PLAN_DELETE = 'discount-plan:delete',

  // Subscription Plan permissions
  SUBSCRIPTION_PLAN_CREATE = 'subscription-plan:create',
  SUBSCRIPTION_PLAN_READ = 'subscription-plan:read',
  SUBSCRIPTION_PLAN_UPDATE = 'subscription-plan:update',
  SUBSCRIPTION_PLAN_DELETE = 'subscription-plan:delete',

  // Subscription permissions
  SUBSCRIPTION_CREATE = 'subscription:create',
  SUBSCRIPTION_READ = 'subscription:read',
  SUBSCRIPTION_UPDATE = 'subscription:update',
  SUBSCRIPTION_DELETE = 'subscription:delete',

  // Subscription Payment permissions
  SUBSCRIPTION_PAYMENT_CREATE = 'subscription-payment:create',
  SUBSCRIPTION_PAYMENT_READ = 'subscription-payment:read',
  SUBSCRIPTION_PAYMENT_UPDATE = 'subscription-payment:update',
  SUBSCRIPTION_PAYMENT_DELETE = 'subscription-payment:delete',

  // Subscription Usage permissions
  SUBSCRIPTION_USAGE_CREATE = 'subscription-usage:create',
  SUBSCRIPTION_USAGE_READ = 'subscription-usage:read',
  SUBSCRIPTION_USAGE_UPDATE = 'subscription-usage:update',
  SUBSCRIPTION_USAGE_DELETE = 'subscription-usage:delete',

  // Promo Code permissions
  PROMO_CODE_CREATE = 'promo-code:create',
  PROMO_CODE_READ = 'promo-code:read',
  PROMO_CODE_UPDATE = 'promo-code:update',
  PROMO_CODE_DELETE = 'promo-code:delete',

  // Referral permissions
  REFERRAL_CREATE = 'referral:create',
  REFERRAL_READ = 'referral:read',
  REFERRAL_UPDATE = 'referral:update',
  REFERRAL_DELETE = 'referral:delete',

  // Asset Maintenance permissions
  ASSET_MAINTENANCE_CREATE = 'asset-maintenance:create',
  ASSET_MAINTENANCE_READ = 'asset-maintenance:read',
  ASSET_MAINTENANCE_UPDATE = 'asset-maintenance:update',
  ASSET_MAINTENANCE_DELETE = 'asset-maintenance:delete',

  // Asset Repair Order permissions
  ASSET_REPAIR_ORDER_CREATE = 'asset-repair-order:create',
  ASSET_REPAIR_ORDER_READ = 'asset-repair-order:read',
  ASSET_REPAIR_ORDER_UPDATE = 'asset-repair-order:update',
  ASSET_REPAIR_ORDER_DELETE = 'asset-repair-order:delete',

  // Meeting permissions
  MEETING_CREATE = 'meeting:create',
  MEETING_READ = 'meeting:read',
  MEETING_UPDATE = 'meeting:update',
  MEETING_DELETE = 'meeting:delete',

  // Appointment permissions
  APPOINTMENT_CREATE = 'appointment:create',
  APPOINTMENT_READ = 'appointment:read',
  APPOINTMENT_UPDATE = 'appointment:update',
  APPOINTMENT_DELETE = 'appointment:delete',

  UNIT_CREATE = 'unit:create',
  UNIT_READ = 'unit:read',
  UNIT_UPDATE = 'unit:update',
  UNIT_DELETE = 'unit:delete',

  // Rental Item permissions
  RENTAL_ITEM_CREATE = 'rental-item:create',
  RENTAL_ITEM_READ = 'rental-item:read',
  RENTAL_ITEM_UPDATE = 'rental-item:update',
  RENTAL_ITEM_DELETE = 'rental-item:delete',

  // Leave Type permissions
  LEAVE_TYPE_CREATE = 'leave-type:create',
  LEAVE_TYPE_READ = 'leave-type:read',
  LEAVE_TYPE_UPDATE = 'leave-type:update',
  LEAVE_TYPE_DELETE = 'leave-type:delete',

  // Leave Request permissions
  LEAVE_REQUEST_CREATE = 'leave-request:create',
  LEAVE_REQUEST_READ = 'leave-request:read',
  LEAVE_REQUEST_UPDATE = 'leave-request:update',
  LEAVE_REQUEST_DELETE = 'leave-request:delete',
  LEAVE_REQUEST_APPROVE = 'leave-request:approve',

  // Leave Balance permissions
  LEAVE_BALANCE_CREATE = 'leave-balance:create',
  LEAVE_BALANCE_READ = 'leave-balance:read',
  LEAVE_BALANCE_UPDATE = 'leave-balance:update',
  LEAVE_BALANCE_DELETE = 'leave-balance:delete',

  // Restaurant Table permissions
  RESTAURANT_TABLE_CREATE = 'restaurant-table:create',
  RESTAURANT_TABLE_READ = 'restaurant-table:read',
  RESTAURANT_TABLE_UPDATE = 'restaurant-table:update',
  RESTAURANT_TABLE_DELETE = 'restaurant-table:delete',

  // Floor Plan permissions
  FLOOR_PLAN_CREATE = 'floor-plan:create',
  FLOOR_PLAN_READ = 'floor-plan:read',
  FLOOR_PLAN_UPDATE = 'floor-plan:update',
  FLOOR_PLAN_DELETE = 'floor-plan:delete',

  // Deduction Type permissions
  DEDUCTION_TYPE_CREATE = 'deduction-type:create',
  DEDUCTION_TYPE_READ = 'deduction-type:read',
  DEDUCTION_TYPE_UPDATE = 'deduction-type:update',
  DEDUCTION_TYPE_DELETE = 'deduction-type:delete',

  // Employee Deduction permissions
  EMPLOYEE_DEDUCTION_CREATE = 'employee-deduction:create',
  EMPLOYEE_DEDUCTION_READ = 'employee-deduction:read',
  EMPLOYEE_DEDUCTION_UPDATE = 'employee-deduction:update',
  EMPLOYEE_DEDUCTION_DELETE = 'employee-deduction:delete',

  // Allowance Type permissions
  ALLOWANCE_TYPE_CREATE = 'allowance-type:create',
  ALLOWANCE_TYPE_READ = 'allowance-type:read',
  ALLOWANCE_TYPE_UPDATE = 'allowance-type:update',
  ALLOWANCE_TYPE_DELETE = 'allowance-type:delete',

  // Employee Allowance permissions
  EMPLOYEE_ALLOWANCE_CREATE = 'employee-allowance:create',
  EMPLOYEE_ALLOWANCE_READ = 'employee-allowance:read',
  EMPLOYEE_ALLOWANCE_UPDATE = 'employee-allowance:update',
  EMPLOYEE_ALLOWANCE_DELETE = 'employee-allowance:delete',

  // Time Slot permissions
  TIME_SLOT_CREATE = 'time-slot:create',
  TIME_SLOT_READ = 'time-slot:read',
  TIME_SLOT_UPDATE = 'time-slot:update',
  TIME_SLOT_DELETE = 'time-slot:delete',

  // Product permissions
  PRODUCT_CREATE = 'product:create',
  PRODUCT_READ = 'product:read',
  PRODUCT_UPDATE = 'product:update',
  PRODUCT_DELETE = 'product:delete',
  PRODUCT_UPDATE_POSITIONS = 'product:update-positions',

  // Accommodation Unit permissions
  ACCOMMODATION_UNIT_CREATE = 'accommodation-unit:create',
  ACCOMMODATION_UNIT_READ = 'accommodation-unit:read',
  ACCOMMODATION_UNIT_UPDATE = 'accommodation-unit:update',
  ACCOMMODATION_UNIT_DELETE = 'accommodation-unit:delete',
  ACCOMMODATION_UNIT_UPDATE_POSITIONS = 'accommodation-unit:update-positions',

  // Accommodation Reservation permissions
  ACCOMMODATION_RESERVATION_CREATE = 'accommodation-reservation:create',
  ACCOMMODATION_RESERVATION_READ = 'accommodation-reservation:read',
  ACCOMMODATION_RESERVATION_UPDATE = 'accommodation-reservation:update',
  ACCOMMODATION_RESERVATION_DELETE = 'accommodation-reservation:delete',

  // Package Reservation permissions
  PACKAGE_RESERVATION_CREATE = 'package-reservation:create',
  PACKAGE_RESERVATION_READ = 'package-reservation:read',
  PACKAGE_RESERVATION_UPDATE = 'package-reservation:update',
  PACKAGE_RESERVATION_DELETE = 'package-reservation:delete',

  // Event Space permissions
  EVENT_SPACE_CREATE = 'event-space:create',
  EVENT_SPACE_READ = 'event-space:read',
  EVENT_SPACE_UPDATE = 'event-space:update',
  EVENT_SPACE_DELETE = 'event-space:delete',

  // Event Space Reservation permissions
  EVENT_SPACE_RESERVATION_CREATE = 'event-space-reservation:create',
  EVENT_SPACE_RESERVATION_READ = 'event-space-reservation:read',
  EVENT_SPACE_RESERVATION_UPDATE = 'event-space-reservation:update',
  EVENT_SPACE_RESERVATION_DELETE = 'event-space-reservation:delete',

  // Recipe permissions
  RECIPE_CREATE = 'recipe:create',
  RECIPE_READ = 'recipe:read',
  RECIPE_UPDATE = 'recipe:update',
  RECIPE_DELETE = 'recipe:delete',

  // Menu Item permissions
  MENU_ITEM_CREATE = 'menu-item:create',
  MENU_ITEM_READ = 'menu-item:read',
  MENU_ITEM_UPDATE = 'menu-item:update',
  MENU_ITEM_DELETE = 'menu-item:delete',

  // Comment permissions
  COMMENT_CREATE = 'comment:create',
  COMMENT_READ = 'comment:read',
  COMMENT_UPDATE = 'comment:update',
  COMMENT_DELETE = 'comment:delete',

  // Vehicle Category permissions
  VEHICLE_CATEGORY_CREATE = 'vehicle-category:create',
  VEHICLE_CATEGORY_READ = 'vehicle-category:read',
  VEHICLE_CATEGORY_UPDATE = 'vehicle-category:update',
  VEHICLE_CATEGORY_DELETE = 'vehicle-category:delete',
  VEHICLE_CATEGORY_UPDATE_POSITIONS = 'vehicle-category:update-positions',
  VEHICLE_CATEGORY_UPDATE_HIERARCHY = 'vehicle-category:update-hierarchy',

  // Package permissions
  PACKAGE_CREATE = 'package:create',
  PACKAGE_READ = 'package:read',
  PACKAGE_UPDATE = 'package:update',
  PACKAGE_DELETE = 'package:delete',

  // Housekeeping permissions
  HOUSEKEEPING_CREATE = 'housekeeping:create',
  HOUSEKEEPING_READ = 'housekeeping:read',
  HOUSEKEEPING_UPDATE = 'housekeeping:update',
  HOUSEKEEPING_DELETE = 'housekeeping:delete',

  // Journal Entry permissions
  JOURNAL_ENTRY_CREATE = 'journal-entry:create',
  JOURNAL_ENTRY_READ = 'journal-entry:read',
  JOURNAL_ENTRY_UPDATE = 'journal-entry:update',
  JOURNAL_ENTRY_DELETE = 'journal-entry:delete',
}
