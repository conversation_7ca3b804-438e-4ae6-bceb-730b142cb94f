import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  MaxLength,
  IsUUID,
  IsBoolean,
  IsEnum,
  IsDecimal,
  IsDateString,
} from 'class-validator';
import { Transform } from 'class-transformer';
import {
  AccountCategory,
  ChartAccountType,
  AccountDetailType,
} from '../../shared/types';
import { AccountStatus } from '../../drizzle/schema/accounts.schema';

export class UpdateAccountDto {
  @ApiPropertyOptional({
    description: 'Account name',
    maxLength: 191,
  })
  @IsOptional()
  @IsString()
  @MaxLength(191)
  accountName?: string;

  @ApiPropertyOptional({
    description: 'Account number',
    maxLength: 191,
  })
  @IsOptional()
  @IsString()
  @MaxLength(191)
  accountNumber?: string;

  @ApiPropertyOptional({
    description: 'Account category',
    enum: AccountCategory,
    enumName: 'AccountCategory',
    example: AccountCategory.ASSETS,
  })
  @IsOptional()
  @IsEnum(AccountCategory, {
    message: 'Account category must be a valid category',
  })
  accountCategory?: AccountCategory;

  @ApiPropertyOptional({
    description: 'Account type',
    enum: ChartAccountType,
    enumName: 'ChartAccountType',
    example: ChartAccountType.CASH_AND_CASH_EQUIVALENTS,
  })
  @IsOptional()
  @IsEnum(ChartAccountType, {
    message: 'Account type must be a valid type',
  })
  accountType?: ChartAccountType;

  @ApiPropertyOptional({
    description: 'Account detail type',
    enum: AccountDetailType,
    enumName: 'AccountDetailType',
    example: AccountDetailType.BANK,
  })
  @IsOptional()
  @IsEnum(AccountDetailType, {
    message: 'Account detail type must be a valid detail type',
  })
  accountDetailType?: AccountDetailType;

  @ApiPropertyOptional({
    description: 'Parent account ID for sub-accounts',
  })
  @IsOptional()
  @IsUUID()
  parentAccountId?: string;


  @ApiPropertyOptional({
    description: 'Opening balance amount',
    example: '1000.00',
  })
  @IsOptional()
  @IsDecimal(
    { decimal_digits: '0,2' },
    {
      message:
        'Opening balance must be a valid decimal with up to 2 decimal places',
    },
  )
  openingBalance?: string;

  @ApiPropertyOptional({
    description: 'Opening balance date',
    example: '2023-01-01',
  })
  @IsOptional()
  @IsDateString()
  openingBalanceDate?: string;

  @ApiPropertyOptional({
    description: 'Account description',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Default tax ID for this account',
  })
  @IsOptional()
  @IsUUID()
  defaultTaxId?: string;

  @ApiPropertyOptional({
    description: 'Use this account for billable expenses',
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  useForBillableExpenses?: boolean;

  @ApiPropertyOptional({
    description: 'Income account ID for billable expenses',
  })
  @IsOptional()
  @IsUUID()
  incomeAccountId?: string;

  @ApiPropertyOptional({
    description: 'Whether this is a system account',
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  isSystemAccount?: boolean;

  @ApiPropertyOptional({
    enum: AccountStatus,
    enumName: 'AccountStatus',
    description: 'Account status',
  })
  @IsOptional()
  @IsEnum(AccountStatus, {
    message: 'Status must be a valid status type',
  })
  status?: AccountStatus;
}
