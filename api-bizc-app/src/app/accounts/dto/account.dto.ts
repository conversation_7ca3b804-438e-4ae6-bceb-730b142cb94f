import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  AccountCategory,
  ChartAccountType,
  AccountDetailType,
} from '../../shared/types';
import { AccountStatus } from '../../drizzle/schema/accounts.schema';

export class AccountDto {
  @ApiProperty({ description: 'Unique identifier for the account' })
  id: string;

  @ApiProperty({ description: 'Business ID the account belongs to' })
  businessId: string;

  @ApiProperty({ description: 'Account name' })
  accountName: string;

  @ApiPropertyOptional({
    description: 'Account number',
    nullable: true,
  })
  accountNumber?: string;

  @ApiProperty({
    description: 'Account category',
    enum: AccountCategory,
    enumName: 'AccountCategory',
    example: AccountCategory.ASSETS,
  })
  accountCategory: AccountCategory;

  @ApiProperty({
    description: 'Account type',
    enum: ChartAccountType,
    enumName: 'ChartAccountType',
    example: ChartAccountType.CASH_AND_CASH_EQUIVALENTS,
  })
  accountType: ChartAccountType;

  @ApiProperty({
    description: 'Account detail type',
    enum: AccountDetailType,
    enumName: 'AccountDetailType',
    example: AccountDetailType.BANK,
  })
  accountDetailType: AccountDetailType;

  @ApiPropertyOptional({
    description: 'Parent account ID for sub-accounts',
    nullable: true,
  })
  parentAccountId?: string;


  @ApiPropertyOptional({
    description: 'Opening balance amount',
    example: '1000.00',
    nullable: true,
  })
  openingBalance?: string;

  @ApiPropertyOptional({
    description: 'Opening balance date',
    example: '2023-01-01',
    nullable: true,
  })
  openingBalanceDate?: string;

  @ApiPropertyOptional({
    description: 'Account description',
    nullable: true,
  })
  description?: string;

  @ApiPropertyOptional({
    description: 'Default tax ID for this account',
    nullable: true,
  })
  defaultTaxId?: string;

  @ApiProperty({
    description: 'Use this account for billable expenses',
    example: false,
  })
  useForBillableExpenses: boolean;

  @ApiPropertyOptional({
    description: 'Income account ID for billable expenses',
    nullable: true,
  })
  incomeAccountId?: string;

  @ApiProperty({
    description: 'Whether this is a system account',
    example: false,
  })
  isSystemAccount: boolean;

  @ApiProperty({
    enum: AccountStatus,
    enumName: 'AccountStatus',
    description: 'Account status',
    example: AccountStatus.ACTIVE,
  })
  status: AccountStatus;

  @ApiProperty({
    example: 'John Doe',
    description: 'Name of the user who created the account',
  })
  createdBy: string;

  @ApiPropertyOptional({
    example: 'Jane Smith',
    description: 'Name of the user who last updated the account',
    nullable: true,
  })
  updatedBy?: string;

  @ApiProperty({
    example: '2023-01-01T00:00:00Z',
    description: 'Creation timestamp',
  })
  createdAt: Date;

  @ApiProperty({
    example: '2023-01-01T00:00:00Z',
    description: 'Last update timestamp',
  })
  updatedAt: Date;
}
