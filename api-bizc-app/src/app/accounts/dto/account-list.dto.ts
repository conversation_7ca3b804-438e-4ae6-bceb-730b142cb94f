import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  AccountCategory,
  ChartAccountType,
  AccountDetailType,
} from '../../shared/types';
import { AccountStatus } from '../../drizzle/schema/accounts.schema';

export class AccountListDto {
  @ApiProperty({ description: 'Unique identifier for the account' })
  id: string;

  @ApiProperty({ description: 'Account name' })
  accountName: string;

  @ApiPropertyOptional({
    description: 'Account number',
    nullable: true,
  })
  accountNumber?: string;

  @ApiProperty({
    description: 'Account category',
    enum: AccountCategory,
    enumName: 'AccountCategory',
    example: AccountCategory.ASSETS,
  })
  accountCategory: AccountCategory;

  @ApiProperty({
    description: 'Account type',
    enum: ChartAccountType,
    enumName: 'ChartAccountType',
    example: ChartAccountType.CASH_AND_CASH_EQUIVALENTS,
  })
  accountType: ChartAccountType;

  @ApiProperty({
    description: 'Account detail type',
    enum: AccountDetailType,
    enumName: 'AccountDetailType',
    example: AccountDetailType.BANK,
  })
  accountDetailType: AccountDetailType;

  @ApiPropertyOptional({
    description: 'Parent account ID for sub-accounts',
    nullable: true,
  })
  parentAccountId?: string;


  @ApiPropertyOptional({
    description: 'Opening balance amount',
    example: '1000.00',
    nullable: true,
  })
  openingBalance?: string;

  @ApiPropertyOptional({
    description: 'Account description',
    nullable: true,
  })
  description?: string;

  @ApiProperty({
    description: 'Use this account for billable expenses',
    example: false,
  })
  useForBillableExpenses: boolean;

  @ApiProperty({
    description: 'Whether this is a system account',
    example: false,
  })
  isSystemAccount: boolean;

  @ApiProperty({
    enum: AccountStatus,
    enumName: 'AccountStatus',
    description: 'Account status',
    example: AccountStatus.ACTIVE,
  })
  status: AccountStatus;
}
