import {
  Injectable,
  ConflictException,
  NotFoundException,
  UnauthorizedException,
  BadRequestException,
} from '@nestjs/common';
import { Inject } from '@nestjs/common';
import {
  and,
  eq,
  ilike,
  isNull,
  isNotNull,
  asc,
  desc,
  or,
  sql,
  lte,
  gte,
} from 'drizzle-orm';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { accounts } from '../drizzle/schema/accounts.schema';
import { users } from '../drizzle/schema/users.schema';
import { CreateAccountDto } from './dto/create-account.dto';
import { UpdateAccountDto } from './dto/update-account.dto';
import { AccountDto } from './dto/account.dto';
import { AccountListDto } from './dto/account-list.dto';
import { AccountSlimDto } from './dto/account-slim.dto';
import {
  ActivityLogName,
  AccountCategory,
  ChartAccountType,
  AccountDetailType,
} from '../shared/types';
import { AccountStatus } from '../drizzle/schema/accounts.schema';
import { ActivityLogService } from '../activity-log/activity-log.service';

@Injectable()
export class AccountsService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private activityLogService: ActivityLogService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createAccountDto: CreateAccountDto,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if an account with the same name already exists for this business
      const existingAccount = await this.db
        .select()
        .from(accounts)
        .where(
          and(
            eq(accounts.businessId, businessId),
            ilike(accounts.accountName, createAccountDto.accountName),
            isNull(accounts.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (existingAccount) {
        throw new ConflictException(
          `Account with name "${createAccountDto.accountName}" already exists`,
        );
      }

      // Check if account number is provided and unique
      if (createAccountDto.accountNumber) {
        const existingAccountNumber = await this.db
          .select()
          .from(accounts)
          .where(
            and(
              eq(accounts.businessId, businessId),
              eq(accounts.accountNumber, createAccountDto.accountNumber),
              isNull(accounts.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (existingAccountNumber) {
          throw new ConflictException(
            `Account with number "${createAccountDto.accountNumber}" already exists`,
          );
        }
      }

      // Validate parent account if provided
      if (createAccountDto.parentAccountId) {
        const parentAccount = await this.db
          .select()
          .from(accounts)
          .where(
            and(
              eq(accounts.id, createAccountDto.parentAccountId),
              eq(accounts.businessId, businessId),
              isNull(accounts.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (!parentAccount) {
          throw new BadRequestException('Parent account not found');
        }
      }

      // Validate income account if provided
      if (createAccountDto.incomeAccountId) {
        const incomeAccount = await this.db
          .select()
          .from(accounts)
          .where(
            and(
              eq(accounts.id, createAccountDto.incomeAccountId),
              eq(accounts.businessId, businessId),
              isNull(accounts.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (!incomeAccount) {
          throw new BadRequestException('Income account not found');
        }
      }

      const [account] = await this.db
        .insert(accounts)
        .values({
          businessId,
          accountName: createAccountDto.accountName,
          accountNumber: createAccountDto.accountNumber,
          accountCategory: createAccountDto.accountCategory as any,
          accountType: createAccountDto.accountType as any,
          accountDetailType: createAccountDto.accountDetailType as any,
          parentAccountId: createAccountDto.parentAccountId,
          openingBalance: createAccountDto.openingBalance,
          openingBalanceDate: createAccountDto.openingBalanceDate,
          description: createAccountDto.description,
          defaultTaxId: createAccountDto.defaultTaxId,
          useForBillableExpenses:
            createAccountDto.useForBillableExpenses ?? false,
          incomeAccountId: createAccountDto.incomeAccountId,
          isSystemAccount: createAccountDto.isSystemAccount ?? false,
          status: createAccountDto.status ?? AccountStatus.ACTIVE,
          createdBy: userId,
        })
        .returning();

      // Log the activity
      await this.activityLogService.log(
        ActivityLogName.CREATE,
        `Created account: ${createAccountDto.accountName}`,
        { id: account.id, type: 'account' },
        { id: userId, type: 'user' },
        { accountId: account.id, businessId },
      );

      return { id: account.id };
    } catch (error) {
      console.error('Failed to create account:', error);
      if (
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to create account');
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createAccountDto: CreateAccountDto,
  ): Promise<{ id: string }> {
    return this.create(userId, businessId, createAccountDto);
  }

  async findAll(
    _userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    accountName?: string,
    accountNumber?: string,
    accountCategory?: string,
    accountType?: string,
    accountDetailType?: string,
    status?: string,
    _filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: AccountListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      isNull(accounts.deletedAt),
      eq(accounts.businessId, businessId),
    ];

    // Add search filters
    if (accountName) {
      whereConditions.push(ilike(accounts.accountName, `%${accountName}%`));
    }

    if (accountNumber) {
      whereConditions.push(ilike(accounts.accountNumber, `%${accountNumber}%`));
    }

    if (accountCategory) {
      whereConditions.push(
        eq(accounts.accountCategory, accountCategory as any),
      );
    }

    if (accountType) {
      whereConditions.push(ilike(accounts.accountType, `%${accountType}%`));
    }

    if (accountDetailType) {
      whereConditions.push(
        eq(accounts.accountDetailType, accountDetailType as any),
      );
    }

    if (status) {
      whereConditions.push(eq(accounts.status, status as AccountStatus));
    }

    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(accounts.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(accounts.createdAt, toDate));
      }
    }

    // Add advanced filters if provided
    if (_filters) {
      try {
        const parsedFilters = JSON.parse(_filters);
        const filterConditions = [];

        for (const filter of parsedFilters) {
          const { id: fieldId, value, operator } = filter;

          if (fieldId === 'accountName') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(
                  ilike(accounts.accountName, `%${value}%`),
                );
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(accounts.accountName, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(eq(accounts.accountName, value));
                break;
              case 'ne':
                filterConditions.push(sql`${accounts.accountName} != ${value}`);
                break;
              case 'isEmpty':
                filterConditions.push(
                  or(
                    isNull(accounts.accountName),
                    eq(accounts.accountName, ''),
                  ),
                );
                break;
              case 'isNotEmpty':
                filterConditions.push(
                  and(
                    isNotNull(accounts.accountName),
                    sql`${accounts.accountName} != ''`,
                  ),
                );
                break;
            }
          } else if (fieldId === 'accountNumber') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(
                  ilike(accounts.accountNumber, `%${value}%`),
                );
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(accounts.accountNumber, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(eq(accounts.accountNumber, value));
                break;
              case 'ne':
                filterConditions.push(
                  sql`${accounts.accountNumber} != ${value}`,
                );
                break;
              case 'isEmpty':
                filterConditions.push(
                  or(
                    isNull(accounts.accountNumber),
                    eq(accounts.accountNumber, ''),
                  ),
                );
                break;
              case 'isNotEmpty':
                filterConditions.push(
                  and(
                    isNotNull(accounts.accountNumber),
                    sql`${accounts.accountNumber} != ''`,
                  ),
                );
                break;
            }
          } else if (fieldId === 'accountCategory') {
            switch (operator) {
              case 'eq':
                filterConditions.push(eq(accounts.accountCategory, value));
                break;
              case 'ne':
                filterConditions.push(
                  sql`${accounts.accountCategory} != ${value}`,
                );
                break;
            }
          } else if (fieldId === 'accountType') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(
                  ilike(accounts.accountType, `%${value}%`),
                );
                break;
              case 'eq':
                filterConditions.push(eq(accounts.accountType, value));
                break;
              case 'ne':
                filterConditions.push(sql`${accounts.accountType} != ${value}`);
                break;
            }
          } else if (fieldId === 'accountDetailType') {
            switch (operator) {
              case 'eq':
                filterConditions.push(eq(accounts.accountDetailType, value));
                break;
              case 'ne':
                filterConditions.push(
                  sql`${accounts.accountDetailType} != ${value}`,
                );
                break;
            }
          } else if (fieldId === 'status') {
            switch (operator) {
              case 'eq':
                filterConditions.push(eq(accounts.status, value));
                break;
              case 'ne':
                filterConditions.push(sql`${accounts.status} != ${value}`);
                break;
            }
          }
        }

        if (filterConditions.length > 0) {
          if (joinOperator === 'or') {
            whereConditions.push(or(...filterConditions));
          } else {
            whereConditions.push(and(...filterConditions));
          }
        }
      } catch {
        // Invalid JSON, ignore filters
      }
    }

    // Build final where clause
    const whereClause = and(...whereConditions);

    // Build order by clause
    let orderBy = [desc(accounts.createdAt), asc(accounts.id)]; // Default sort

    if (sort) {
      try {
        const parsedSort = JSON.parse(sort);
        if (parsedSort.length > 0) {
          const sortField = parsedSort[0];
          const isDesc = sortField.desc === true;

          switch (sortField.id) {
            case 'accountName':
              orderBy = [
                isDesc ? desc(accounts.accountName) : asc(accounts.accountName),
                asc(accounts.id), // Secondary sort for consistency
              ];
              break;
            case 'accountNumber':
              orderBy = [
                isDesc
                  ? desc(accounts.accountNumber)
                  : asc(accounts.accountNumber),
                asc(accounts.id),
              ];
              break;
            case 'accountCategory':
              orderBy = [
                isDesc
                  ? desc(accounts.accountCategory)
                  : asc(accounts.accountCategory),
                asc(accounts.id),
              ];
              break;
            case 'accountType':
              orderBy = [
                isDesc ? desc(accounts.accountType) : asc(accounts.accountType),
                asc(accounts.id),
              ];
              break;
            case 'openingBalance':
              orderBy = [
                isDesc
                  ? desc(accounts.openingBalance)
                  : asc(accounts.openingBalance),
                asc(accounts.id),
              ];
              break;
            case 'createdAt':
              orderBy = [
                isDesc ? desc(accounts.createdAt) : asc(accounts.createdAt),
                asc(accounts.id),
              ];
              break;
            case 'updatedAt':
              orderBy = [
                isDesc ? desc(accounts.updatedAt) : asc(accounts.updatedAt),
                asc(accounts.id),
              ];
              break;
          }
        }
      } catch {
        // Invalid JSON, use default sort
      }
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(accounts)
      .where(whereClause);

    const total = totalResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // Get paginated results
    const results = await this.db
      .select({
        id: accounts.id,
        accountName: accounts.accountName,
        accountNumber: accounts.accountNumber,
        accountCategory: accounts.accountCategory,
        accountType: accounts.accountType,
        accountDetailType: accounts.accountDetailType,
        parentAccountId: accounts.parentAccountId,
        openingBalance: accounts.openingBalance,
        description: accounts.description,
        useForBillableExpenses: accounts.useForBillableExpenses,
        isSystemAccount: accounts.isSystemAccount,
        status: accounts.status,
      })
      .from(accounts)
      .where(whereClause)
      .orderBy(...orderBy)
      .limit(limit)
      .offset(offset);

    return {
      data: results as AccountListDto[],
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findAllOptimized(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    accountName?: string,
    accountNumber?: string,
    accountCategory?: string,
    accountType?: string,
    accountDetailType?: string,
    status?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: AccountListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    return this.findAll(
      userId,
      businessId,
      page,
      limit,
      from,
      to,
      accountName,
      accountNumber,
      accountCategory,
      accountType,
      accountDetailType,
      status,
      filters,
      joinOperator,
      sort,
    );
  }

  async findOne(
    _userId: string,
    businessId: string | null,
    id: string,
  ): Promise<AccountDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const result = await this.db
      .select({
        id: accounts.id,
        businessId: accounts.businessId,
        accountName: accounts.accountName,
        accountNumber: accounts.accountNumber,
        accountCategory: accounts.accountCategory,
        accountType: accounts.accountType,
        accountDetailType: accounts.accountDetailType,
        parentAccountId: accounts.parentAccountId,
        openingBalance: accounts.openingBalance,
        openingBalanceDate: accounts.openingBalanceDate,
        description: accounts.description,
        defaultTaxId: accounts.defaultTaxId,
        useForBillableExpenses: accounts.useForBillableExpenses,
        incomeAccountId: accounts.incomeAccountId,
        isSystemAccount: accounts.isSystemAccount,
        status: accounts.status,
        createdBy: users.name,
        updatedBy: sql<string>`updated_user.name`,
        createdAt: accounts.createdAt,
        updatedAt: accounts.updatedAt,
      })
      .from(accounts)
      .leftJoin(users, eq(accounts.createdBy, users.id))
      .leftJoin(
        sql`${users} as updated_user`,
        eq(accounts.updatedBy, sql`updated_user.id`),
      )
      .where(
        and(
          eq(accounts.id, id),
          eq(accounts.businessId, businessId),
          isNull(accounts.deletedAt),
        ),
      )
      .then((results) => results[0]);

    if (!result) {
      throw new NotFoundException('Account not found');
    }

    return result as AccountDto;
  }

  async findAllSlim(
    _userId: string,
    businessId: string | null,
  ): Promise<AccountSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const results = await this.db
      .select({
        id: accounts.id,
        accountName: accounts.accountName,
        accountNumber: accounts.accountNumber,
        accountCategory: accounts.accountCategory,
        accountType: accounts.accountType,
        status: accounts.status,
      })
      .from(accounts)
      .where(
        and(
          eq(accounts.businessId, businessId),
          eq(accounts.status, AccountStatus.ACTIVE),
          isNull(accounts.deletedAt),
        ),
      )
      .orderBy(asc(accounts.accountName));

    return results as AccountSlimDto[];
  }

  async checkAccountNameAvailability(
    _userId: string,
    businessId: string | null,
    accountName: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const existingAccount = await this.db
      .select()
      .from(accounts)
      .where(
        and(
          eq(accounts.businessId, businessId),
          ilike(accounts.accountName, accountName),
          isNull(accounts.deletedAt),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingAccount };
  }

  async checkAccountNumberAvailability(
    _userId: string,
    businessId: string | null,
    accountNumber: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const existingAccount = await this.db
      .select()
      .from(accounts)
      .where(
        and(
          eq(accounts.businessId, businessId),
          eq(accounts.accountNumber, accountNumber),
          isNull(accounts.deletedAt),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingAccount };
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateAccountDto: UpdateAccountDto,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if account exists
      const existingAccount = await this.db
        .select()
        .from(accounts)
        .where(
          and(
            eq(accounts.id, id),
            eq(accounts.businessId, businessId),
            isNull(accounts.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (!existingAccount) {
        throw new NotFoundException('Account not found');
      }

      // Check if account name is being updated and is unique
      if (
        updateAccountDto.accountName &&
        updateAccountDto.accountName !== existingAccount.accountName
      ) {
        const duplicateAccount = await this.db
          .select()
          .from(accounts)
          .where(
            and(
              eq(accounts.businessId, businessId),
              ilike(accounts.accountName, updateAccountDto.accountName),
              isNull(accounts.deletedAt),
              sql`${accounts.id} != ${id}`,
            ),
          )
          .then((results) => results[0]);

        if (duplicateAccount) {
          throw new ConflictException(
            `Account with name "${updateAccountDto.accountName}" already exists`,
          );
        }
      }

      // Check if account number is being updated and is unique
      if (
        updateAccountDto.accountNumber &&
        updateAccountDto.accountNumber !== existingAccount.accountNumber
      ) {
        const duplicateAccountNumber = await this.db
          .select()
          .from(accounts)
          .where(
            and(
              eq(accounts.businessId, businessId),
              eq(accounts.accountNumber, updateAccountDto.accountNumber),
              isNull(accounts.deletedAt),
              sql`${accounts.id} != ${id}`,
            ),
          )
          .then((results) => results[0]);

        if (duplicateAccountNumber) {
          throw new ConflictException(
            `Account with number "${updateAccountDto.accountNumber}" already exists`,
          );
        }
      }

      // Validate parent account if provided
      if (updateAccountDto.parentAccountId) {
        const parentAccount = await this.db
          .select()
          .from(accounts)
          .where(
            and(
              eq(accounts.id, updateAccountDto.parentAccountId),
              eq(accounts.businessId, businessId),
              isNull(accounts.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (!parentAccount) {
          throw new BadRequestException('Parent account not found');
        }
      }

      // Validate income account if provided
      if (updateAccountDto.incomeAccountId) {
        const incomeAccount = await this.db
          .select()
          .from(accounts)
          .where(
            and(
              eq(accounts.id, updateAccountDto.incomeAccountId),
              eq(accounts.businessId, businessId),
              isNull(accounts.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (!incomeAccount) {
          throw new BadRequestException('Income account not found');
        }
      }

      const updateData: any = {
        ...updateAccountDto,
        updatedBy: userId,
        updatedAt: new Date(),
      };

      // Cast enum values to their string equivalents for database compatibility
      if (updateAccountDto.accountCategory) {
        updateData.accountCategory = updateAccountDto.accountCategory as any;
      }
      if (updateAccountDto.accountType) {
        updateData.accountType = updateAccountDto.accountType as any;
      }
      if (updateAccountDto.accountDetailType) {
        updateData.accountDetailType =
          updateAccountDto.accountDetailType as any;
      }

      const [updatedAccount] = await this.db
        .update(accounts)
        .set(updateData)
        .where(eq(accounts.id, id))
        .returning();

      // Log the activity
      await this.activityLogService.log(
        ActivityLogName.UPDATE,
        `Updated account: ${updateAccountDto.accountName || existingAccount.accountName}`,
        { id: id, type: 'account' },
        { id: userId, type: 'user' },
        { accountId: id, businessId },
      );

      return { id: updatedAccount.id };
    } catch (error) {
      if (
        error instanceof ConflictException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to update account');
    }
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateAccountDto: UpdateAccountDto,
  ): Promise<{ id: string }> {
    return this.update(userId, businessId, id, updateAccountDto);
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<{ id: string; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if account exists
    const existingAccount = await this.db
      .select()
      .from(accounts)
      .where(
        and(
          eq(accounts.id, id),
          eq(accounts.businessId, businessId),
          isNull(accounts.deletedAt),
        ),
      )
      .then((results) => results[0]);

    if (!existingAccount) {
      throw new NotFoundException('Account not found');
    }

    // Soft delete the account
    await this.db
      .update(accounts)
      .set({
        deletedBy: userId,
        deletedAt: new Date(),
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(accounts.id, id));

    // Log the activity
    await this.activityLogService.log(
      ActivityLogName.DELETE,
      `Deleted account: ${existingAccount.accountName}`,
      { id: id, type: 'account' },
      { id: userId, type: 'user' },
      { accountId: id, businessId },
    );

    return {
      id,
      message: 'Account deleted successfully',
    };
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    createAccountDtos: CreateAccountDto[],
  ): Promise<{ ids: string[] }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const createdIds: string[] = [];

    // Use transaction for bulk operations
    await this.db.transaction(async (tx) => {
      for (const createAccountDto of createAccountDtos) {
        // Check if account name already exists
        const existingAccount = await tx
          .select()
          .from(accounts)
          .where(
            and(
              eq(accounts.businessId, businessId),
              ilike(accounts.accountName, createAccountDto.accountName),
              isNull(accounts.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (existingAccount) {
          throw new ConflictException(
            `Account with name "${createAccountDto.accountName}" already exists`,
          );
        }

        // Check if account number already exists (if provided)
        if (createAccountDto.accountNumber) {
          const existingAccountNumber = await tx
            .select()
            .from(accounts)
            .where(
              and(
                eq(accounts.businessId, businessId),
                eq(accounts.accountNumber, createAccountDto.accountNumber),
                isNull(accounts.deletedAt),
              ),
            )
            .then((results) => results[0]);

          if (existingAccountNumber) {
            throw new ConflictException(
              `Account with number "${createAccountDto.accountNumber}" already exists`,
            );
          }
        }

        const [account] = await tx
          .insert(accounts)
          .values({
            businessId,
            accountName: createAccountDto.accountName,
            accountNumber: createAccountDto.accountNumber,
            accountCategory: createAccountDto.accountCategory as any,
            accountType: createAccountDto.accountType as any,
            accountDetailType: createAccountDto.accountDetailType as any,
            parentAccountId: createAccountDto.parentAccountId,
            openingBalance: createAccountDto.openingBalance,
            openingBalanceDate: createAccountDto.openingBalanceDate,
            description: createAccountDto.description,
            defaultTaxId: createAccountDto.defaultTaxId,
            useForBillableExpenses:
              createAccountDto.useForBillableExpenses ?? false,
            incomeAccountId: createAccountDto.incomeAccountId,
            isSystemAccount: createAccountDto.isSystemAccount ?? false,
            status: createAccountDto.status ?? AccountStatus.ACTIVE,
            createdBy: userId,
          })
          .returning();

        createdIds.push(account.id);
      }
    });

    // Log the activity
    await this.activityLogService.log(
      ActivityLogName.CREATE,
      `Bulk created ${createdIds.length} accounts`,
      { id: createdIds.join(','), type: 'account' },
      { id: userId, type: 'user' },
      { accountIds: createdIds, businessId },
    );

    return { ids: createdIds };
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createAccountDtos: CreateAccountDto[],
  ): Promise<{ ids: string[] }> {
    return this.bulkCreate(userId, businessId, createAccountDtos);
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    ids: string[],
  ): Promise<{ deletedCount: number; deletedIds: string[] }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const deletedIds: string[] = [];

    // Use transaction for bulk operations
    await this.db.transaction(async (tx) => {
      for (const id of ids) {
        // Check if account exists
        const existingAccount = await tx
          .select()
          .from(accounts)
          .where(
            and(
              eq(accounts.id, id),
              eq(accounts.businessId, businessId),
              isNull(accounts.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (existingAccount) {
          await tx
            .update(accounts)
            .set({
              deletedBy: userId,
              deletedAt: new Date(),
              updatedBy: userId,
              updatedAt: new Date(),
            })
            .where(eq(accounts.id, id));

          deletedIds.push(id);
        }
      }
    });

    // Log the activity
    await this.activityLogService.log(
      ActivityLogName.DELETE,
      `Bulk deleted ${deletedIds.length} accounts`,
      { id: deletedIds.join(','), type: 'account' },
      { id: userId, type: 'user' },
      { accountIds: deletedIds, businessId },
    );

    return {
      deletedCount: deletedIds.length,
      deletedIds,
    };
  }

  async findFilteredAccounts(
    _userId: string,
    businessId: string | null,
    accountCategory?: string,
    accountType?: string,
    accountDetailType?: string,
  ): Promise<
    {
      id: string;
      accountName: string;
      accountCategory: AccountCategory;
      accountType: ChartAccountType;
      accountDetailType: AccountDetailType;
    }[]
  > {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Build base where conditions
    const whereConditions = [
      isNull(accounts.deletedAt),
      eq(accounts.businessId, businessId),
    ];

    // Add filter conditions based on provided parameters
    if (accountCategory) {
      whereConditions.push(
        eq(accounts.accountCategory, accountCategory as any),
      );
    }

    if (accountType) {
      whereConditions.push(eq(accounts.accountType, accountType as any));
    }

    if (accountDetailType) {
      whereConditions.push(
        eq(accounts.accountDetailType, accountDetailType as any),
      );
    }

    // Execute query with only required fields for optimization
    const results = await this.db
      .select({
        id: accounts.id,
        accountName: accounts.accountName,
        accountCategory: accounts.accountCategory,
        accountType: accounts.accountType,
        accountDetailType: accounts.accountDetailType,
      })
      .from(accounts)
      .where(and(...whereConditions))
      .orderBy(accounts.accountName);

    return results as {
      id: string;
      accountName: string;
      accountCategory: AccountCategory;
      accountType: ChartAccountType;
      accountDetailType: AccountDetailType;
    }[];
  }
}
