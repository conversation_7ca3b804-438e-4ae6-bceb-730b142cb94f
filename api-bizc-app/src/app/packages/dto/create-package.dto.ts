import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
  IsUUID,
  IsEnum,
  IsNumber,
  Min,
  IsArray,
  IsDecimal,
} from 'class-validator';
import { Transform } from 'class-transformer';
import {
  PackageStatus,
  PackageType,
} from '../../drizzle/schema/packages.schema';
import { TaxType } from '../../shared/types/common.enum';

export class CreatePackageDto {
  @ApiProperty({ description: 'Package code', maxLength: 191 })
  @IsNotEmpty()
  @IsString()
  @MaxLength(191)
  packageCode: string;

  @ApiProperty({ description: 'Package name', maxLength: 191 })
  @IsNotEmpty()
  @IsString()
  @MaxLength(191)
  name: string;

  @ApiProperty({
    description: 'Short description of the package',
    required: false,
  })
  @IsOptional()
  @IsString()
  shortDescription?: string;

  @ApiProperty({
    description: 'Package description',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    enum: PackageType,
    enumName: 'PackageType',
    description: 'Package type',
    default: PackageType.TOUR_PACKAGE,
  })
  @IsEnum(PackageType, {
    message: 'Type must be a valid package type',
  })
  @IsOptional()
  type?: PackageType;

  @ApiPropertyOptional({
    enum: PackageStatus,
    enumName: 'PackageStatus',
    description: 'Package status',
    default: PackageStatus.ACTIVE,
  })
  @IsEnum(PackageStatus, {
    message: 'Status must be a valid package status',
  })
  @IsOptional()
  status?: PackageStatus;

  @ApiProperty({
    description: 'Base price of the package',
    example: '100.00',
  })
  @IsDecimal(
    { decimal_digits: '0,2' },
    {
      message: 'Base price must be a valid decimal with up to 2 decimal places',
    },
  )
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value;
    }
    return value?.toString();
  })
  basePrice: string;

  @ApiProperty({
    description: 'Sale price of the package',
    example: '90.00',
  })
  @IsDecimal(
    { decimal_digits: '0,2' },
    {
      message: 'Sale price must be a valid decimal with up to 2 decimal places',
    },
  )
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value;
    }
    return value?.toString();
  })
  salePrice: string;

  @ApiProperty({
    description: 'Cost of the package',
    example: '70.00',
  })
  @IsDecimal(
    { decimal_digits: '0,2' },
    { message: 'Cost must be a valid decimal with up to 2 decimal places' },
  )
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value;
    }
    return value?.toString();
  })
  cost: string;

  @ApiProperty({
    description: 'Income account ID',
  })
  @IsNotEmpty()
  @IsUUID()
  incomeAccountId: string;

  @ApiProperty({
    description: 'Expense account ID',
  })
  @IsNotEmpty()
  @IsUUID()
  expenseAccountId: string;

  // SEO fields
  @ApiPropertyOptional({
    description: 'SEO title for search engines',
    maxLength: 191,
  })
  @IsOptional()
  @IsString()
  @MaxLength(191)
  seoTitle?: string;

  @ApiPropertyOptional({
    description: 'SEO description for search engines',
  })
  @IsOptional()
  @IsString()
  seoDescription?: string;

  @ApiPropertyOptional({
    description: 'SEO keywords for search engines',
    type: [String],
    example: ['tour', 'package', 'travel'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      // If it's a single string, try to parse as JSON or wrap in array
      try {
        const parsed = JSON.parse(value);
        return Array.isArray(parsed) ? parsed : [value];
      } catch {
        return [value];
      }
    }
    if (Array.isArray(value)) {
      return value;
    }
    return value;
  })
  seoKeywords?: string[];

  @ApiPropertyOptional({
    enum: TaxType,
    enumName: 'TaxType',
    description: 'Tax type',
    default: TaxType.INCLUSIVE,
  })
  @IsEnum(TaxType, {
    message: 'Tax type must be a valid tax type',
  })
  @IsOptional()
  taxType?: TaxType;

  @ApiProperty({
    description: 'Default tax rate ID',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  defaultTaxRateId?: string;

  // Custom key fields
  @ApiPropertyOptional({
    description: 'Custom key field 1',
  })
  @IsOptional()
  @IsString()
  key1?: string;

  @ApiPropertyOptional({
    description: 'Custom key field 2',
  })
  @IsOptional()
  @IsString()
  key2?: string;

  @ApiPropertyOptional({
    description: 'Custom key field 3',
  })
  @IsOptional()
  @IsString()
  key3?: string;

  @ApiPropertyOptional({
    description: 'Custom key field 4',
  })
  @IsOptional()
  @IsString()
  key4?: string;

  @ApiPropertyOptional({
    description: 'Custom key field 5',
  })
  @IsOptional()
  @IsString()
  key5?: string;

  @ApiPropertyOptional({
    description: 'Custom key field 6',
  })
  @IsOptional()
  @IsString()
  key6?: string;

  @ApiPropertyOptional({
    description: 'Custom key field 7',
  })
  @IsOptional()
  @IsString()
  key7?: string;

  @ApiPropertyOptional({
    description: 'Custom key field 8',
  })
  @IsOptional()
  @IsString()
  key8?: string;

  @ApiPropertyOptional({
    description: 'Custom key field 9',
  })
  @IsOptional()
  @IsString()
  key9?: string;

  @ApiPropertyOptional({
    description: 'Custom key field 10',
  })
  @IsOptional()
  @IsString()
  key10?: string;

  @ApiPropertyOptional({
    example: 0,
    description:
      'Index of the image file in the images array to use for this package (0-based)',
    minimum: 0,
  })
  @IsNumber({}, { message: 'Image index must be a number' })
  @Min(0, { message: 'Image index must be 0 or greater' })
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      const parsed = parseInt(value, 10);
      return isNaN(parsed) ? value : parsed;
    }
    return value;
  })
  imageIndex?: number;

  @ApiPropertyOptional({
    example: 0,
    description:
      'Index of the Open Graph image file in the images array to use for social media sharing (0-based)',
    minimum: 0,
  })
  @IsNumber({}, { message: 'OG image index must be a number' })
  @Min(0, { message: 'OG image index must be 0 or greater' })
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      const parsed = parseInt(value, 10);
      return isNaN(parsed) ? value : parsed;
    }
    return value;
  })
  ogImageIndex?: number;
}
