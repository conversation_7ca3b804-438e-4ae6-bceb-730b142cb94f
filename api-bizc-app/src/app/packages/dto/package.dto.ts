import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  PackageStatus,
  PackageType,
} from '../../drizzle/schema/packages.schema';
import { TaxType } from '../../shared/types/common.enum';

export class PackageDto {
  @ApiProperty({ description: 'Unique identifier for the package' })
  id: string;

  @ApiProperty({ description: 'Business ID the package belongs to' })
  businessId: string;

  @ApiProperty({ description: 'Package code' })
  packageCode: string;

  @ApiProperty({ description: 'Package name' })
  name: string;

  @ApiProperty({
    description: 'Short description of the package',
    required: false,
    nullable: true,
  })
  shortDescription?: string;

  @ApiProperty({
    description: 'Package description',
    required: false,
    nullable: true,
  })
  description?: string;

  @ApiProperty({
    example: PackageType.TOUR_PACKAGE,
    enum: PackageType,
    enumName: 'PackageType',
    description: 'Package type',
  })
  type: PackageType;

  @ApiProperty({
    example: PackageStatus.ACTIVE,
    enum: PackageStatus,
    enumName: 'PackageStatus',
    description: 'Package status',
  })
  status: PackageStatus;

  @ApiProperty({
    example: '100.00',
    description: 'Base price of the package',
  })
  basePrice: string;

  @ApiProperty({
    example: '90.00',
    description: 'Sale price of the package',
  })
  salePrice: string;

  @ApiProperty({
    example: '70.00',
    description: 'Cost of the package',
  })
  cost: string;

  @ApiProperty({
    description: 'Income account ID',
  })
  incomeAccountId: string;

  @ApiProperty({
    description: 'Expense account ID',
  })
  expenseAccountId: string;

  @ApiProperty({
    description: 'SEO title for search engines',
    required: false,
    nullable: true,
  })
  seoTitle?: string;

  @ApiProperty({
    description: 'SEO description for search engines',
    required: false,
    nullable: true,
  })
  seoDescription?: string;

  @ApiProperty({
    description: 'SEO keywords for search engines',
    required: false,
    nullable: true,
    type: [String],
    example: ['tour', 'package', 'travel'],
  })
  seoKeywords?: string[];

  @ApiPropertyOptional({
    description: 'Open Graph image for social media sharing',
    type: String,
  })
  ogImage?: string;

  @ApiProperty({
    example: 0,
    description: 'Display order position for the package',
  })
  position: number;

  @ApiProperty({
    example: TaxType.INCLUSIVE,
    enum: TaxType,
    enumName: 'TaxType',
    description: 'Tax type',
  })
  taxType: TaxType;

  @ApiProperty({
    description: 'Default tax rate ID',
    required: false,
    nullable: true,
  })
  defaultTaxRateId?: string;

  // Custom key fields
  @ApiProperty({
    description: 'Custom key field 1',
    required: false,
    nullable: true,
  })
  key1?: string;

  @ApiProperty({
    description: 'Custom key field 2',
    required: false,
    nullable: true,
  })
  key2?: string;

  @ApiProperty({
    description: 'Custom key field 3',
    required: false,
    nullable: true,
  })
  key3?: string;

  @ApiProperty({
    description: 'Custom key field 4',
    required: false,
    nullable: true,
  })
  key4?: string;

  @ApiProperty({
    description: 'Custom key field 5',
    required: false,
    nullable: true,
  })
  key5?: string;

  @ApiProperty({
    description: 'Custom key field 6',
    required: false,
    nullable: true,
  })
  key6?: string;

  @ApiProperty({
    description: 'Custom key field 7',
    required: false,
    nullable: true,
  })
  key7?: string;

  @ApiProperty({
    description: 'Custom key field 8',
    required: false,
    nullable: true,
  })
  key8?: string;

  @ApiProperty({
    description: 'Custom key field 9',
    required: false,
    nullable: true,
  })
  key9?: string;

  @ApiProperty({
    description: 'Custom key field 10',
    required: false,
    nullable: true,
  })
  key10?: string;

  @ApiProperty({
    example: 'John Doe',
    description: 'Name of the user who created the package',
  })
  createdBy: string;

  @ApiProperty({
    example: 'Jane Smith',
    description: 'Name of the user who last updated the package',
    required: false,
    nullable: true,
  })
  updatedBy?: string;

  @ApiProperty({
    example: '2023-01-01T00:00:00Z',
    description: 'Creation timestamp',
  })
  createdAt: Date;

  @ApiProperty({
    example: '2023-01-01T00:00:00Z',
    description: 'Last update timestamp',
  })
  updatedAt: Date;
}
