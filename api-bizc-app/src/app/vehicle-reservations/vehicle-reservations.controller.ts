import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  Query,
  UseGuards,
} from '@nestjs/common';
import { VehicleReservationsService } from './vehicle-reservations.service';
import { CreateVehicleReservationDto } from './dto/create-vehicle-reservation.dto';
import { UpdateVehicleReservationDto } from './dto/update-vehicle-reservation.dto';
import { VehicleReservationDto } from './dto/vehicle-reservation.dto';
import { VehicleReservationSlimDto } from './dto/vehicle-reservation-slim.dto';
import { VehicleReservationIdResponseDto } from './dto/vehicle-reservation-id-response.dto';
import { BulkVehicleReservationIdsResponseDto } from './dto/bulk-vehicle-reservation-ids-response.dto';
import { BulkCreateVehicleReservationDto } from './dto/bulk-create-vehicle-reservation.dto';
import { DeleteVehicleReservationResponseDto } from './dto/delete-vehicle-reservation-response.dto';
import { BulkDeleteVehicleReservationDto } from './dto/bulk-delete-vehicle-reservation.dto';
import { BulkDeleteVehicleReservationResponseDto } from './dto/bulk-delete-vehicle-reservation-response.dto';
import { PaginatedVehicleReservationsResponseDto } from './dto/paginated-vehicle-reservations-response.dto';
import {
  BulkUpdateVehicleReservationStatusDto,
  BulkUpdateVehicleReservationStatusResponseDto,
} from './dto/bulk-update-vehicle-reservation-status.dto';
import { VehicleReservationNumberAvailabilityResponseDto } from './dto/check-vehicle-reservation-number.dto';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { Permission } from '../shared/types/permission.enum';

@ApiTags('vehicle-reservations')
@Controller('vehicle-reservations')
@UseGuards(PermissionsGuard)
export class VehicleReservationsController {
  constructor(
    private readonly vehicleReservationsService: VehicleReservationsService,
  ) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.VEHICLE_RESERVATION_CREATE)
  @ApiOperation({ summary: 'Create a new vehicle reservation' })
  @ApiBody({
    description: 'Vehicle reservation creation data',
    type: CreateVehicleReservationDto,
  })
  @ApiResponse({
    status: 201,
    description: 'The vehicle reservation has been successfully created',
    type: VehicleReservationIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Reservation number already exists',
  })
  create(
    @Request() req,
    @Body() createVehicleReservationDto: CreateVehicleReservationDto,
  ): Promise<VehicleReservationIdResponseDto> {
    return this.vehicleReservationsService.createAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      createVehicleReservationDto,
    );
  }

  @Post('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.VEHICLE_RESERVATION_CREATE)
  @ApiOperation({ summary: 'Bulk create vehicle reservations' })
  @ApiBody({
    description: 'Bulk vehicle reservation creation data',
    type: BulkCreateVehicleReservationDto,
  })
  @ApiResponse({
    status: 201,
    description: 'The vehicle reservations have been successfully created',
    type: BulkVehicleReservationIdsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or duplicate reservation numbers',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - One or more reservation numbers already exist',
  })
  async bulkCreate(
    @Request() req,
    @Body() bulkCreateVehicleReservationDto: BulkCreateVehicleReservationDto,
  ): Promise<BulkVehicleReservationIdsResponseDto> {
    const results = [];
    const failed = [];

    for (
      let i = 0;
      i < bulkCreateVehicleReservationDto.vehicleReservations.length;
      i++
    ) {
      const reservation =
        bulkCreateVehicleReservationDto.vehicleReservations[i];
      try {
        const result = await this.vehicleReservationsService.createAndReturnId(
          req.user.id,
          req.user.activeBusinessId,
          reservation,
        );
        results.push(result.id);
      } catch (error) {
        failed.push({
          index: i,
          reservationNumber: reservation.reservationNumber,
          error: error.message,
        });
      }
    }

    return {
      ids: results,
      created: results.length,
      message: `Successfully created ${results.length} vehicle reservations`,
      failed: failed.length > 0 ? failed : undefined,
    };
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.VEHICLE_RESERVATION_READ)
  @ApiOperation({
    summary: 'Get all vehicle reservations for the active business',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'from',
    description: 'Filter from date (YYYY-MM-DD format)',
    required: false,
    type: String,
    example: '2025-05-24',
  })
  @ApiQuery({
    name: 'to',
    description: 'Filter to date (YYYY-MM-DD format)',
    required: false,
    type: String,
    example: '2025-05-24',
  })
  @ApiQuery({
    name: 'reservationNumber',
    description: 'Filter by reservation number',
    required: false,
    type: String,
    example: 'VR-2025-001',
  })
  @ApiQuery({
    name: 'referenceNumber',
    description: 'Filter by reference number',
    required: false,
    type: String,
    example: 'EXT-REF-001',
  })
  @ApiQuery({
    name: 'status',
    description:
      'Filter by status (comma-separated for multiple values). Supports URL encoding: status=PENDING%2CCONFIRMED',
    required: false,
    type: String,
    example: 'PENDING,CONFIRMED',
  })
  @ApiQuery({
    name: 'paymentStatus',
    description:
      'Filter by payment status (comma-separated for multiple values). Supports URL encoding: paymentStatus=PENDING%2CPAID',
    required: false,
    type: String,
    example: 'PENDING,PAID',
  })
  @ApiQuery({
    name: 'customerId',
    description: 'Filter by customer ID',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'filters',
    description:
      'Advanced filters as JSON string with operator support. Supported operators: iLike (contains), notILike (does not contain), eq (is), ne (is not), isEmpty (is empty), isNotEmpty (is not empty)',
    required: false,
    type: String,
    example:
      '[{"id":"reservationNumber","value":"VR-2025","operator":"iLike","type":"text","rowId":"1"},{"id":"status","value":"CONFIRMED","operator":"eq","type":"select","rowId":"2"}]',
  })
  @ApiQuery({
    name: 'joinOperator',
    description: 'Join operator for advanced filters',
    required: false,
    type: String,
    enum: ['and', 'or'],
    example: 'and',
  })
  @ApiQuery({
    name: 'sort',
    description:
      'Sort configuration as JSON string. Supported fields: reservationNumber, pickUpDate, returnDate, status, paymentStatus, total, createdAt, updatedAt',
    required: false,
    type: String,
    example: '[{"id":"pickUpDate","desc":false}]',
  })
  @ApiResponse({
    status: 200,
    description:
      "Returns all vehicle reservations for the user's active business with pagination",
    type: PaginatedVehicleReservationsResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('reservationNumber') reservationNumber?: string,
    @Query('referenceNumber') referenceNumber?: string,
    @Query('status') status?: string,
    @Query('paymentStatus') paymentStatus?: string,
    @Query('customerId') customerId?: string,
    @Query('filters') filters?: string,
    @Query('joinOperator') joinOperator?: 'and' | 'or',
    @Query('sort') sort?: string,
  ): Promise<PaginatedVehicleReservationsResponseDto> {
    return this.vehicleReservationsService.findAllOptimized(
      req.user.activeBusinessId,
      page ? parseInt(page.toString()) : undefined,
      limit ? parseInt(limit.toString()) : undefined,
      from,
      to,
      reservationNumber,
      referenceNumber,
      status,
      paymentStatus,
      customerId,
      filters,
      joinOperator,
      sort,
    );
  }

  @Get('check-reservation-number-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.VEHICLE_RESERVATION_READ)
  @ApiOperation({
    summary: 'Check if a vehicle reservation number is available',
  })
  @ApiQuery({
    name: 'reservationNumber',
    description: 'Reservation number to check',
    required: true,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Returns whether the reservation number is available',
    type: VehicleReservationNumberAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async checkReservationNumberAvailability(
    @Request() req,
    @Query('reservationNumber') reservationNumber: string,
  ): Promise<VehicleReservationNumberAvailabilityResponseDto> {
    const result =
      await this.vehicleReservationsService.checkReservationNumberAvailability(
        req.user.activeBusinessId,
        reservationNumber,
      );

    return {
      available: result.available,
      reservationNumber,
      message: result.available
        ? 'Reservation number is available'
        : 'Reservation number is already taken',
    };
  }

  @Get('check-reference-number-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.VEHICLE_RESERVATION_READ)
  @ApiOperation({
    summary: 'Check if a vehicle reservation reference number is available',
  })
  @ApiQuery({
    name: 'referenceNumber',
    required: true,
    description: 'The reference number to check for availability',
    example: 'EXT-REF-001',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns reference number availability',
    type: VehicleReservationNumberAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async checkReferenceNumberAvailability(
    @Request() req,
    @Query('referenceNumber') referenceNumber: string,
  ): Promise<VehicleReservationNumberAvailabilityResponseDto> {
    const result =
      await this.vehicleReservationsService.checkReferenceNumberAvailability(
        req.user.activeBusinessId,
        referenceNumber,
      );

    return {
      available: result.available,
      reservationNumber: referenceNumber,
      message: result.available
        ? 'Reference number is available'
        : 'Reference number is already taken',
    };
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.VEHICLE_RESERVATION_READ)
  @ApiOperation({ summary: 'Get all vehicle reservations in slim format' })
  @ApiResponse({
    status: 200,
    description: 'All vehicle reservations returned successfully',
    type: [VehicleReservationSlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllSlim(@Request() req): Promise<VehicleReservationSlimDto[]> {
    return this.vehicleReservationsService.findAllSlim(
      req.user.activeBusinessId,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.VEHICLE_RESERVATION_READ)
  @ApiOperation({ summary: 'Get a vehicle reservation by ID' })
  @ApiParam({
    name: 'id',
    description: 'Vehicle reservation ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the vehicle reservation with all details',
    type: VehicleReservationDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Vehicle reservation not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to this vehicle reservation',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findOne(
    @Request() req,
    @Param('id') id: string,
  ): Promise<VehicleReservationDto> {
    return this.vehicleReservationsService.findOne(id);
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.VEHICLE_RESERVATION_UPDATE)
  @ApiOperation({ summary: 'Update a vehicle reservation' })
  @ApiParam({
    name: 'id',
    description: 'Vehicle reservation ID',
  })
  @ApiBody({
    description: 'Vehicle reservation update data',
    type: UpdateVehicleReservationDto,
  })
  @ApiResponse({
    status: 200,
    description: 'The vehicle reservation has been successfully updated',
    type: VehicleReservationIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 404,
    description: 'Vehicle reservation not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to this vehicle reservation',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async update(
    @Request() req,
    @Param('id') id: string,
    @Body() updateVehicleReservationDto: UpdateVehicleReservationDto,
  ): Promise<VehicleReservationIdResponseDto> {
    return this.vehicleReservationsService.update(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateVehicleReservationDto,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.VEHICLE_RESERVATION_DELETE)
  @ApiOperation({ summary: 'Bulk delete vehicle reservations' })
  @ApiBody({
    description: 'Array of vehicle reservation IDs to delete',
    type: BulkDeleteVehicleReservationDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Vehicle reservations have been successfully deleted',
    type: BulkDeleteVehicleReservationResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or reservations not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'One or more vehicle reservations not found',
  })
  async bulkDelete(
    @Request() req,
    @Body() bulkDeleteVehicleReservationDto: BulkDeleteVehicleReservationDto,
  ): Promise<BulkDeleteVehicleReservationResponseDto> {
    return this.vehicleReservationsService.bulkDelete(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteVehicleReservationDto.vehicleReservationIds,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.VEHICLE_RESERVATION_DELETE)
  @ApiOperation({ summary: 'Delete a vehicle reservation' })
  @ApiParam({
    name: 'id',
    description: 'Vehicle reservation ID',
  })
  @ApiResponse({
    status: 200,
    description: 'The vehicle reservation has been successfully deleted',
    type: DeleteVehicleReservationResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Vehicle reservation not found',
  })
  @ApiResponse({
    status: 401,
    description:
      'Unauthorized - Access denied to delete this vehicle reservation',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  remove(
    @Request() req,
    @Param('id') id: string,
  ): Promise<DeleteVehicleReservationResponseDto> {
    return this.vehicleReservationsService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
    );
  }

  @Patch('bulk-status')
  @ApiBearerAuth()
  @RequirePermissions(Permission.VEHICLE_RESERVATION_UPDATE)
  @ApiOperation({ summary: 'Bulk update vehicle reservation status' })
  @ApiBody({
    description: 'Array of vehicle reservation IDs and status to update',
    type: BulkUpdateVehicleReservationStatusDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Vehicle reservation status has been successfully updated',
    type: BulkUpdateVehicleReservationStatusResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or reservations not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async bulkUpdateStatus(
    @Request() req,
    @Body() bulkUpdateStatusDto: BulkUpdateVehicleReservationStatusDto,
  ): Promise<BulkUpdateVehicleReservationStatusResponseDto> {
    return this.vehicleReservationsService.bulkUpdateStatus(
      req.user.id,
      req.user.activeBusinessId,
      bulkUpdateStatusDto.vehicleReservationIds,
      bulkUpdateStatusDto.status,
    );
  }
}
