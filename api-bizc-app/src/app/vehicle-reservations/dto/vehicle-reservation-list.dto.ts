import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { VehicleReservationStatus } from '../../shared/types/vehicle.enum';
import { PaymentStatus } from '../../shared/types/common.enum';
import { ReservationSource } from '../../shared/types/accommodation.enum';

export class VehicleReservationListDto {
  @ApiProperty({ description: 'Vehicle reservation ID' })
  id: string;

  @ApiProperty({ description: 'Reservation number' })
  reservationNumber: string;

  @ApiPropertyOptional({ description: 'External booking reference number' })
  referenceNumber?: string;

  @ApiPropertyOptional({ description: 'Customer ID' })
  customerId?: string;

  @ApiPropertyOptional({ description: 'Customer information' })
  customer?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
  };

  @ApiProperty({ description: 'Pick up date and time' })
  pickUpDate: string;

  @ApiProperty({ description: 'Return date and time' })
  returnDate: string;

  @ApiProperty({
    enum: VehicleReservationStatus,
    description: 'Reservation status',
  })
  status: VehicleReservationStatus;

  @ApiPropertyOptional({
    enum: ReservationSource,
    description: 'Source of the reservation',
  })
  reservationSource?: ReservationSource;

  @ApiProperty({ enum: PaymentStatus, description: 'Payment status' })
  paymentStatus: PaymentStatus;

  @ApiProperty({ description: 'Daily rate for the reservation' })
  dailyRate: string;

  @ApiProperty({ description: 'Total amount' })
  total: string;

  @ApiPropertyOptional({ description: 'Balance due' })
  balanceDue?: string;

  @ApiProperty({ description: 'Number of vehicles in reservation' })
  vehicleCount: number;

  @ApiProperty({ description: 'Vehicle information summary' })
  vehicleSummary: string;

  @ApiProperty({ description: 'Created at timestamp' })
  createdAt: string;

  @ApiProperty({ description: 'Updated at timestamp' })
  updatedAt: string;

  @ApiPropertyOptional({ description: 'Created by user information' })
  createdBy?: {
    id: string;
    name: string;
    avatar?: string;
  };

  @ApiPropertyOptional({ description: 'Updated by user information' })
  updatedBy?: {
    id: string;
    name: string;
    avatar?: string;
  };
}
