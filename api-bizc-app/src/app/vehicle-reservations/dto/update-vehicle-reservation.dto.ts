import { PartialType } from '@nestjs/swagger';
import {
  CreateVehicleReservationDto,
  CreateVehicleReservationVehicleDto,
} from './create-vehicle-reservation.dto';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsDateString, IsNumber, Min, Max } from 'class-validator';

export class UpdateVehicleReservationVehicleDto extends PartialType(
  CreateVehicleReservationVehicleDto,
) {
  @ApiPropertyOptional({
    description: 'Vehicle reservation vehicle ID (for updates)',
  })
  @IsOptional()
  id?: string;

  @ApiPropertyOptional({
    description: 'Pick up odometer reading',
    minimum: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  pickUpOdometer?: number;

  @ApiPropertyOptional({
    description: 'Return odometer reading',
    minimum: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  returnOdometer?: number;

  @ApiPropertyOptional({
    description: 'Pick up fuel level (0-100%)',
    minimum: 0,
    maximum: 100,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  pickUpFuelLevel?: number;

  @ApiPropertyOptional({
    description: 'Return fuel level (0-100%)',
    minimum: 0,
    maximum: 100,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  returnFuelLevel?: number;

  @ApiPropertyOptional({
    description: 'Pick up condition notes',
  })
  @IsOptional()
  pickUpConditionNotes?: string;

  @ApiPropertyOptional({
    description: 'Return condition notes',
  })
  @IsOptional()
  returnConditionNotes?: string;
}

export class UpdateVehicleReservationDto extends PartialType(
  CreateVehicleReservationDto,
) {
  @ApiPropertyOptional({
    description: 'Actual pick up time',
  })
  @IsOptional()
  @IsDateString()
  actualPickUpTime?: string;

  @ApiPropertyOptional({
    description: 'Actual return time',
  })
  @IsOptional()
  @IsDateString()
  actualReturnTime?: string;

  @ApiPropertyOptional({
    description: 'Cancellation reason',
  })
  @IsOptional()
  cancellationReason?: string;

  @ApiPropertyOptional({
    description: 'Cancellation date',
  })
  @IsOptional()
  @IsDateString()
  cancellationDate?: string;

  @ApiPropertyOptional({
    description: 'Flight arrival date for AIRPORT pickup',
  })
  @IsOptional()
  @IsDateString()
  flightArrivalDate?: string;

  @ApiPropertyOptional({
    description: 'Flight arrival time (HH:MM format)',
  })
  @IsOptional()
  flightArrivalTime?: string;

  @ApiPropertyOptional({
    description: 'Flight departure date for AIRPORT return',
  })
  @IsOptional()
  @IsDateString()
  flightDepartureDate?: string;

  @ApiPropertyOptional({
    description: 'Flight departure time (HH:MM format)',
  })
  @IsOptional()
  flightDepartureTime?: string;

  @ApiPropertyOptional({
    description: 'Arrival flight number',
  })
  @IsOptional()
  arrivalFlightNumber?: string;

  @ApiPropertyOptional({
    description: 'Departure flight number',
  })
  @IsOptional()
  departureFlightNumber?: string;

  @ApiPropertyOptional({
    description: 'Arrival airline',
  })
  @IsOptional()
  arrivalAirline?: string;

  @ApiPropertyOptional({
    description: 'Departure airline',
  })
  @IsOptional()
  departureAirline?: string;

  @ApiPropertyOptional({
    description: 'Array of vehicles for this reservation (replaces existing)',
    type: [UpdateVehicleReservationVehicleDto],
  })
  @IsOptional()
  vehicles?: CreateVehicleReservationVehicleDto[];
}
