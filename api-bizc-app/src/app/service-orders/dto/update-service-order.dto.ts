import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsUUID,
  IsDateString,
  IsEnum,
  IsDecimal,
  IsArray,
  ValidateNested,
  IsNumber,
  IsBoolean,
  Max<PERSON>ength,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';
import {
  TaxType,
  StandardUnitOfMeasure,
  DiscountType,
} from '../../shared/types';
import {
  ServiceOrderLineType,
  ServiceOrderLineStaffRoleType,
  ServiceOrderPaymentStatus,
  CustomerApprovalStatus,
} from '../../drizzle/schema/service-orders.schema';
import { TaskStatus, TaskPriority } from '../../drizzle/schema/tasks.schema';

export class UpdateServiceOrderItemDto {
  @ApiPropertyOptional({ description: 'Service order item ID (for updates)' })
  @IsOptional()
  @IsUUID()
  id?: string;

  @ApiPropertyOptional({ description: 'Item/Product ID' })
  @IsOptional()
  @IsUUID()
  itemId?: string;

  @ApiPropertyOptional({ description: 'Quantity required' })
  @IsOptional()
  @IsDecimal({ decimal_digits: '0,3' })
  quantity?: string;

  @ApiPropertyOptional({ description: 'Unit price' })
  @IsOptional()
  @IsDecimal({ decimal_digits: '0,2' })
  unitPrice?: string;

  @ApiPropertyOptional({ description: 'Total price for this item' })
  @IsOptional()
  @IsDecimal({ decimal_digits: '0,2' })
  totalPrice?: string;

  @ApiPropertyOptional({
    description: 'Standard unit of measure',
    enum: StandardUnitOfMeasure,
    enumName: 'StandardUnitOfMeasure',
  })
  @IsOptional()
  @IsEnum(StandardUnitOfMeasure)
  standardUnitOfMeasure?: StandardUnitOfMeasure;

  @ApiPropertyOptional({ description: 'Custom unit ID' })
  @IsOptional()
  @IsUUID()
  customUnitId?: string;

  @ApiPropertyOptional({ description: 'Item description' })
  @IsOptional()
  @IsString()
  description?: string;
}

export class UpdateServiceOrderDiagnosticDto {
  @ApiPropertyOptional({
    description: 'Service order diagnostic ID (for updates)',
  })
  @IsOptional()
  @IsUUID()
  id?: string;

  @ApiPropertyOptional({ description: 'Test performed' })
  @IsOptional()
  @IsString()
  testPerformed?: string;

  @ApiPropertyOptional({ description: 'Test result' })
  @IsOptional()
  @IsString()
  testResult?: string;

  @ApiPropertyOptional({ description: 'Issue found' })
  @IsOptional()
  @IsString()
  issueFound?: string;

  @ApiPropertyOptional({ description: 'Recommended action' })
  @IsOptional()
  @IsString()
  recommendedAction?: string;

  @ApiPropertyOptional({ description: 'Requires parts' })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  requiresParts?: boolean;

  @ApiPropertyOptional({ description: 'Parts needed' })
  @IsOptional()
  @IsString()
  partsNeeded?: string;

  @ApiPropertyOptional({ description: 'Estimated repair time in hours' })
  @IsOptional()
  @IsDecimal({ decimal_digits: '0,2' })
  estimatedRepairTime?: string;

  @ApiPropertyOptional({ description: 'Diagnostic notes' })
  @IsOptional()
  @IsString()
  notes?: string;
}

export class UpdateServiceOrderLineStaffTaskDto {
  @ApiPropertyOptional({ description: 'Task ID (for updates)' })
  @IsOptional()
  @IsUUID()
  id?: string;

  @ApiPropertyOptional({ description: 'Task title', maxLength: 191 })
  @IsOptional()
  @IsString()
  @MaxLength(191)
  title?: string;

  @ApiPropertyOptional({ description: 'Task description' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: 'Task due date in ISO format' })
  @IsOptional()
  @IsDateString()
  dueDate?: string;

  @ApiPropertyOptional({
    enum: TaskPriority,
    enumName: 'TaskPriority',
    description: 'Task priority',
  })
  @IsOptional()
  @IsEnum(TaskPriority)
  priority?: TaskPriority;

  @ApiPropertyOptional({
    enum: TaskStatus,
    enumName: 'TaskStatus',
    description: 'Task status',
  })
  @IsOptional()
  @IsEnum(TaskStatus)
  status?: TaskStatus;
}

export class UpdateServiceOrderLineStaffDto {
  @ApiPropertyOptional({
    description: 'Service order line staff ID (for updates)',
  })
  @IsOptional()
  @IsUUID()
  id?: string;

  @ApiPropertyOptional({ description: 'Staff member ID' })
  @IsOptional()
  @IsUUID()
  staffId?: string;

  @ApiPropertyOptional({
    description: 'Staff role type',
    enum: ServiceOrderLineStaffRoleType,
    enumName: 'ServiceOrderLineStaffRoleType',
  })
  @IsOptional()
  @IsEnum(ServiceOrderLineStaffRoleType)
  roleType?: ServiceOrderLineStaffRoleType;

  @ApiPropertyOptional({ description: 'Start time' })
  @IsOptional()
  @IsDateString()
  startTime?: string;

  @ApiPropertyOptional({ description: 'End time' })
  @IsOptional()
  @IsDateString()
  endTime?: string;

  @ApiPropertyOptional({ description: 'Hours worked' })
  @IsOptional()
  @IsDecimal({ decimal_digits: '0,2' })
  hoursWorked?: string;

  @ApiPropertyOptional({ description: 'Staff notes' })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiPropertyOptional({
    description: 'Task for this staff assignment',
    type: UpdateServiceOrderLineStaffTaskDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateServiceOrderLineStaffTaskDto)
  task?: UpdateServiceOrderLineStaffTaskDto;
}

export class UpdateServiceOrderLineDto {
  @ApiPropertyOptional({ description: 'Service order line ID (for updates)' })
  @IsOptional()
  @IsUUID()
  id?: string;

  @ApiPropertyOptional({ description: 'Service ID' })
  @IsOptional()
  @IsUUID()
  serviceId?: string;

  @ApiPropertyOptional({
    description: 'Line type',
    enum: ServiceOrderLineType,
    enumName: 'ServiceOrderLineType',
  })
  @IsOptional()
  @IsEnum(ServiceOrderLineType)
  lineType?: ServiceOrderLineType;

  @ApiPropertyOptional({ description: 'Quantity' })
  @IsOptional()
  @IsNumber()
  quantity?: number;

  @ApiPropertyOptional({ description: 'Unit price' })
  @IsOptional()
  @IsDecimal({ decimal_digits: '0,2' })
  unitPrice?: string;

  @ApiPropertyOptional({
    description: 'Discount type',
    enum: DiscountType,
    enumName: 'DiscountType',
  })
  @IsOptional()
  @IsEnum(DiscountType)
  discountType?: DiscountType;

  @ApiPropertyOptional({ description: 'Discount amount' })
  @IsOptional()
  @IsDecimal({ decimal_digits: '0,2' })
  discountAmount?: string;

  @ApiPropertyOptional({ description: 'Line total' })
  @IsOptional()
  @IsDecimal({ decimal_digits: '0,2' })
  lineTotal?: string;

  @ApiPropertyOptional({ description: 'Is warranty item' })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  isWarrantyItem?: boolean;

  @ApiPropertyOptional({ description: 'Line notes' })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiPropertyOptional({
    description: 'Staff assignments for this line',
    type: [UpdateServiceOrderLineStaffDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateServiceOrderLineStaffDto)
  staff?: UpdateServiceOrderLineStaffDto[];
}

export class UpdateServiceOrderDto {
  @ApiPropertyOptional({ description: 'Service order number', maxLength: 50 })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  orderNumber?: string;

  @ApiPropertyOptional({ description: 'Customer ID' })
  @IsOptional()
  @IsUUID()
  customerId?: string;

  @ApiPropertyOptional({ description: 'Appointment ID' })
  @IsOptional()
  @IsUUID()
  appointmentId?: string;

  @ApiPropertyOptional({ description: 'Status ID' })
  @IsOptional()
  @IsUUID()
  statusId?: string;

  @ApiPropertyOptional({ description: 'Priority ID' })
  @IsOptional()
  @IsUUID()
  priorityId?: string;

  @ApiPropertyOptional({ description: 'Service type ID' })
  @IsOptional()
  @IsUUID()
  serviceTypeId?: string;

  @ApiPropertyOptional({ description: 'Location ID' })
  @IsOptional()
  @IsUUID()
  locationId?: string;

  @ApiPropertyOptional({ description: 'Order date' })
  @IsOptional()
  @IsDateString()
  orderDate?: string;

  @ApiPropertyOptional({ description: 'Scheduled date' })
  @IsOptional()
  @IsDateString()
  scheduledDate?: string;

  @ApiPropertyOptional({ description: 'Actual start date' })
  @IsOptional()
  @IsDateString()
  actualStartDate?: string;

  @ApiPropertyOptional({ description: 'Actual end date' })
  @IsOptional()
  @IsDateString()
  actualEndDate?: string;

  @ApiPropertyOptional({ description: 'Promised date' })
  @IsOptional()
  @IsDateString()
  promisedDate?: string;

  @ApiPropertyOptional({ description: 'Service order description' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: 'Estimated cost' })
  @IsOptional()
  @IsDecimal({ decimal_digits: '0,2' })
  estimatedCost?: string;

  @ApiPropertyOptional({ description: 'Actual cost' })
  @IsOptional()
  @IsDecimal({ decimal_digits: '0,2' })
  actualCost?: string;

  @ApiPropertyOptional({ description: 'Total amount' })
  @IsOptional()
  @IsDecimal({ decimal_digits: '0,2' })
  totalAmount?: string;

  @ApiPropertyOptional({
    description: 'Tax type',
    enum: TaxType,
    enumName: 'TaxType',
  })
  @IsOptional()
  @IsEnum(TaxType)
  taxType?: TaxType;

  @ApiPropertyOptional({ description: 'Default tax rate ID' })
  @IsOptional()
  @IsUUID()
  defaultTaxRateId?: string;

  @ApiPropertyOptional({ description: 'Staff member who received the order' })
  @IsOptional()
  @IsUUID()
  receivedById?: string;

  @ApiPropertyOptional({
    description: 'Customer approval status',
    enum: CustomerApprovalStatus,
    enumName: 'CustomerApprovalStatus',
  })
  @IsOptional()
  @IsEnum(CustomerApprovalStatus)
  customerApprovalStatus?: CustomerApprovalStatus;

  @ApiPropertyOptional({ description: 'Customer approval date' })
  @IsOptional()
  @IsDateString()
  customerApprovalDate?: string;

  @ApiPropertyOptional({ description: 'Cancellation reason' })
  @IsOptional()
  @IsString()
  cancellationReason?: string;

  @ApiPropertyOptional({
    description: 'Payment status',
    enum: ServiceOrderPaymentStatus,
    enumName: 'ServiceOrderPaymentStatus',
  })
  @IsOptional()
  @IsEnum(ServiceOrderPaymentStatus)
  paymentStatus?: ServiceOrderPaymentStatus;

  // Related data arrays
  @ApiPropertyOptional({
    description: 'Service order items',
    type: [UpdateServiceOrderItemDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateServiceOrderItemDto)
  items?: UpdateServiceOrderItemDto[];

  @ApiPropertyOptional({
    description: 'Service order diagnostics',
    type: [UpdateServiceOrderDiagnosticDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateServiceOrderDiagnosticDto)
  diagnostics?: UpdateServiceOrderDiagnosticDto[];

  @ApiPropertyOptional({
    description: 'Service order lines',
    type: [UpdateServiceOrderLineDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateServiceOrderLineDto)
  lines?: UpdateServiceOrderLineDto[];
}
