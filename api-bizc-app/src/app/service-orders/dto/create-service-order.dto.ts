import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  IsUUID,
  IsOptional,
  IsDateString,
  IsEnum,
  IsDecimal,
  IsArray,
  ValidateNested,
  IsNumber,
  IsBoolean,
  <PERSON><PERSON>ength,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';
import {
  ServiceOrderLineType,
  ServiceOrderLineStaffRoleType,
  ServiceOrderPaymentStatus,
  CustomerApprovalStatus,
} from '../../drizzle/schema/service-orders.schema';
import { TaskStatus, TaskPriority } from '../../drizzle/schema/tasks.schema';
import { TaxType, DiscountType } from '@app/shared/types/common.enum';
import { StandardUnitOfMeasure } from '@app/shared/types/product.enum';

export class CreateServiceOrderItemDto {
  @ApiProperty({ description: 'Item/Product ID' })
  @IsNotEmpty()
  @IsUUID()
  itemId: string;

  @ApiProperty({ description: 'Quantity required' })
  @IsNotEmpty()
  @IsDecimal({ decimal_digits: '0,3' })
  quantity: string;

  @ApiProperty({ description: 'Unit price' })
  @IsNotEmpty()
  @IsDecimal({ decimal_digits: '0,2' })
  unitPrice: string;

  @ApiProperty({ description: 'Total price for this item' })
  @IsNotEmpty()
  @IsDecimal({ decimal_digits: '0,2' })
  totalPrice: string;

  @ApiProperty({
    description: 'Standard unit of measure',
    enum: StandardUnitOfMeasure,
    enumName: 'StandardUnitOfMeasure',
  })
  @IsEnum(StandardUnitOfMeasure)
  standardUnitOfMeasure: StandardUnitOfMeasure;

  @ApiPropertyOptional({ description: 'Custom unit ID' })
  @IsOptional()
  @IsUUID()
  customUnitId?: string;

  @ApiPropertyOptional({ description: 'Item description' })
  @IsOptional()
  @IsString()
  description?: string;
}

export class CreateServiceOrderDiagnosticDto {
  @ApiProperty({ description: 'Test performed' })
  @IsNotEmpty()
  @IsString()
  testPerformed: string;

  @ApiPropertyOptional({ description: 'Test result' })
  @IsOptional()
  @IsString()
  testResult?: string;

  @ApiPropertyOptional({ description: 'Issue found' })
  @IsOptional()
  @IsString()
  issueFound?: string;

  @ApiPropertyOptional({ description: 'Recommended action' })
  @IsOptional()
  @IsString()
  recommendedAction?: string;

  @ApiPropertyOptional({ description: 'Requires parts' })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  requiresParts?: boolean;

  @ApiPropertyOptional({ description: 'Parts needed' })
  @IsOptional()
  @IsString()
  partsNeeded?: string;

  @ApiPropertyOptional({ description: 'Estimated repair time in hours' })
  @IsOptional()
  @IsDecimal({ decimal_digits: '0,2' })
  estimatedRepairTime?: string;

  @ApiPropertyOptional({ description: 'Diagnostic notes' })
  @IsOptional()
  @IsString()
  notes?: string;
}

export class CreateServiceOrderLineStaffTaskDto {
  @ApiProperty({ description: 'Task title', maxLength: 191 })
  @IsNotEmpty()
  @IsString()
  @MaxLength(191)
  title: string;

  @ApiPropertyOptional({ description: 'Task description' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: 'Task due date in ISO format' })
  @IsOptional()
  @IsDateString()
  dueDate?: string;

  @ApiPropertyOptional({
    enum: TaskPriority,
    enumName: 'TaskPriority',
    description: 'Task priority',
    default: TaskPriority.MEDIUM,
  })
  @IsOptional()
  @IsEnum(TaskPriority)
  priority?: TaskPriority;

  @ApiPropertyOptional({
    enum: TaskStatus,
    enumName: 'TaskStatus',
    description: 'Task status',
    default: TaskStatus.PENDING,
  })
  @IsOptional()
  @IsEnum(TaskStatus)
  status?: TaskStatus;
}

export class CreateServiceOrderLineStaffDto {
  @ApiProperty({ description: 'Staff member ID' })
  @IsNotEmpty()
  @IsUUID()
  staffId: string;

  @ApiProperty({
    description: 'Staff role type',
    enum: ServiceOrderLineStaffRoleType,
    enumName: 'ServiceOrderLineStaffRoleType',
  })
  @IsEnum(ServiceOrderLineStaffRoleType)
  roleType: ServiceOrderLineStaffRoleType;

  @ApiPropertyOptional({ description: 'Start time' })
  @IsOptional()
  @IsDateString()
  startTime?: string;

  @ApiPropertyOptional({ description: 'End time' })
  @IsOptional()
  @IsDateString()
  endTime?: string;

  @ApiPropertyOptional({ description: 'Hours worked' })
  @IsOptional()
  @IsDecimal({ decimal_digits: '0,2' })
  hoursWorked?: string;

  @ApiPropertyOptional({ description: 'Staff notes' })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiPropertyOptional({
    description: 'Task to create for this staff assignment',
    type: CreateServiceOrderLineStaffTaskDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateServiceOrderLineStaffTaskDto)
  task?: CreateServiceOrderLineStaffTaskDto;
}

export class CreateServiceOrderLineDto {
  @ApiPropertyOptional({ description: 'Service ID' })
  @IsOptional()
  @IsUUID()
  serviceId?: string;

  @ApiProperty({
    description: 'Line type',
    enum: ServiceOrderLineType,
    enumName: 'ServiceOrderLineType',
  })
  @IsEnum(ServiceOrderLineType)
  lineType: ServiceOrderLineType;

  @ApiProperty({ description: 'Quantity' })
  @IsNotEmpty()
  @IsNumber()
  quantity: number;

  @ApiProperty({ description: 'Unit price' })
  @IsNotEmpty()
  @IsDecimal({ decimal_digits: '0,2' })
  unitPrice: string;

  @ApiPropertyOptional({
    description: 'Discount type',
    enum: DiscountType,
    enumName: 'DiscountType',
  })
  @IsOptional()
  @IsEnum(DiscountType)
  discountType?: DiscountType;

  @ApiPropertyOptional({ description: 'Discount amount' })
  @IsOptional()
  @IsDecimal({ decimal_digits: '0,2' })
  discountAmount?: string;

  @ApiProperty({ description: 'Line total' })
  @IsNotEmpty()
  @IsDecimal({ decimal_digits: '0,2' })
  lineTotal: string;

  @ApiPropertyOptional({ description: 'Is warranty item' })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  isWarrantyItem?: boolean;

  @ApiPropertyOptional({ description: 'Line notes' })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiPropertyOptional({
    description: 'Staff assignments for this line',
    type: [CreateServiceOrderLineStaffDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateServiceOrderLineStaffDto)
  staff?: CreateServiceOrderLineStaffDto[];
}

export class CreateServiceOrderDto {
  @ApiProperty({ description: 'Service order number', maxLength: 50 })
  @IsNotEmpty()
  @IsString()
  @MaxLength(50)
  orderNumber: string;

  @ApiProperty({ description: 'Customer ID' })
  @IsNotEmpty()
  @IsUUID()
  customerId: string;

  @ApiPropertyOptional({ description: 'Appointment ID' })
  @IsOptional()
  @IsUUID()
  appointmentId?: string;

  @ApiProperty({ description: 'Status ID' })
  @IsNotEmpty()
  @IsUUID()
  statusId: string;

  @ApiProperty({ description: 'Priority ID' })
  @IsNotEmpty()
  @IsUUID()
  priorityId: string;

  @ApiProperty({ description: 'Service type ID' })
  @IsNotEmpty()
  @IsUUID()
  serviceTypeId: string;

  @ApiProperty({ description: 'Location ID' })
  @IsNotEmpty()
  @IsUUID()
  locationId: string;

  @ApiProperty({ description: 'Order date' })
  @IsNotEmpty()
  @IsDateString()
  orderDate: string;

  @ApiPropertyOptional({ description: 'Scheduled date' })
  @IsOptional()
  @IsDateString()
  scheduledDate?: string;

  @ApiPropertyOptional({ description: 'Actual start date' })
  @IsOptional()
  @IsDateString()
  actualStartDate?: string;

  @ApiPropertyOptional({ description: 'Actual end date' })
  @IsOptional()
  @IsDateString()
  actualEndDate?: string;

  @ApiPropertyOptional({ description: 'Promised date' })
  @IsOptional()
  @IsDateString()
  promisedDate?: string;

  @ApiPropertyOptional({ description: 'Service order description' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: 'Estimated cost' })
  @IsOptional()
  @IsDecimal({ decimal_digits: '0,2' })
  estimatedCost?: string;

  @ApiPropertyOptional({ description: 'Actual cost' })
  @IsOptional()
  @IsDecimal({ decimal_digits: '0,2' })
  actualCost?: string;

  @ApiPropertyOptional({ description: 'Total amount' })
  @IsOptional()
  @IsDecimal({ decimal_digits: '0,2' })
  totalAmount?: string;

  @ApiPropertyOptional({
    description: 'Tax type',
    enum: TaxType,
    enumName: 'TaxType',
  })
  @IsOptional()
  @IsEnum(TaxType)
  taxType?: TaxType;

  @ApiPropertyOptional({ description: 'Default tax rate ID' })
  @IsOptional()
  @IsUUID()
  defaultTaxRateId?: string;

  @ApiProperty({ description: 'Staff member who received the order' })
  @IsNotEmpty()
  @IsUUID()
  receivedById: string;

  @ApiPropertyOptional({
    description: 'Customer approval status',
    enum: CustomerApprovalStatus,
    enumName: 'CustomerApprovalStatus',
  })
  @IsOptional()
  @IsEnum(CustomerApprovalStatus)
  customerApprovalStatus?: CustomerApprovalStatus;

  @ApiPropertyOptional({ description: 'Customer approval date' })
  @IsOptional()
  @IsDateString()
  customerApprovalDate?: string;

  @ApiPropertyOptional({ description: 'Cancellation reason' })
  @IsOptional()
  @IsString()
  cancellationReason?: string;

  @ApiPropertyOptional({
    description: 'Payment status',
    enum: ServiceOrderPaymentStatus,
    enumName: 'ServiceOrderPaymentStatus',
  })
  @IsOptional()
  @IsEnum(ServiceOrderPaymentStatus)
  paymentStatus?: ServiceOrderPaymentStatus;

  // Related data arrays
  @ApiPropertyOptional({
    description: 'Service order items',
    type: [CreateServiceOrderItemDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateServiceOrderItemDto)
  items?: CreateServiceOrderItemDto[];

  @ApiPropertyOptional({
    description: 'Service order diagnostics',
    type: [CreateServiceOrderDiagnosticDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateServiceOrderDiagnosticDto)
  diagnostics?: CreateServiceOrderDiagnosticDto[];

  @ApiPropertyOptional({
    description: 'Service order lines',
    type: [CreateServiceOrderLineDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateServiceOrderLineDto)
  lines?: CreateServiceOrderLineDto[];
}
