import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
  integer,
  decimal,
  jsonb,
} from 'drizzle-orm/pg-core';
import { isNull } from 'drizzle-orm';
import { business } from './business.schema';
import { media } from './media.schema';
import { reservationTypes } from './reservation-types.schema';
import { accounts } from './accounts.schema';
import { taxes } from './taxes.schema';
import { auditFields } from './common-fields.schema';
import { assets } from './assets.schema';
import { TaxType } from '@app/shared/types/common.enum';
import { EventSpaceFeatures, EventSpaceStatus } from '@shared/types';

// Event Space Status Enum
export const eventSpaceStatusEnum = pgEnum('event_space_status', [
  EventSpaceStatus.AVAILABLE,
  EventSpaceStatus.BOOKED,
  EventSpaceStatus.TENTATIVE,
  EventSpaceStatus.SETUP,
  EventSpaceStatus.IN_USE,
  EventSpaceStatus.BREAKDOWN,
  EventSpaceStatus.MAINTENANCE,
  EventSpaceStatus.BLOCKED,
  EventSpaceStatus.OUT_OF_ORDER,
  EventSpaceStatus.INACTIVE,
]);

// Tax Type Enum (assuming it's similar to other enums)
export const taxTypeEnum = pgEnum('tax_type', [
  TaxType.INCLUSIVE,
  TaxType.EXCLUSIVE,
  TaxType.OUT_OF_SCOPE,
]);

// Event Spaces table
export const eventSpaces = pgTable(
  'event_spaces',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),

    // Basic space information
    spaceCode: text('space_code').notNull(), // Unique identifier like "BALLROOM-A"
    name: text('name').notNull(), // Display name like "Grand Ballroom"

    // Type references to reservation-types
    type: uuid('type')
      .notNull()
      .references(() => reservationTypes.id),
    subType: uuid('sub_type').references(() => reservationTypes.id),

    // Space details
    status: eventSpaceStatusEnum('status')
      .default(EventSpaceStatus.AVAILABLE)
      .notNull(),
    floor: integer('floor').notNull(),
    building: text('building'), // For venues with multiple buildings
    description: text('description'),

    // Features and amenities
    features: jsonb('features')
      .$type<EventSpaceFeatures>()
      .default({})
      .notNull(),

    maxOccupancy: integer('max_occupancy').notNull(), // Fire code maximum

    // Pricing information
    baseHourlyRate: decimal('base_hourly_rate', { precision: 12, scale: 2 })
      .default('0.00')
      .notNull(),
    baseHalfDayRate: decimal('base_half_day_rate', { precision: 12, scale: 2 })
      .default('0.00')
      .notNull(),
    baseFullDayRate: decimal('base_full_day_rate', { precision: 12, scale: 2 })
      .default('0.00')
      .notNull(),
    standardCost: decimal('standard_cost', { precision: 12, scale: 2 })
      .default('0.00')
      .notNull(),

    // Minimum booking requirements
    minBookingHours: decimal('min_booking_hours', { precision: 4, scale: 2 })
      .default('1.00')
      .notNull(),

    // Account references
    incomeAccountId: uuid('income_account_id')
      .notNull()
      .references(() => accounts.id),
    expenseAccountId: uuid('expense_account_id')
      .notNull()
      .references(() => accounts.id),
    assetAccountId: uuid('asset_account_id').references(() => accounts.id),

    // Asset reference
    assetId: uuid('asset_id').references(() => assets.id),

    // Image reference
    image: uuid('image').references(() => media.id),

    // SEO fields
    seoTitle: text('seo_title'),
    seoDescription: text('seo_description'),
    seoKeywords: text('seo_keywords').array(),
    ogImage: uuid('og_image').references(() => media.id),

    // Position for ordering
    typePosition: integer('type_position').default(0).notNull(),
    subTypePosition: integer('sub_type_position').default(0).notNull(),
    globalPosition: integer('global_position').default(0).notNull(),

    // Tax information
    taxType: taxTypeEnum('tax_type').default(TaxType.INCLUSIVE).notNull(),
    defaultTaxRateId: uuid('default_tax_rate_id').references(() => taxes.id),

    // Standard audit fields
    ...auditFields,
  },
  (t) => ({
    // Indexes for performance
    businessIdIndex: index('event_spaces_business_id_index').on(t.businessId),
    spaceCodeIndex: index('event_spaces_space_code_index').on(t.spaceCode),
    nameIndex: index('event_spaces_name_index').on(t.name),
    typeIndex: index('event_spaces_type_index').on(t.type),
    subTypeIndex: index('event_spaces_sub_type_index').on(t.subType),

    floorIndex: index('event_spaces_floor_index').on(t.floor),
    buildingIndex: index('event_spaces_building_index').on(t.building),
    maxOccupancyIndex: index('event_spaces_max_occupancy_index').on(
      t.maxOccupancy,
    ),

    imageIndex: index('event_spaces_image_index').on(t.image),
    ogImageIndex: index('event_spaces_og_image_index').on(t.ogImage),

    typePositionIndex: index('event_spaces_type_position_index').on(
      t.typePosition,
    ),
    subTypePositionIndex: index('event_spaces_sub_type_position_index').on(
      t.subTypePosition,
    ),
    globalPositionIndex: index('event_spaces_global_position_index').on(
      t.globalPosition,
    ),
    incomeAccountIdIndex: index('event_spaces_income_account_id_index').on(
      t.incomeAccountId,
    ),
    expenseAccountIdIndex: index('event_spaces_expense_account_id_index').on(
      t.expenseAccountId,
    ),
    assetAccountIdIndex: index('event_spaces_asset_account_id_index').on(
      t.assetAccountId,
    ),
    assetIdIndex: index('event_spaces_asset_id_index').on(t.assetId),
    defaultTaxRateIdIndex: index('event_spaces_default_tax_rate_id_index').on(
      t.defaultTaxRateId,
    ),

    // Unique constraints with soft deletion support
    uniqueBusinessSpaceCode: uniqueIndex(
      'event_spaces_business_space_code_unique',
    )
      .on(t.businessId, t.spaceCode)
      .where(isNull(t.deletedAt)),
    uniqueBusinessName: uniqueIndex('event_spaces_business_name_unique')
      .on(t.businessId, t.name)
      .where(isNull(t.deletedAt)),

    // Composite indexes for common queries
    businessStatusIndex: index('event_spaces_business_status_index').on(
      t.businessId,
      t.status,
    ),
    businessTypeStatusIndex: index(
      'event_spaces_business_type_status_index',
    ).on(t.businessId, t.type, t.status),
  }),
);
