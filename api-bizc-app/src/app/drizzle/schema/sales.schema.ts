import {
  date,
  decimal,
  index,
  integer,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uuid,
  uniqueIndex,
} from 'drizzle-orm/pg-core';
import { isNull } from 'drizzle-orm';
import { business } from './business.schema';
import { customers } from './customers.schema';
import { locations } from './locations.schema';
import { users } from './users.schema';
import { staffMembers } from './staff.schema';
import { PaymentStatus } from '../../shared/types/common.enum';
import { paymentStatusEnum } from './common-fields.schema';

// Quotation Status Enum
export enum QuotationStatus {
  DRAFT = 'draft',
  SENT = 'sent',
  ACCEPTED = 'accepted',
  REJECTED = 'rejected',
  EXPIRED = 'expired',
  CONVERTED = 'converted',
}

// Price Tax Type Enum
export enum PriceTaxType {
  INCLUSIVE = 'inclusive',
  EXCLUSIVE = 'exclusive',
}

// Order Source Enum
export enum OrderSource {
  POS = 'pos',
  ONLINE = 'online',
  PHONE = 'phone',
  EMAIL = 'email',
  SALES_REP = 'sales_rep',
  DINE_IN = 'dine_in',
  TAKEOUT = 'takeout',
  DELIVERY = 'delivery',
}

// Order Type Enum
export enum OrderType {
  RETAIL = 'retail',
  RESTAURANT = 'restaurant',
  CATERING = 'catering',
}

// Sales Order Status Enum
export enum SalesOrderStatus {
  DRAFT = 'draft',
  CONFIRMED = 'confirmed',
  PICKING = 'picking',
  PREPARING = 'preparing',
  READY = 'ready',
  SHIPPED = 'shipped',
  DELIVERED = 'delivered',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

// Order Line Status Enum
export enum OrderLineStatus {
  PENDING = 'pending',
  PREPARING = 'preparing',
  READY = 'ready',
  SERVED = 'served',
  CANCELLED = 'cancelled',
}

// Ticket Type Enum
export enum TicketType {
  KOT = 'KOT',
  BOT = 'BOT',
}

// Payment Method Enum
export enum PaymentMethod {
  CREDIT_CARD = 'credit_card',
  DEBIT_CARD = 'debit_card',
  PAYPAL = 'paypal',
  BANK_TRANSFER = 'bank_transfer',
  CASH_ON_DELIVERY = 'cash_on_delivery',
  STORE_CREDIT = 'store_credit',
  GIFT_CARD = 'gift_card',
}

// Payment Transaction Status Enum
export enum PaymentTransactionStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  REFUNDED = 'refunded',
}

// Return Type Enum
export enum ReturnType {
  REFUND = 'refund',
  EXCHANGE = 'exchange',
  CREDIT = 'credit',
}

// Return Status Enum
export enum ReturnStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  RECEIVED = 'received',
  REJECTED = 'rejected',
  COMPLETED = 'completed',
}

// Invoice Type Enum
export enum InvoiceType {
  SALES = 'sales',
  SERVICE = 'service',
  REPAIR = 'repair',
  CATERING = 'catering',
  PROFORMA = 'proforma',
  CREDIT_NOTE = 'credit_note',
}

// Invoice Status Enum
export enum InvoiceStatus {
  DRAFT = 'draft',
  SENT = 'sent',
  PAID = 'paid',
  PARTIAL = 'partial',
  OVERDUE = 'overdue',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded',
}

// Invoice Line Type Enum
export enum InvoiceLineType {
  PRODUCT = 'product',
  SERVICE = 'service',
  WARRANTY = 'warranty',
  SHIPPING = 'shipping',
  DISCOUNT = 'discount',
  TAX = 'tax',
}

// Cash Register Session Status Enum
export enum CashRegisterSessionStatus {
  OPEN = 'open',
  CLOSED = 'closed',
  SUSPENDED = 'suspended',
}

// POS Transaction Type Enum
export enum PosTransactionType {
  SALE = 'sale',
  REFUND = 'refund',
  VOID = 'void',
  EXCHANGE = 'exchange',
}

// POS Transaction Status Enum
export enum PosTransactionStatus {
  COMPLETED = 'completed',
  PENDING = 'pending',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded',
}

// Table Status Enum
export enum TableStatus {
  AVAILABLE = 'available',
  OCCUPIED = 'occupied',
  RESERVED = 'reserved',
  CLEANING = 'cleaning',
  MAINTENANCE = 'maintenance',
}

// Table Shape Enum
export enum TableShape {
  ROUND = 'round',
  SQUARE = 'square',
  RECTANGLE = 'rectangle',
  BOOTH = 'booth',
}

export const quotationStatusEnum = pgEnum('quotation_status', [
  QuotationStatus.DRAFT,
  QuotationStatus.SENT,
  QuotationStatus.ACCEPTED,
  QuotationStatus.REJECTED,
  QuotationStatus.EXPIRED,
  QuotationStatus.CONVERTED,
]);

export const priceTaxTypeEnum = pgEnum('price_tax_type', [
  PriceTaxType.INCLUSIVE,
  PriceTaxType.EXCLUSIVE,
]);

export const orderSourceEnum = pgEnum('order_source', [
  OrderSource.POS,
  OrderSource.ONLINE,
  OrderSource.PHONE,
  OrderSource.EMAIL,
  OrderSource.SALES_REP,
  OrderSource.DINE_IN,
  OrderSource.TAKEOUT,
  OrderSource.DELIVERY,
]);

export const orderTypeEnum = pgEnum('order_type', [
  OrderType.RETAIL,
  OrderType.RESTAURANT,
  OrderType.CATERING,
]);

export const salesOrderStatusEnum = pgEnum('sales_order_status', [
  SalesOrderStatus.DRAFT,
  SalesOrderStatus.CONFIRMED,
  SalesOrderStatus.PICKING,
  SalesOrderStatus.PREPARING,
  SalesOrderStatus.READY,
  SalesOrderStatus.SHIPPED,
  SalesOrderStatus.DELIVERED,
  SalesOrderStatus.COMPLETED,
  SalesOrderStatus.CANCELLED,
]);

export const orderLineStatusEnum = pgEnum('order_line_status', [
  OrderLineStatus.PENDING,
  OrderLineStatus.PREPARING,
  OrderLineStatus.READY,
  OrderLineStatus.SERVED,
  OrderLineStatus.CANCELLED,
]);

export const ticketTypeEnum = pgEnum('ticket_type', [
  TicketType.KOT,
  TicketType.BOT,
]);

export const paymentMethodEnum = pgEnum('payment_method', [
  PaymentMethod.CREDIT_CARD,
  PaymentMethod.DEBIT_CARD,
  PaymentMethod.PAYPAL,
  PaymentMethod.BANK_TRANSFER,
  PaymentMethod.CASH_ON_DELIVERY,
  PaymentMethod.STORE_CREDIT,
  PaymentMethod.GIFT_CARD,
]);

export const paymentTransactionStatusEnum = pgEnum(
  'payment_transaction_status',
  [
    PaymentTransactionStatus.PENDING,
    PaymentTransactionStatus.PROCESSING,
    PaymentTransactionStatus.COMPLETED,
    PaymentTransactionStatus.FAILED,
    PaymentTransactionStatus.REFUNDED,
  ],
);

export const returnTypeEnum = pgEnum('return_type', [
  ReturnType.REFUND,
  ReturnType.EXCHANGE,
  ReturnType.CREDIT,
]);

export const returnStatusEnum = pgEnum('return_status', [
  ReturnStatus.PENDING,
  ReturnStatus.APPROVED,
  ReturnStatus.RECEIVED,
  ReturnStatus.REJECTED,
  ReturnStatus.COMPLETED,
]);

export const invoiceTypeEnum = pgEnum('invoice_type', [
  InvoiceType.SALES,
  InvoiceType.SERVICE,
  InvoiceType.REPAIR,
  InvoiceType.CATERING,
  InvoiceType.PROFORMA,
  InvoiceType.CREDIT_NOTE,
]);

export const invoiceStatusEnum = pgEnum('invoice_status', [
  InvoiceStatus.DRAFT,
  InvoiceStatus.SENT,
  InvoiceStatus.PAID,
  InvoiceStatus.PARTIAL,
  InvoiceStatus.OVERDUE,
  InvoiceStatus.CANCELLED,
  InvoiceStatus.REFUNDED,
]);

export const invoiceLineTypeEnum = pgEnum('invoice_line_type', [
  InvoiceLineType.PRODUCT,
  InvoiceLineType.SERVICE,
  InvoiceLineType.WARRANTY,
  InvoiceLineType.SHIPPING,
  InvoiceLineType.DISCOUNT,
  InvoiceLineType.TAX,
]);

export const cashRegisterSessionStatusEnum = pgEnum(
  'cash_register_session_status',
  [
    CashRegisterSessionStatus.OPEN,
    CashRegisterSessionStatus.CLOSED,
    CashRegisterSessionStatus.SUSPENDED,
  ],
);

export const posTransactionTypeEnum = pgEnum('pos_transaction_type', [
  PosTransactionType.SALE,
  PosTransactionType.REFUND,
  PosTransactionType.VOID,
  PosTransactionType.EXCHANGE,
]);

export const posTransactionStatusEnum = pgEnum('pos_transaction_status', [
  PosTransactionStatus.COMPLETED,
  PosTransactionStatus.PENDING,
  PosTransactionStatus.CANCELLED,
  PosTransactionStatus.REFUNDED,
]);

export const tableStatusEnum = pgEnum('table_status', [
  TableStatus.AVAILABLE,
  TableStatus.OCCUPIED,
  TableStatus.RESERVED,
  TableStatus.CLEANING,
  TableStatus.MAINTENANCE,
]);

export const tableShapeEnum = pgEnum('table_shape', [
  TableShape.ROUND,
  TableShape.SQUARE,
  TableShape.RECTANGLE,
  TableShape.BOOTH,
]);

// Quotations table
export const quotations = pgTable(
  'quotations',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    quotationNumber: text('quotation_number').notNull(),
    customerId: uuid('customer_id')
      .notNull()
      .references(() => customers.id),
    quotationDate: date('quotation_date').notNull(),
    validUntilDate: date('valid_until_date'),
    status: quotationStatusEnum('status')
      .default(QuotationStatus.DRAFT)
      .notNull(),
    subtotal: decimal('subtotal', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    taxAmount: decimal('tax_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    discountAmount: decimal('discount_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    shippingAmount: decimal('shipping_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    totalAmount: decimal('total_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    paymentTerms: text('payment_terms'),
    deliveryTerms: text('delivery_terms'),
    billingAddress: text('billing_address'),
    shippingAddress: text('shipping_address'),
    termsAndConditions: text('terms_and_conditions'),
    notes: text('notes'),
    salespersonId: uuid('salesperson_id').references(() => staffMembers.id),
    convertedToOrderId: uuid('converted_to_order_id'), // References SALES_ORDERS
    convertedDate: date('converted_date'),

    // Audit fields
    createdBy: uuid('created_by')
      .notNull()
      .references(() => users.id),
    updatedBy: uuid('updated_by').references(() => users.id),
    deletedBy: uuid('deleted_by').references(() => users.id),
    deletedAt: timestamp('deleted_at'),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
  },
  (t) => ({
    // Primary index
    idIndex: index('quotations_id_index').on(t.id),

    // Foreign key indexes
    businessIdIndex: index('quotations_business_id_index').on(t.businessId),
    customerIdIndex: index('quotations_customer_id_index').on(t.customerId),
    salespersonIdIndex: index('quotations_salesperson_id_index').on(
      t.salespersonId,
    ),

    // Performance indexes
    statusIndex: index('quotations_status_index').on(t.status),
    quotationDateIndex: index('quotations_quotation_date_index').on(
      t.quotationDate,
    ),
    validUntilDateIndex: index('quotations_valid_until_date_index').on(
      t.validUntilDate,
    ),
    convertedDateIndex: index('quotations_converted_date_index').on(
      t.convertedDate,
    ),

    // Unique constraint
    uniqueQuotationNumber: uniqueIndex('quotations_quotation_number_unique')
      .on(t.businessId, t.quotationNumber)
      .where(isNull(t.deletedAt)),

    // Composite indexes for common query patterns
    businessStatusIndex: index('quotations_business_status_index')
      .on(t.businessId, t.status)
      .where(isNull(t.deletedAt)),
    businessCustomerIndex: index('quotations_business_customer_index')
      .on(t.businessId, t.customerId)
      .where(isNull(t.deletedAt)),
    businessDateIndex: index('quotations_business_date_index')
      .on(t.businessId, t.quotationDate)
      .where(isNull(t.deletedAt)),

    // Audit indexes
    createdAtIndex: index('quotations_created_at_index').on(t.createdAt),
    updatedAtIndex: index('quotations_updated_at_index').on(t.updatedAt),
  }),
);

// Quotation Order Lines table
export const quotationOrderLines = pgTable(
  'quotation_order_lines',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    quotationId: uuid('quotation_id')
      .notNull()
      .references(() => quotations.id, { onDelete: 'cascade' }),
    productId: uuid('product_id').notNull(), // References PRODUCTS table
    variantId: uuid('variant_id'), // References PRODUCT_VARIANTS table
    productDescription: text('product_description').notNull(),
    quantity: decimal('quantity', { precision: 15, scale: 3 }).notNull(),
    unitPrice: decimal('unit_price', { precision: 10, scale: 2 }).notNull(),
    priceTaxType: priceTaxTypeEnum('price_tax_type')
      .default(PriceTaxType.EXCLUSIVE)
      .notNull(),
    discountPercent: decimal('discount_percent', { precision: 5, scale: 2 })
      .default('0.00')
      .notNull(),
    lineSubtotal: decimal('line_subtotal', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    taxAmount: decimal('tax_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    lineTotal: decimal('line_total', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    sortOrder: integer('sort_order').default(1).notNull(),
    notes: text('notes'),
    createdAt: timestamp('created_at').defaultNow().notNull(),
  },
  (t) => ({
    // Primary index
    idIndex: index('quotation_order_lines_id_index').on(t.id),

    // Foreign key indexes
    businessIdIndex: index('quotation_order_lines_business_id_index').on(
      t.businessId,
    ),
    quotationIdIndex: index('quotation_order_lines_quotation_id_index').on(
      t.quotationId,
    ),
    productIdIndex: index('quotation_order_lines_product_id_index').on(
      t.productId,
    ),
    variantIdIndex: index('quotation_order_lines_variant_id_index').on(
      t.variantId,
    ),

    // Sort order index
    sortOrderIndex: index('quotation_order_lines_sort_order_index').on(
      t.quotationId,
      t.sortOrder,
    ),

    // Composite indexes for common query patterns
    businessQuotationIndex: index(
      'quotation_order_lines_business_quotation_index',
    ).on(t.businessId, t.quotationId),

    // Audit indexes
    createdAtIndex: index('quotation_order_lines_created_at_index').on(
      t.createdAt,
    ),
  }),
);

// Sales Orders table
export const salesOrders = pgTable(
  'sales_orders',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    orderNumber: text('order_number').notNull(),
    customerId: uuid('customer_id')
      .notNull()
      .references(() => customers.id),
    quotationId: uuid('quotation_id').references(() => quotations.id),
    tableId: uuid('table_id'), // References TABLES table for restaurant orders
    serverId: uuid('server_id').references(() => staffMembers.id),
    orderDate: date('order_date').notNull(),
    deliveryDate: date('delivery_date'),
    orderSource: orderSourceEnum('order_source').notNull(),
    orderType: orderTypeEnum('order_type').notNull(),
    status: salesOrderStatusEnum('status')
      .default(SalesOrderStatus.DRAFT)
      .notNull(),
    paymentStatus: paymentStatusEnum('payment_status')
      .default(PaymentStatus.PENDING)
      .notNull(),
    subtotal: decimal('subtotal', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    taxAmount: decimal('tax_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    discountAmount: decimal('discount_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    shippingAmount: decimal('shipping_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    serviceCharge: decimal('service_charge', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    tipAmount: decimal('tip_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    totalAmount: decimal('total_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    paymentTerms: text('payment_terms'),
    billingAddress: text('billing_address'),
    shippingAddress: text('shipping_address'),
    trackingNumber: text('tracking_number'),
    shippingCarrier: text('shipping_carrier'),
    customerIpAddress: text('customer_ip_address'),
    sessionId: text('session_id'),
    couponCode: text('coupon_code'),
    notes: text('notes'),
    salespersonId: uuid('salesperson_id').references(() => staffMembers.id),

    // Audit fields
    createdBy: uuid('created_by')
      .notNull()
      .references(() => users.id),
    updatedBy: uuid('updated_by').references(() => users.id),
    deletedBy: uuid('deleted_by').references(() => users.id),
    deletedAt: timestamp('deleted_at'),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
  },
  (t) => ({
    // Primary index
    idIndex: index('sales_orders_id_index').on(t.id),

    // Foreign key indexes
    businessIdIndex: index('sales_orders_business_id_index').on(t.businessId),
    customerIdIndex: index('sales_orders_customer_id_index').on(t.customerId),
    quotationIdIndex: index('sales_orders_quotation_id_index').on(
      t.quotationId,
    ),
    tableIdIndex: index('sales_orders_table_id_index').on(t.tableId),
    serverIdIndex: index('sales_orders_server_id_index').on(t.serverId),
    salespersonIdIndex: index('sales_orders_salesperson_id_index').on(
      t.salespersonId,
    ),

    // Performance indexes
    statusIndex: index('sales_orders_status_index').on(t.status),
    paymentStatusIndex: index('sales_orders_payment_status_index').on(
      t.paymentStatus,
    ),
    orderSourceIndex: index('sales_orders_order_source_index').on(
      t.orderSource,
    ),
    orderTypeIndex: index('sales_orders_order_type_index').on(t.orderType),
    orderDateIndex: index('sales_orders_order_date_index').on(t.orderDate),
    deliveryDateIndex: index('sales_orders_delivery_date_index').on(
      t.deliveryDate,
    ),

    // Unique constraint
    uniqueOrderNumber: uniqueIndex('sales_orders_order_number_unique')
      .on(t.businessId, t.orderNumber)
      .where(isNull(t.deletedAt)),

    // Composite indexes for common query patterns
    businessStatusIndex: index('sales_orders_business_status_index')
      .on(t.businessId, t.status)
      .where(isNull(t.deletedAt)),
    businessCustomerIndex: index('sales_orders_business_customer_index')
      .on(t.businessId, t.customerId)
      .where(isNull(t.deletedAt)),
    businessDateIndex: index('sales_orders_business_date_index')
      .on(t.businessId, t.orderDate)
      .where(isNull(t.deletedAt)),
    businessPaymentStatusIndex: index(
      'sales_orders_business_payment_status_index',
    )
      .on(t.businessId, t.paymentStatus)
      .where(isNull(t.deletedAt)),

    // Audit indexes
    createdAtIndex: index('sales_orders_created_at_index').on(t.createdAt),
    updatedAtIndex: index('sales_orders_updated_at_index').on(t.updatedAt),
  }),
);

// Sales Order Lines table
export const salesOrderLines = pgTable(
  'sales_order_lines',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    salesOrderId: uuid('sales_order_id')
      .notNull()
      .references(() => salesOrders.id, { onDelete: 'cascade' }),
    productId: uuid('product_id').notNull(), // References PRODUCTS table
    variantId: uuid('variant_id'), // References PRODUCT_VARIANTS table
    menuItemId: uuid('menu_item_id'), // References MENU_ITEMS table for restaurant
    quantityOrdered: decimal('quantity_ordered', {
      precision: 15,
      scale: 3,
    }).notNull(),
    quantityDelivered: decimal('quantity_delivered', {
      precision: 15,
      scale: 3,
    })
      .default('0.000')
      .notNull(),
    unitPrice: decimal('unit_price', { precision: 10, scale: 2 }).notNull(),
    priceTaxType: priceTaxTypeEnum('price_tax_type')
      .default(PriceTaxType.EXCLUSIVE)
      .notNull(),
    discountPercent: decimal('discount_percent', { precision: 5, scale: 2 })
      .default('0.00')
      .notNull(),
    lineSubtotal: decimal('line_subtotal', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    taxAmount: decimal('tax_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    lineTotal: decimal('line_total', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    warrantyTemplateId: uuid('warranty_template_id'), // References WARRANTY_TEMPLATES
    warrantyPrice: decimal('warranty_price', { precision: 10, scale: 2 })
      .default('0.00')
      .notNull(),
    specialInstructions: text('special_instructions'),
    orderStatus: orderLineStatusEnum('order_status')
      .default(OrderLineStatus.PENDING)
      .notNull(),
    ticketType: ticketTypeEnum('ticket_type'),
    kotNumber: text('kot_number'),
    botNumber: text('bot_number'),
    ticketPrintedAt: timestamp('ticket_printed_at'),
    prepStartedAt: timestamp('prep_started_at'),
    readyAt: timestamp('ready_at'),
    servedAt: timestamp('served_at'),
    preparedBy: uuid('prepared_by').references(() => staffMembers.id),
    notes: text('notes'),
    createdAt: timestamp('created_at').defaultNow().notNull(),
  },
  (t) => ({
    // Primary index
    idIndex: index('sales_order_lines_id_index').on(t.id),

    // Foreign key indexes
    businessIdIndex: index('sales_order_lines_business_id_index').on(
      t.businessId,
    ),
    salesOrderIdIndex: index('sales_order_lines_sales_order_id_index').on(
      t.salesOrderId,
    ),
    productIdIndex: index('sales_order_lines_product_id_index').on(t.productId),
    variantIdIndex: index('sales_order_lines_variant_id_index').on(t.variantId),
    menuItemIdIndex: index('sales_order_lines_menu_item_id_index').on(
      t.menuItemId,
    ),
    preparedByIndex: index('sales_order_lines_prepared_by_index').on(
      t.preparedBy,
    ),

    // Performance indexes
    orderStatusIndex: index('sales_order_lines_order_status_index').on(
      t.orderStatus,
    ),
    ticketTypeIndex: index('sales_order_lines_ticket_type_index').on(
      t.ticketType,
    ),
    kotNumberIndex: index('sales_order_lines_kot_number_index').on(t.kotNumber),
    botNumberIndex: index('sales_order_lines_bot_number_index').on(t.botNumber),

    // Time-based indexes
    ticketPrintedAtIndex: index('sales_order_lines_ticket_printed_at_index').on(
      t.ticketPrintedAt,
    ),
    prepStartedAtIndex: index('sales_order_lines_prep_started_at_index').on(
      t.prepStartedAt,
    ),
    readyAtIndex: index('sales_order_lines_ready_at_index').on(t.readyAt),
    servedAtIndex: index('sales_order_lines_served_at_index').on(t.servedAt),

    // Composite indexes for common query patterns
    businessSalesOrderIndex: index(
      'sales_order_lines_business_sales_order_index',
    ).on(t.businessId, t.salesOrderId),
    businessStatusIndex: index('sales_order_lines_business_status_index').on(
      t.businessId,
      t.orderStatus,
    ),

    // Audit indexes
    createdAtIndex: index('sales_order_lines_created_at_index').on(t.createdAt),
  }),
);

// Order Payments table
export const orderPayments = pgTable(
  'order_payments',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    salesOrderId: uuid('sales_order_id')
      .notNull()
      .references(() => salesOrders.id, { onDelete: 'cascade' }),
    paymentMethod: paymentMethodEnum('payment_method').notNull(),
    paymentGateway: text('payment_gateway'),
    transactionId: text('transaction_id'),
    amount: decimal('amount', { precision: 15, scale: 2 }).notNull(),
    status: paymentTransactionStatusEnum('status')
      .default(PaymentTransactionStatus.PENDING)
      .notNull(),
    gatewayResponse: text('gateway_response'),
    paymentDate: timestamp('payment_date'),
    cardLastFour: text('card_last_four'),
    cardType: text('card_type'),

    // Audit fields
    createdBy: uuid('created_by')
      .notNull()
      .references(() => users.id),
    updatedBy: uuid('updated_by').references(() => users.id),
    deletedBy: uuid('deleted_by').references(() => users.id),
    deletedAt: timestamp('deleted_at'),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
  },
  (t) => ({
    // Primary index
    idIndex: index('order_payments_id_index').on(t.id),

    // Foreign key indexes
    businessIdIndex: index('order_payments_business_id_index').on(t.businessId),
    salesOrderIdIndex: index('order_payments_sales_order_id_index').on(
      t.salesOrderId,
    ),

    // Performance indexes
    paymentMethodIndex: index('order_payments_payment_method_index').on(
      t.paymentMethod,
    ),
    statusIndex: index('order_payments_status_index').on(t.status),
    paymentDateIndex: index('order_payments_payment_date_index').on(
      t.paymentDate,
    ),
    transactionIdIndex: index('order_payments_transaction_id_index').on(
      t.transactionId,
    ),

    // Composite indexes for common query patterns
    businessSalesOrderIndex: index('order_payments_business_sales_order_index')
      .on(t.businessId, t.salesOrderId)
      .where(isNull(t.deletedAt)),
    businessStatusIndex: index('order_payments_business_status_index')
      .on(t.businessId, t.status)
      .where(isNull(t.deletedAt)),
    businessPaymentDateIndex: index(
      'order_payments_business_payment_date_index',
    )
      .on(t.businessId, t.paymentDate)
      .where(isNull(t.deletedAt)),

    // Audit indexes
    createdAtIndex: index('order_payments_created_at_index').on(t.createdAt),
    updatedAtIndex: index('order_payments_updated_at_index').on(t.updatedAt),
  }),
);

// Sales Returns table
export const salesReturns = pgTable(
  'sales_returns',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    returnNumber: text('return_number').notNull(),
    customerId: uuid('customer_id')
      .notNull()
      .references(() => customers.id),
    salesOrderId: uuid('sales_order_id').references(() => salesOrders.id),
    invoiceId: uuid('invoice_id'), // References INVOICES table
    returnDate: date('return_date').notNull(),
    returnType: returnTypeEnum('return_type').notNull(),
    status: returnStatusEnum('status').default(ReturnStatus.PENDING).notNull(),
    returnReason: text('return_reason'),
    subtotal: decimal('subtotal', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    taxAmount: decimal('tax_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    shippingCost: decimal('shipping_cost', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    restockingFee: decimal('restocking_fee', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    totalAmount: decimal('total_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    notes: text('notes'),
    requestedBy: uuid('requested_by')
      .notNull()
      .references(() => users.id),
    approvedBy: uuid('approved_by').references(() => users.id),
    receivedBy: uuid('received_by').references(() => users.id),
    approvedDate: timestamp('approved_date'),
    receivedDate: timestamp('received_date'),

    // Audit fields
    createdBy: uuid('created_by')
      .notNull()
      .references(() => users.id),
    updatedBy: uuid('updated_by').references(() => users.id),
    deletedBy: uuid('deleted_by').references(() => users.id),
    deletedAt: timestamp('deleted_at'),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
  },
  (t) => ({
    // Primary index
    idIndex: index('sales_returns_id_index').on(t.id),

    // Foreign key indexes
    businessIdIndex: index('sales_returns_business_id_index').on(t.businessId),
    customerIdIndex: index('sales_returns_customer_id_index').on(t.customerId),
    salesOrderIdIndex: index('sales_returns_sales_order_id_index').on(
      t.salesOrderId,
    ),
    requestedByIndex: index('sales_returns_requested_by_index').on(
      t.requestedBy,
    ),
    approvedByIndex: index('sales_returns_approved_by_index').on(t.approvedBy),
    receivedByIndex: index('sales_returns_received_by_index').on(t.receivedBy),

    // Performance indexes
    returnTypeIndex: index('sales_returns_return_type_index').on(t.returnType),
    statusIndex: index('sales_returns_status_index').on(t.status),
    returnDateIndex: index('sales_returns_return_date_index').on(t.returnDate),
    approvedDateIndex: index('sales_returns_approved_date_index').on(
      t.approvedDate,
    ),
    receivedDateIndex: index('sales_returns_received_date_index').on(
      t.receivedDate,
    ),

    // Unique constraint
    uniqueReturnNumber: uniqueIndex('sales_returns_return_number_unique')
      .on(t.businessId, t.returnNumber)
      .where(isNull(t.deletedAt)),

    // Composite indexes for common query patterns
    businessStatusIndex: index('sales_returns_business_status_index')
      .on(t.businessId, t.status)
      .where(isNull(t.deletedAt)),
    businessCustomerIndex: index('sales_returns_business_customer_index')
      .on(t.businessId, t.customerId)
      .where(isNull(t.deletedAt)),
    businessDateIndex: index('sales_returns_business_date_index')
      .on(t.businessId, t.returnDate)
      .where(isNull(t.deletedAt)),

    // Audit indexes
    createdAtIndex: index('sales_returns_created_at_index').on(t.createdAt),
    updatedAtIndex: index('sales_returns_updated_at_index').on(t.updatedAt),
  }),
);

// Sales Invoices table
export const salesInvoices = pgTable(
  'sales_invoices',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    invoiceNumber: text('invoice_number').notNull(),
    invoiceType: invoiceTypeEnum('invoice_type')
      .default(InvoiceType.SALES)
      .notNull(),
    customerId: uuid('customer_id')
      .notNull()
      .references(() => customers.id),
    salesOrderId: uuid('sales_order_id').references(() => salesOrders.id),
    invoiceDate: date('invoice_date').notNull(),
    dueDate: date('due_date'),
    status: invoiceStatusEnum('status').default(InvoiceStatus.DRAFT).notNull(),
    subtotal: decimal('subtotal', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    taxAmount: decimal('tax_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    discountAmount: decimal('discount_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    shippingAmount: decimal('shipping_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    serviceCharge: decimal('service_charge', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    totalAmount: decimal('total_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    paidAmount: decimal('paid_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    balanceDue: decimal('balance_due', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    paymentTerms: text('payment_terms'),
    currencyCode: text('currency_code').default('USD').notNull(),
    exchangeRate: decimal('exchange_rate', { precision: 10, scale: 6 })
      .default('1.000000')
      .notNull(),
    billingAddress: text('billing_address'),
    shippingAddress: text('shipping_address'),
    poNumber: text('po_number'),
    referenceNumber: text('reference_number'),
    notes: text('notes'),
    termsConditions: text('terms_conditions'),
    createdBy: uuid('created_by')
      .notNull()
      .references(() => users.id),
    approvedBy: uuid('approved_by').references(() => users.id),
    sentDate: timestamp('sent_date'),
    paidDate: timestamp('paid_date'),

    // Audit fields
    updatedBy: uuid('updated_by').references(() => users.id),
    deletedBy: uuid('deleted_by').references(() => users.id),
    deletedAt: timestamp('deleted_at'),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
  },
  (t) => ({
    // Primary index
    idIndex: index('sales_invoices_id_index').on(t.id),

    // Foreign key indexes
    businessIdIndex: index('sales_invoices_business_id_index').on(t.businessId),
    customerIdIndex: index('sales_invoices_customer_id_index').on(t.customerId),
    salesOrderIdIndex: index('sales_invoices_sales_order_id_index').on(
      t.salesOrderId,
    ),
    createdByIndex: index('sales_invoices_created_by_index').on(t.createdBy),
    approvedByIndex: index('sales_invoices_approved_by_index').on(t.approvedBy),

    // Performance indexes
    invoiceTypeIndex: index('sales_invoices_invoice_type_index').on(
      t.invoiceType,
    ),
    statusIndex: index('sales_invoices_status_index').on(t.status),
    invoiceDateIndex: index('sales_invoices_invoice_date_index').on(
      t.invoiceDate,
    ),
    dueDateIndex: index('sales_invoices_due_date_index').on(t.dueDate),
    sentDateIndex: index('sales_invoices_sent_date_index').on(t.sentDate),
    paidDateIndex: index('sales_invoices_paid_date_index').on(t.paidDate),
    currencyCodeIndex: index('sales_invoices_currency_code_index').on(
      t.currencyCode,
    ),

    // Unique constraint
    uniqueInvoiceNumber: uniqueIndex('sales_invoices_invoice_number_unique')
      .on(t.businessId, t.invoiceNumber)
      .where(isNull(t.deletedAt)),

    // Composite indexes for common query patterns
    businessStatusIndex: index('sales_invoices_business_status_index')
      .on(t.businessId, t.status)
      .where(isNull(t.deletedAt)),
    businessCustomerIndex: index('sales_invoices_business_customer_index')
      .on(t.businessId, t.customerId)
      .where(isNull(t.deletedAt)),
    businessDateIndex: index('sales_invoices_business_date_index')
      .on(t.businessId, t.invoiceDate)
      .where(isNull(t.deletedAt)),
    businessTypeIndex: index('sales_invoices_business_type_index')
      .on(t.businessId, t.invoiceType)
      .where(isNull(t.deletedAt)),
    businessDueDateIndex: index('sales_invoices_business_due_date_index')
      .on(t.businessId, t.dueDate)
      .where(isNull(t.deletedAt)),

    // Audit indexes
    createdAtIndex: index('sales_invoices_created_at_index').on(t.createdAt),
    updatedAtIndex: index('sales_invoices_updated_at_index').on(t.updatedAt),
  }),
);

// Sales Invoice Lines table
export const salesInvoiceLines = pgTable(
  'sales_invoice_lines',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    invoiceId: uuid('invoice_id')
      .notNull()
      .references(() => salesInvoices.id, { onDelete: 'cascade' }),
    productId: uuid('product_id'), // References PRODUCTS table
    variantId: uuid('variant_id'), // References PRODUCT_VARIANTS table
    menuItemId: uuid('menu_item_id'), // References MENU_ITEMS table
    salesOrderLineId: uuid('sales_order_line_id').references(
      () => salesOrderLines.id,
    ),
    lineType: invoiceLineTypeEnum('line_type')
      .default(InvoiceLineType.PRODUCT)
      .notNull(),
    description: text('description').notNull(),
    quantity: decimal('quantity', { precision: 15, scale: 3 })
      .default('1.000')
      .notNull(),
    unitOfMeasure: text('unit_of_measure'),
    unitPrice: decimal('unit_price', { precision: 10, scale: 2 })
      .default('0.00')
      .notNull(),
    priceTaxType: priceTaxTypeEnum('price_tax_type')
      .default(PriceTaxType.EXCLUSIVE)
      .notNull(),
    discountPercent: decimal('discount_percent', { precision: 5, scale: 2 })
      .default('0.00')
      .notNull(),
    discountAmount: decimal('discount_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    lineSubtotal: decimal('line_subtotal', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    taxRateId: uuid('tax_rate_id'), // References TAX_RATES table
    taxAmount: decimal('tax_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    lineTotal: decimal('line_total', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    sortOrder: integer('sort_order').default(1).notNull(),
    referenceInfo: text('reference_info'),
    createdAt: timestamp('created_at').defaultNow().notNull(),
  },
  (t) => ({
    // Primary index
    idIndex: index('sales_invoice_lines_id_index').on(t.id),

    // Foreign key indexes
    businessIdIndex: index('sales_invoice_lines_business_id_index').on(
      t.businessId,
    ),
    invoiceIdIndex: index('sales_invoice_lines_invoice_id_index').on(
      t.invoiceId,
    ),
    productIdIndex: index('sales_invoice_lines_product_id_index').on(
      t.productId,
    ),
    variantIdIndex: index('sales_invoice_lines_variant_id_index').on(
      t.variantId,
    ),
    menuItemIdIndex: index('sales_invoice_lines_menu_item_id_index').on(
      t.menuItemId,
    ),
    salesOrderLineIdIndex: index(
      'sales_invoice_lines_sales_order_line_id_index',
    ).on(t.salesOrderLineId),
    taxRateIdIndex: index('sales_invoice_lines_tax_rate_id_index').on(
      t.taxRateId,
    ),

    // Performance indexes
    lineTypeIndex: index('sales_invoice_lines_line_type_index').on(t.lineType),

    // Sort order index
    sortOrderIndex: index('sales_invoice_lines_sort_order_index').on(
      t.invoiceId,
      t.sortOrder,
    ),

    // Composite indexes for common query patterns
    businessInvoiceIndex: index(
      'sales_invoice_lines_business_invoice_index',
    ).on(t.businessId, t.invoiceId),
    businessLineTypeIndex: index(
      'sales_invoice_lines_business_line_type_index',
    ).on(t.businessId, t.lineType),

    // Audit indexes
    createdAtIndex: index('sales_invoice_lines_created_at_index').on(
      t.createdAt,
    ),
  }),
);

// Cash Registers table
export const cashRegisters = pgTable(
  'cash_registers',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    registerCode: text('register_code').notNull(),
    registerName: text('register_name').notNull(),
    locationId: uuid('location_id')
      .notNull()
      .references(() => locations.id),
    ipAddress: text('ip_address'),
    deviceId: text('device_id'),
    openingFloat: decimal('opening_float', { precision: 10, scale: 2 })
      .default('0.00')
      .notNull(),

    // Audit fields
    createdBy: uuid('created_by')
      .notNull()
      .references(() => users.id),
    updatedBy: uuid('updated_by').references(() => users.id),
    deletedBy: uuid('deleted_by').references(() => users.id),
    deletedAt: timestamp('deleted_at'),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
  },
  (t) => ({
    // Primary index
    idIndex: index('cash_registers_id_index').on(t.id),

    // Foreign key indexes
    businessIdIndex: index('cash_registers_business_id_index').on(t.businessId),
    locationIdIndex: index('cash_registers_location_id_index').on(t.locationId),

    // Performance indexes
    deviceIdIndex: index('cash_registers_device_id_index').on(t.deviceId),

    // Unique constraint
    uniqueRegisterCode: uniqueIndex('cash_registers_register_code_unique')
      .on(t.businessId, t.registerCode)
      .where(isNull(t.deletedAt)),

    // Composite indexes for common query patterns
    businessLocationIndex: index('cash_registers_business_location_index')
      .on(t.businessId, t.locationId)
      .where(isNull(t.deletedAt)),

    // Audit indexes
    createdAtIndex: index('cash_registers_created_at_index').on(t.createdAt),
    updatedAtIndex: index('cash_registers_updated_at_index').on(t.updatedAt),
  }),
);

// Cash Register Sessions table
export const cashRegisterSessions = pgTable(
  'cash_register_sessions',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    registerId: uuid('register_id')
      .notNull()
      .references(() => cashRegisters.id),
    openedBy: uuid('opened_by')
      .notNull()
      .references(() => staffMembers.id),
    closedBy: uuid('closed_by').references(() => staffMembers.id),
    openTime: timestamp('open_time').notNull(),
    closeTime: timestamp('close_time'),
    openingAmount: decimal('opening_amount', { precision: 10, scale: 2 })
      .default('0.00')
      .notNull(),
    expectedCash: decimal('expected_cash', { precision: 10, scale: 2 })
      .default('0.00')
      .notNull(),
    actualCash: decimal('actual_cash', { precision: 10, scale: 2 })
      .default('0.00')
      .notNull(),
    cashDifference: decimal('cash_difference', { precision: 10, scale: 2 })
      .default('0.00')
      .notNull(),
    status: cashRegisterSessionStatusEnum('status')
      .default(CashRegisterSessionStatus.OPEN)
      .notNull(),
    notes: text('notes'),
    createdAt: timestamp('created_at').defaultNow().notNull(),
  },
  (t) => ({
    // Primary index
    idIndex: index('cash_register_sessions_id_index').on(t.id),

    // Foreign key indexes
    businessIdIndex: index('cash_register_sessions_business_id_index').on(
      t.businessId,
    ),
    registerIdIndex: index('cash_register_sessions_register_id_index').on(
      t.registerId,
    ),
    openedByIndex: index('cash_register_sessions_opened_by_index').on(
      t.openedBy,
    ),
    closedByIndex: index('cash_register_sessions_closed_by_index').on(
      t.closedBy,
    ),

    // Performance indexes
    statusIndex: index('cash_register_sessions_status_index').on(t.status),
    openTimeIndex: index('cash_register_sessions_open_time_index').on(
      t.openTime,
    ),
    closeTimeIndex: index('cash_register_sessions_close_time_index').on(
      t.closeTime,
    ),

    // Composite indexes for common query patterns
    businessRegisterIndex: index(
      'cash_register_sessions_business_register_index',
    ).on(t.businessId, t.registerId),
    businessStatusIndex: index(
      'cash_register_sessions_business_status_index',
    ).on(t.businessId, t.status),
    businessOpenTimeIndex: index(
      'cash_register_sessions_business_open_time_index',
    ).on(t.businessId, t.openTime),

    // Audit indexes
    createdAtIndex: index('cash_register_sessions_created_at_index').on(
      t.createdAt,
    ),
  }),
);

// POS Transactions table
export const posTransactions = pgTable(
  'pos_transactions',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    transactionNumber: text('transaction_number').notNull(),
    registerId: uuid('register_id')
      .notNull()
      .references(() => cashRegisters.id),
    sessionId: uuid('session_id')
      .notNull()
      .references(() => cashRegisterSessions.id),
    staffId: uuid('staff_id')
      .notNull()
      .references(() => staffMembers.id),
    customerId: uuid('customer_id').references(() => customers.id),
    salesOrderId: uuid('sales_order_id').references(() => salesOrders.id),
    transactionDate: timestamp('transaction_date').notNull(),
    subtotal: decimal('subtotal', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    taxAmount: decimal('tax_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    discountAmount: decimal('discount_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    totalAmount: decimal('total_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    tenderedAmount: decimal('tendered_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    changeAmount: decimal('change_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    transactionType: posTransactionTypeEnum('transaction_type')
      .default(PosTransactionType.SALE)
      .notNull(),
    status: posTransactionStatusEnum('status')
      .default(PosTransactionStatus.COMPLETED)
      .notNull(),

    // Audit fields
    createdBy: uuid('created_by')
      .notNull()
      .references(() => users.id),
    updatedBy: uuid('updated_by').references(() => users.id),
    deletedBy: uuid('deleted_by').references(() => users.id),
    deletedAt: timestamp('deleted_at'),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
  },
  (t) => ({
    // Primary index
    idIndex: index('pos_transactions_id_index').on(t.id),

    // Foreign key indexes
    businessIdIndex: index('pos_transactions_business_id_index').on(
      t.businessId,
    ),
    registerIdIndex: index('pos_transactions_register_id_index').on(
      t.registerId,
    ),
    sessionIdIndex: index('pos_transactions_session_id_index').on(t.sessionId),
    staffIdIndex: index('pos_transactions_staff_id_index').on(t.staffId),
    customerIdIndex: index('pos_transactions_customer_id_index').on(
      t.customerId,
    ),
    salesOrderIdIndex: index('pos_transactions_sales_order_id_index').on(
      t.salesOrderId,
    ),

    // Performance indexes
    transactionTypeIndex: index('pos_transactions_transaction_type_index').on(
      t.transactionType,
    ),
    statusIndex: index('pos_transactions_status_index').on(t.status),
    transactionDateIndex: index('pos_transactions_transaction_date_index').on(
      t.transactionDate,
    ),

    // Unique constraint
    uniqueTransactionNumber: uniqueIndex(
      'pos_transactions_transaction_number_unique',
    )
      .on(t.businessId, t.transactionNumber)
      .where(isNull(t.deletedAt)),

    // Composite indexes for common query patterns
    businessRegisterIndex: index('pos_transactions_business_register_index')
      .on(t.businessId, t.registerId)
      .where(isNull(t.deletedAt)),
    businessSessionIndex: index('pos_transactions_business_session_index')
      .on(t.businessId, t.sessionId)
      .where(isNull(t.deletedAt)),
    businessDateIndex: index('pos_transactions_business_date_index')
      .on(t.businessId, t.transactionDate)
      .where(isNull(t.deletedAt)),
    businessStaffIndex: index('pos_transactions_business_staff_index')
      .on(t.businessId, t.staffId)
      .where(isNull(t.deletedAt)),

    // Audit indexes
    createdAtIndex: index('pos_transactions_created_at_index').on(t.createdAt),
    updatedAtIndex: index('pos_transactions_updated_at_index').on(t.updatedAt),
  }),
);

// POS Transaction Lines table
export const posTransactionLines = pgTable(
  'pos_transaction_lines',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    posTransactionId: uuid('pos_transaction_id')
      .notNull()
      .references(() => posTransactions.id, { onDelete: 'cascade' }),
    productId: uuid('product_id').notNull(), // References PRODUCTS table
    variantId: uuid('variant_id'), // References PRODUCT_VARIANTS table
    serialId: uuid('serial_id'), // References SERIAL_NUMBERS table
    quantity: decimal('quantity', { precision: 15, scale: 3 }).notNull(),
    unitPrice: decimal('unit_price', { precision: 10, scale: 2 }).notNull(),
    priceTaxType: priceTaxTypeEnum('price_tax_type')
      .default(PriceTaxType.EXCLUSIVE)
      .notNull(),
    discountPercent: decimal('discount_percent', { precision: 5, scale: 2 })
      .default('0.00')
      .notNull(),
    lineSubtotal: decimal('line_subtotal', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    taxAmount: decimal('tax_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    lineTotal: decimal('line_total', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    discountApprovedBy: uuid('discount_approved_by').references(
      () => staffMembers.id,
    ),
    discountReason: text('discount_reason'),
    createdAt: timestamp('created_at').defaultNow().notNull(),
  },
  (t) => ({
    // Primary index
    idIndex: index('pos_transaction_lines_id_index').on(t.id),

    // Foreign key indexes
    businessIdIndex: index('pos_transaction_lines_business_id_index').on(
      t.businessId,
    ),
    posTransactionIdIndex: index(
      'pos_transaction_lines_pos_transaction_id_index',
    ).on(t.posTransactionId),
    productIdIndex: index('pos_transaction_lines_product_id_index').on(
      t.productId,
    ),
    variantIdIndex: index('pos_transaction_lines_variant_id_index').on(
      t.variantId,
    ),
    serialIdIndex: index('pos_transaction_lines_serial_id_index').on(
      t.serialId,
    ),
    discountApprovedByIndex: index(
      'pos_transaction_lines_discount_approved_by_index',
    ).on(t.discountApprovedBy),

    // Composite indexes for common query patterns
    businessPosTransactionIndex: index(
      'pos_transaction_lines_business_pos_transaction_index',
    ).on(t.businessId, t.posTransactionId),

    // Audit indexes
    createdAtIndex: index('pos_transaction_lines_created_at_index').on(
      t.createdAt,
    ),
  }),
);

// POS Payments table
export const posPayments = pgTable(
  'pos_payments',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    posTransactionId: uuid('pos_transaction_id')
      .notNull()
      .references(() => posTransactions.id, { onDelete: 'cascade' }),
    paymentMethod: paymentMethodEnum('payment_method').notNull(),
    amount: decimal('amount', { precision: 15, scale: 2 }).notNull(),
    referenceNumber: text('reference_number'),
    cardLastFour: text('card_last_four'),
    cardType: text('card_type'),
    tipAmount: decimal('tip_amount', { precision: 10, scale: 2 })
      .default('0.00')
      .notNull(),

    // Audit fields
    createdBy: uuid('created_by')
      .notNull()
      .references(() => users.id),
    createdAt: timestamp('created_at').defaultNow().notNull(),
  },
  (t) => ({
    // Primary index
    idIndex: index('pos_payments_id_index').on(t.id),

    // Foreign key indexes
    businessIdIndex: index('pos_payments_business_id_index').on(t.businessId),
    posTransactionIdIndex: index('pos_payments_pos_transaction_id_index').on(
      t.posTransactionId,
    ),

    // Performance indexes
    paymentMethodIndex: index('pos_payments_payment_method_index').on(
      t.paymentMethod,
    ),
    referenceNumberIndex: index('pos_payments_reference_number_index').on(
      t.referenceNumber,
    ),

    // Composite indexes for common query patterns
    businessPosTransactionIndex: index(
      'pos_payments_business_pos_transaction_index',
    ).on(t.businessId, t.posTransactionId),
    businessPaymentMethodIndex: index(
      'pos_payments_business_payment_method_index',
    ).on(t.businessId, t.paymentMethod),

    // Audit indexes
    createdAtIndex: index('pos_payments_created_at_index').on(t.createdAt),
  }),
);
