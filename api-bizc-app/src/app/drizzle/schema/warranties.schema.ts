// Combined schema for warranties, warranty templates, and warranty coverages

import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
  integer,
  date,
  timestamp,
  boolean,
  decimal,
} from 'drizzle-orm/pg-core';

import { business } from './business.schema';
import { customers } from './customers.schema';
import { products, productVariants } from './products.schema';
import { users } from './users.schema';
import { auditFields } from './common-fields.schema';

import {
  WarrantyStatus,
  WarrantySource,
  WarrantyReferenceType,
} from '../../shared/types';

// Enums from warranty-templates.schema.ts
export enum DurationType {
  DAYS = 'days',
  MONTHS = 'months',
  YEARS = 'years',
  LIFETIME = 'lifetime',
}

export enum WarrantyType {
  MANUFACTURER = 'manufacturer',
  EXTENDED = 'extended',
  SERVICE = 'service',
  REPAIR = 'repair',
}

export enum CoverageType {
  FULL = 'full',
  LIMITED = 'limited',
  PARTS_ONLY = 'parts_only',
  LABOR_ONLY = 'labor_only',
}

export const durationTypeEnum = pgEnum('duration_type', [
  DurationType.DAYS,
  DurationType.MONTHS,
  DurationType.YEARS,
  DurationType.LIFETIME,
]);

// Using the more comprehensive status enum from warranties.schema.ts
export const warrantyStatusEnum = pgEnum('warranty_status', [
  WarrantyStatus.ACTIVE,
  WarrantyStatus.INACTIVE,
  WarrantyStatus.TRANSFERRED,
  WarrantyStatus.CANCELLED,
]);

export const warrantyTypeEnum = pgEnum('warranty_type', [
  WarrantyType.MANUFACTURER,
  WarrantyType.EXTENDED,
  WarrantyType.SERVICE,
  WarrantyType.REPAIR,
]);

export const coverageTypeEnum = pgEnum('coverage_type', [
  CoverageType.FULL,
  CoverageType.LIMITED,
  CoverageType.PARTS_ONLY,
  CoverageType.LABOR_ONLY,
]);

// Enums from warranties.schema.ts
export const warrantySourceEnum = pgEnum('warranty_source', [
  WarrantySource.PRODUCT_SALE,
  WarrantySource.REPAIR_SERVICE,
  WarrantySource.STANDALONE,
]);

export const warrantyReferenceTypeEnum = pgEnum('warranty_reference_type', [
  WarrantyReferenceType.SALES_ORDER_LINE,
  WarrantyReferenceType.REPAIR_ORDER,
  WarrantyReferenceType.WARRANTY_SALE,
]);

// warranty-templates.schema.ts content
export const warrantyTemplates = pgTable(
  'warranty_templates',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    templateCode: text('template_code').notNull(),
    templateName: text('template_name').notNull(),
    warrantyType: warrantyTypeEnum('warranty_type').notNull(),
    duration: integer('duration'),
    durationType: durationTypeEnum('duration_type').notNull(),
    coverageType: coverageTypeEnum('coverage_type').notNull(),
    coverageDetails: text('coverage_details'),
    maxClaims: integer('max_claims'),
    termsConditions: text('terms_conditions'),
    isTransferable: boolean('is_transferable').default(false).notNull(),
    autoApply: boolean('auto_apply').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    createdBy: uuid('created_by')
      .notNull()
      .references(() => users.id),
    updatedBy: uuid('updated_by').references(() => users.id),
    deletedBy: uuid('deleted_by').references(() => users.id),
    status: warrantyStatusEnum('status') // This will use the merged enum
      .default(WarrantyStatus.ACTIVE)
      .notNull(),
    deletedAt: timestamp('deleted_at'),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
  },
  (t) => ({
    idIndex: index('warranty_templates_id_index').on(t.id),
    templateCodeIndex: index('warranty_templates_template_code_index').on(
      t.templateCode,
    ),
    warrantyTypeIndex: index('warranty_templates_warranty_type_index').on(
      t.warrantyType,
    ),
    businessIdIndex: index('warranty_templates_business_id_index').on(
      t.businessId,
    ),
    statusIndex: index('warranty_templates_status_index').on(t.status),
    uniqueTemplateCode: uniqueIndex(
      'warranty_templates_template_code_unique',
    ).on(t.templateCode),
    uniqueBusinessTemplateName: uniqueIndex(
      'warranty_templates_business_template_name_unique',
    ).on(t.businessId, t.templateName),
  }),
);

// warranties.schema.ts content
export const warranties = pgTable(
  'warranties',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    warrantyNumber: text('warranty_number').notNull(),
    warrantyType: warrantyTypeEnum('warranty_type').notNull(),
    warrantySource: warrantySourceEnum('warranty_source').notNull(),
    duration: integer('duration'),
    durationType: durationTypeEnum('duration_type').notNull(),

    // References
    templateId: uuid('template_id').references(() => warrantyTemplates.id),
    referenceId: uuid('reference_id'), // FK to sales_order_line_id, repair_order_id, etc.
    referenceType: warrantyReferenceTypeEnum('reference_type'),
    productId: uuid('product_id').references(() => products.id),
    productVariantId: uuid('product_variant_id').references(
      () => productVariants.id,
    ),
    serialId: uuid('serial_id'), // Reference to serial number when implemented
    customerId: uuid('customer_id')
      .notNull()
      .references(() => customers.id),

    // Device and warranty details
    deviceInfo: text('device_info'),
    startDate: date('start_date').notNull(),
    expiryDate: date('expiry_date').notNull(),
    coverageType: coverageTypeEnum('coverage_type').notNull(),
    coverageDetails: text('coverage_details'),
    maxClaims: integer('max_claims').default(1),
    claimsUsed: integer('claims_used').default(0),

    // Status and transfer information
    status: warrantyStatusEnum('status')
      .default(WarrantyStatus.ACTIVE)
      .notNull(),
    originalWarrantyId: uuid('original_warranty_id').references(
      () => warranties.id,
    ), // For transferred warranties
    transferredToCustomerId: uuid('transferred_to_customer_id').references(
      () => customers.id,
    ),
    transferredDate: date('transferred_date'),
    termsConditions: text('terms_conditions'),

    // Business context
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),

    // Audit fields
    ...auditFields,
  },
  (t) => ({
    idIndex: index('warranties_id_index').on(t.id),
    warrantyNumberIndex: index('warranties_warranty_number_index').on(
      t.warrantyNumber,
    ),
    warrantyTypeIndex: index('warranties_warranty_type_index').on(
      t.warrantyType,
    ),
    warrantySourceIndex: index('warranties_warranty_source_index').on(
      t.warrantySource,
    ),
    customerIdIndex: index('warranties_customer_id_index').on(t.customerId),
    productIdIndex: index('warranties_product_id_index').on(t.productId),
    productVariantIdIndex: index('warranties_product_variant_id_index').on(
      t.productVariantId,
    ),
    templateIdIndex: index('warranties_template_id_index').on(t.templateId),
    statusIndex: index('warranties_status_index').on(t.status),
    businessIdIndex: index('warranties_business_id_index').on(t.businessId),
    startDateIndex: index('warranties_start_date_index').on(t.startDate),
    expiryDateIndex: index('warranties_expiry_date_index').on(t.expiryDate),
    originalWarrantyIdIndex: index('warranties_original_warranty_id_index').on(
      t.originalWarrantyId,
    ),
    transferredToCustomerIndex: index(
      'warranties_transferred_to_customer_index',
    ).on(t.transferredToCustomerId),

    // Unique constraints
    uniqueWarrantyNumber: uniqueIndex('warranties_warranty_number_unique').on(
      t.warrantyNumber,
    ),
    uniqueBusinessWarrantyNumber: uniqueIndex(
      'warranties_business_warranty_number_unique',
    ).on(t.businessId, t.warrantyNumber),
  }),
);

// Self-referencing relation for transferred warranties
export const warrantyRelations = {
  originalWarranty: warranties.originalWarrantyId,
};

// warranty-coverages.schema.ts content
export const warrantyCoverages = pgTable(
  'warranty_coverages',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    warrantyId: uuid('warranty_id')
      .notNull()
      .references(() => warranties.id, { onDelete: 'cascade' }),
    coverageItem: text('coverage_item').notNull(),
    isCovered: boolean('is_covered').default(true).notNull(),
    coverageLimit: decimal('coverage_limit', { precision: 10, scale: 2 }),

    // Coverage period fields
    coverageStartDate: date('coverage_start_date'),
    coverageExpiryDate: date('coverage_expiry_date'),
    duration: integer('duration'),
    durationType: durationTypeEnum('duration_type').notNull(),

    exclusions: text('exclusions'),

    // Audit fields
    ...auditFields,
  },
  (t) => ({
    idIndex: index('warranty_coverages_id_index').on(t.id),
    warrantyIdIndex: index('warranty_coverages_warranty_id_index').on(
      t.warrantyId,
    ),
    coverageItemIndex: index('warranty_coverages_coverage_item_index').on(
      t.coverageItem,
    ),
    isCoveredIndex: index('warranty_coverages_is_covered_index').on(
      t.isCovered,
    ),
    coverageStartDateIndex: index('warranty_coverages_start_date_index').on(
      t.coverageStartDate,
    ),
    coverageExpiryDateIndex: index('warranty_coverages_expiry_date_index').on(
      t.coverageExpiryDate,
    ),

    // Unique constraint - one record per warranty and coverage item
    uniqueWarrantyCoverage: uniqueIndex(
      'warranty_coverages_warranty_coverage_unique',
    ).on(t.warrantyId, t.coverageItem),
  }),
);
