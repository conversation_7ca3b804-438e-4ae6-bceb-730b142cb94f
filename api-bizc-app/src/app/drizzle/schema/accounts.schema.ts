import {
  index,
  pgTable,
  text,
  timestamp,
  uuid,
  pgEnum,
  uniqueIndex,
  boolean,
  decimal,
  date,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { users } from './users.schema';
import { business } from './business.schema';
import { taxes } from './taxes.schema';
import {
  AccountCategory,
  AccountDetailType,
  ChartAccountType,
} from '@app/shared/types/account.enum';

// TypeScript Enum for Account Status
export enum AccountStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

// Account Category Enum - Main categories from chart of accounts
export const accountCategoryEnum = pgEnum('account_category', [
  AccountCategory.ASSETS,
  AccountCategory.LIABILITIES,
  AccountCategory.EQUITY,
  AccountCategory.REVENUE,
  AccountCategory.EXPENSES,
]);

// Account Type Enum - Account types from the hierarchy
export const chartAccountTypeEnum = pgEnum('chart_account_type', [
  ChartAccountType.ACCOUNTS_RECEIVABLE,
  ChartAccountType.CURRENT_ASSETS,
  ChartAccountType.CASH_AND_CASH_EQUIVALENTS,
  ChartAccountType.FIXED_ASSETS,
  ChartAccountType.NON_CURRENT_ASSETS,
  ChartAccountType.ACCOUNTS_PAYABLE,
  ChartAccountType.CREDIT_CARD,
  ChartAccountType.CURRENT_LIABILITIES,
  ChartAccountType.NON_CURRENT_LIABILITIES,
  ChartAccountType.OWNERS_EQUITY,
  ChartAccountType.INCOME,
  ChartAccountType.OTHER_INCOME,
  ChartAccountType.COST_OF_SALES,
  ChartAccountType.EXPENSES,
  ChartAccountType.OTHER_EXPENSE,
]);

// Account Detail Type Enum - Specific detail types for each account type
export const accountDetailTypeEnum = pgEnum('account_detail_type', [
  // Accounts receivable (A/R)
  AccountDetailType.ACCOUNT_RECEIVABLE,

  // Current assets
  AccountDetailType.ALLOWANCE_FOR_BAD_DEBTS,
  AccountDetailType.ASSETS_AVAILABLE_FOR_SALE,
  AccountDetailType.DEVELOPMENT_COSTS,
  AccountDetailType.EMPLOYEE_CASH_ADVANCES,
  AccountDetailType.INVENTORY,
  AccountDetailType.INVESTMENTS_OTHER,
  AccountDetailType.LOANS_TO_OFFICERS,
  AccountDetailType.LOANS_TO_OTHERS,
  AccountDetailType.LOANS_TO_SHAREHOLDERS,
  AccountDetailType.OTHER_CURRENT_ASSETS,
  AccountDetailType.PREPAID_EXPENSES,
  AccountDetailType.RETAINAGE,
  AccountDetailType.UNDEPOSITED_FUNDS,

  // Cash and cash equivalents
  AccountDetailType.BANK,
  AccountDetailType.CASH_AND_CASH_EQUIVALENTS,
  AccountDetailType.CASH_ON_HAND,
  AccountDetailType.CLIENT_TRUST_ACCOUNT,
  AccountDetailType.MONEY_MARKET,
  AccountDetailType.RENTS_HELD_IN_TRUST,
  AccountDetailType.SAVINGS,

  // Fixed assets
  AccountDetailType.ACCUMULATED_DEPLETION,
  AccountDetailType.ACCUMULATED_DEPRECIATION_PPE,
  AccountDetailType.BUILDINGS,
  AccountDetailType.DEPLETABLE_ASSETS,
  AccountDetailType.FURNITURE_AND_FIXTURES,
  AccountDetailType.LAND,
  AccountDetailType.LEASEHOLD_IMPROVEMENTS,
  AccountDetailType.MACHINERY_AND_EQUIPMENT,
  AccountDetailType.OTHER_FIXED_ASSETS,
  AccountDetailType.VEHICLES,

  // Non-current assets
  AccountDetailType.ACCUMULATED_AMORTIZATION_NON_CURRENT,
  AccountDetailType.ASSETS_HELD_FOR_SALE,
  AccountDetailType.DEFERRED_TAX,
  AccountDetailType.GOODWILL,
  AccountDetailType.INTANGIBLE_ASSETS,
  AccountDetailType.LEASE_BUYOUT,
  AccountDetailType.LICENCES,
  AccountDetailType.LONG_TERM_INVESTMENTS,
  AccountDetailType.ORGANISATIONAL_COSTS,
  AccountDetailType.OTHER_NON_CURRENT_ASSETS,
  AccountDetailType.SECURITY_DEPOSITS,

  // Accounts payable (A/P)
  AccountDetailType.ACCOUNT_PAYABLE,

  // Credit Card
  AccountDetailType.CREDIT_CARD,

  // Current liabilities
  AccountDetailType.ACCRUED_LIABILITIES,
  AccountDetailType.CLIENT_TRUST_ACCOUNTS_LIABILITIES,
  AccountDetailType.CURRENT_TAX_LIABILITY,
  AccountDetailType.CURRENT_PORTION_FINANCE_LEASES,
  AccountDetailType.DIVIDENDS_PAYABLE,
  AccountDetailType.INCOME_TAX_PAYABLE,
  AccountDetailType.INSURANCE_PAYABLE,
  AccountDetailType.LINE_OF_CREDIT,
  AccountDetailType.LOAN_PAYABLE,
  AccountDetailType.OTHER_CURRENT_LIABILITIES,
  AccountDetailType.PAYROLL_CLEARING,
  AccountDetailType.PAYROLL_LIABILITIES,
  AccountDetailType.PREPAID_EXPENSES_PAYABLE,
  AccountDetailType.RENTS_IN_TRUST_LIABILITY,
  AccountDetailType.SALES_SERVICE_TAX_PAYABLE,

  // Non-current liabilities
  AccountDetailType.ACCRUED_NON_CURRENT_LIABILITIES,
  AccountDetailType.ACCRUED_VACATION_PAYABLE,
  AccountDetailType.LIABILITIES_ASSETS_HELD_FOR_SALE,
  AccountDetailType.LONG_TERM_DEBT,
  AccountDetailType.NOTES_PAYABLE,
  AccountDetailType.OTHER_NON_CURRENT_LIABILITIES,
  AccountDetailType.SHAREHOLDER_NOTES_PAYABLE,

  // Owner's Equity
  AccountDetailType.ACCUMULATED_ADJUSTMENT,
  AccountDetailType.DIVIDEND_DISBURSED,
  AccountDetailType.EQUITY_IN_EARNINGS_SUBSIDIARIES,
  AccountDetailType.OPENING_BALANCE_EQUITY,
  AccountDetailType.ORDINARY_SHARES,
  AccountDetailType.OTHER_COMPREHENSIVE_INCOME,
  AccountDetailType.OWNERS_EQUITY,
  AccountDetailType.PAID_IN_CAPITAL_SURPLUS,
  AccountDetailType.PARTNER_CONTRIBUTIONS,
  AccountDetailType.PARTNER_DISTRIBUTIONS,
  AccountDetailType.PARTNERS_EQUITY,
  AccountDetailType.PREFERRED_SHARES,
  AccountDetailType.SHARE_CAPITAL,
  AccountDetailType.TREASURY_SHARES,

  // Income
  AccountDetailType.DISCOUNTS_REFUNDS_GIVEN,
  AccountDetailType.NON_PROFIT_INCOME,
  AccountDetailType.OTHER_PRIMARY_INCOME,
  AccountDetailType.REVENUE_GENERAL,
  AccountDetailType.SALES_RETAIL,
  AccountDetailType.SALES_WHOLESALE,
  AccountDetailType.SALES_OF_PRODUCT_INCOME,
  AccountDetailType.SERVICE_FEE_INCOME,
  AccountDetailType.UNAPPLIED_CASH_PAYMENT_INCOME,

  // Other Income
  AccountDetailType.DIVIDEND_INCOME,
  AccountDetailType.INTEREST_EARNED,
  AccountDetailType.LOSS_ON_DISPOSAL_ASSETS,
  AccountDetailType.OTHER_INVESTMENT_INCOME,
  AccountDetailType.OTHER_MISCELLANEOUS_INCOME,
  AccountDetailType.OTHER_OPERATING_INCOME,
  AccountDetailType.TAX_EXEMPT_INTEREST,
  AccountDetailType.UNREALISED_LOSS_SECURITIES,

  // Cost Of Sales
  AccountDetailType.COST_OF_LABOR_COS,
  AccountDetailType.EQUIPMENT_RENTAL_COS,
  AccountDetailType.FREIGHT_DELIVERY_COS,
  AccountDetailType.OTHER_COSTS_OF_SALES,
  AccountDetailType.SUPPLIES_MATERIALS_COS,

  // Expenses
  AccountDetailType.ADVERTISING_PROMOTIONAL,
  AccountDetailType.AMORTIZATION_EXPENSE,
  AccountDetailType.AUTO,
  AccountDetailType.BAD_DEBTS,
  AccountDetailType.BANK_CHARGES,
  AccountDetailType.CHARITABLE_CONTRIBUTIONS,
  AccountDetailType.COMMISSIONS_AND_FEES,
  AccountDetailType.COST_OF_LABOR,
  AccountDetailType.DUES_AND_SUBSCRIPTIONS,
  AccountDetailType.EQUIPMENT_RENTAL,
  AccountDetailType.FINANCE_COSTS,
  AccountDetailType.INCOME_TAX_EXPENSE,
  AccountDetailType.INSURANCE,
  AccountDetailType.INTEREST_PAID,
  AccountDetailType.LEGAL_PROFESSIONAL_FEES,
  AccountDetailType.LOSS_ON_DISCONTINUED_OPERATIONS,
  AccountDetailType.MANAGEMENT_COMPENSATION,
  AccountDetailType.MEALS_AND_ENTERTAINMENT,
  AccountDetailType.OFFICE_GENERAL_ADMIN_EXPENSES,
  AccountDetailType.OTHER_MISCELLANEOUS_SERVICE_COST,
  AccountDetailType.OTHER_SELLING_EXPENSES,
  AccountDetailType.PAYROLL_EXPENSES,
  AccountDetailType.RENT_LEASE_BUILDINGS,
  AccountDetailType.REPAIR_AND_MAINTENANCE,
  AccountDetailType.SHIPPING_DELIVERY_EXPENSE,
  AccountDetailType.SUPPLIES_AND_MATERIALS,
  AccountDetailType.TAXES_PAID,
  AccountDetailType.TRAVEL_EXPENSES_ADMIN,
  AccountDetailType.TRAVEL_EXPENSES_SELLING,
  AccountDetailType.UNAPPLIED_CASH_BILL_PAYMENT_EXPENSE,
  AccountDetailType.UTILITIES,

  // Other Expenses
  AccountDetailType.AMORTIZATION,
  AccountDetailType.DEPRECIATION,
  AccountDetailType.EXCHANGE_GAIN_OR_LOSS,
  AccountDetailType.OTHER_EXPENSE,
  AccountDetailType.PENALTIES_AND_SETTLEMENTS,
]);

// Account Status Enum
export const accountStatusEnum = pgEnum('account_status', [
  AccountStatus.ACTIVE,
  AccountStatus.INACTIVE,
]);

export const accounts = pgTable(
  'accounts',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),

    // Account identification
    accountName: text('account_name').notNull(),
    accountNumber: text('account_number'),

    // Account classification
    accountCategory: accountCategoryEnum('account_category').notNull(),
    accountType: chartAccountTypeEnum('chart_account_type').notNull(),
    accountDetailType: accountDetailTypeEnum('account_detail_type').notNull(),

    // Hierarchical structure
    parentAccountId: uuid('parent_account_id').references(() => accounts.id),

    // Financial details
    openingBalance: decimal('opening_balance', {
      precision: 15,
      scale: 2,
    }).default('0.00'),
    openingBalanceDate: date('opening_balance_date'),

    // Additional information
    description: text('description'),
    defaultTaxId: uuid('default_tax_id').references(() => taxes.id), // Reference to tax

    // Account usage settings
    useForBillableExpenses: boolean('use_for_billable_expenses')
      .default(false)
      .notNull(),
    incomeAccountId: uuid('income_account_id').references(() => accounts.id), // Reference to income account

    // System flags
    isSystemAccount: boolean('is_system_account').default(false).notNull(),

    // Status
    status: accountStatusEnum('status').default(AccountStatus.ACTIVE).notNull(),

    // Audit fields
    createdBy: uuid('created_by')
      .notNull()
      .references(() => users.id),
    updatedBy: uuid('updated_by').references(() => users.id),
    deletedBy: uuid('deleted_by').references(() => users.id),
    deletedAt: timestamp('deleted_at'),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
  },
  (t) => ({
    // Indexes for performance
    businessIdIndex: index('accounts_business_id_index').on(t.businessId),
    accountTypeIndex: index('accounts_account_type_index').on(t.accountType),
    accountCategoryIndex: index('accounts_account_category_index').on(
      t.accountCategory,
    ),
    parentAccountIdIndex: index('accounts_parent_account_id_index').on(
      t.parentAccountId,
    ),
    statusIndex: index('accounts_status_index').on(t.status),

    // Unique constraints
    uniqueAccountNumberPerBusiness: uniqueIndex(
      'accounts_unique_account_number_per_business',
    )
      .on(t.businessId, t.accountNumber)
      .where(sql`${t.accountNumber} IS NOT NULL AND ${t.deletedAt} IS NULL`),
    uniqueAccountNamePerBusiness: uniqueIndex(
      'accounts_unique_account_name_per_business',
    )
      .on(t.businessId, t.accountName)
      .where(sql`${t.deletedAt} IS NULL`),
  }),
);
