import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
  integer,
  decimal,
} from 'drizzle-orm/pg-core';
import { isNull } from 'drizzle-orm';
import { business } from './business.schema';
import { media } from './media.schema';
import { accounts } from './accounts.schema';
import { taxes } from './taxes.schema';
import { auditFields } from './common-fields.schema';
import { TaxType } from '@app/shared/types/common.enum';

// Package Status TypeScript Enum
export enum PackageStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DRAFT = 'draft',
  ARCHIVED = 'archived',
}

// Package Type TypeScript Enum
export enum PackageType {
  TOUR_PACKAGE = 'tour_package',
  ACCOMMODATION_PACKAGE = 'accommodation_package',
  TRANSPORT_PACKAGE = 'transport_package',
  MEAL_PACKAGE = 'meal_package',
  ACTIVITY_PACKAGE = 'activity_package',
  HONEYMOON_PACKAGE = 'honeymoon_package',
  FAMILY_PACKAGE = 'family_package',
  ADVENTURE_PACKAGE = 'adventure_package',
  CULTURAL_PACKAGE = 'cultural_package',
  BUSINESS_PACKAGE = 'business_package',
  GROUP_PACKAGE = 'group_package',
  CUSTOM_PACKAGE = 'custom_package',
}

// Package Status Enum
export const packageStatusEnum = pgEnum('package_status', [
  PackageStatus.ACTIVE,
  PackageStatus.INACTIVE,
  PackageStatus.DRAFT,
  PackageStatus.ARCHIVED,
]);

// Package Type Enum
export const packageTypeEnum = pgEnum('package_type', [
  PackageType.TOUR_PACKAGE,
  PackageType.ACCOMMODATION_PACKAGE,
  PackageType.TRANSPORT_PACKAGE,
  PackageType.MEAL_PACKAGE,
  PackageType.ACTIVITY_PACKAGE,
  PackageType.HONEYMOON_PACKAGE,
  PackageType.FAMILY_PACKAGE,
  PackageType.ADVENTURE_PACKAGE,
  PackageType.CULTURAL_PACKAGE,
  PackageType.BUSINESS_PACKAGE,
  PackageType.GROUP_PACKAGE,
  PackageType.CUSTOM_PACKAGE,
]);

// Tax Type Enum
export const taxTypeEnum = pgEnum('tax_type', [
  TaxType.INCLUSIVE,
  TaxType.EXCLUSIVE,
  TaxType.OUT_OF_SCOPE,
]);

// Packages table
export const packages = pgTable(
  'packages',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),

    // Basic package information
    packageCode: text('package_code').notNull(), // Unique identifier like "PKG-001"
    name: text('name').notNull(), // Display name like "Wedding Package"
    shortDescription: text('short_description'),
    description: text('description'),

    // Package classification
    type: packageTypeEnum('type').default(PackageType.TOUR_PACKAGE).notNull(),

    // Package status
    status: packageStatusEnum('status').default(PackageStatus.ACTIVE).notNull(),

    // Pricing information
    basePrice: decimal('base_price', { precision: 12, scale: 2 })
      .default('0.00')
      .notNull(),
    salePrice: decimal('sale_price', { precision: 12, scale: 2 })
      .default('0.00')
      .notNull(),
    cost: decimal('cost', { precision: 12, scale: 2 })
      .default('0.00')
      .notNull(),

    // Account references
    incomeAccountId: uuid('income_account_id')
      .notNull()
      .references(() => accounts.id),
    expenseAccountId: uuid('expense_account_id')
      .notNull()
      .references(() => accounts.id),

    // SEO fields
    seoTitle: text('seo_title'),
    seoDescription: text('seo_description'),
    seoKeywords: text('seo_keywords').array(),
    ogImage: uuid('og_image').references(() => media.id),

    // Position for ordering
    position: integer('position').default(0).notNull(),

    // Tax information
    taxType: taxTypeEnum('tax_type').default(TaxType.INCLUSIVE).notNull(),
    defaultTaxRateId: uuid('default_tax_rate_id').references(() => taxes.id),

    // Custom key fields
    key1: text('key1'),
    key2: text('key2'),
    key3: text('key3'),
    key4: text('key4'),
    key5: text('key5'),
    key6: text('key6'),
    key7: text('key7'),
    key8: text('key8'),
    key9: text('key9'),
    key10: text('key10'),

    // Standard audit fields
    ...auditFields,
  },
  (t) => ({
    // Indexes for performance
    businessIdIndex: index('packages_business_id_index').on(t.businessId),
    packageCodeIndex: index('packages_package_code_index').on(t.packageCode),
    nameIndex: index('packages_name_index').on(t.name),
    typeIndex: index('packages_type_index').on(t.type),
    statusIndex: index('packages_status_index').on(t.status),

    ogImageIndex: index('packages_og_image_index').on(t.ogImage),

    positionIndex: index('packages_position_index').on(t.position),

    incomeAccountIdIndex: index('packages_income_account_id_index').on(
      t.incomeAccountId,
    ),
    expenseAccountIdIndex: index('packages_expense_account_id_index').on(
      t.expenseAccountId,
    ),
    defaultTaxRateIdIndex: index('packages_default_tax_rate_id_index').on(
      t.defaultTaxRateId,
    ),

    // Unique constraints with soft deletion support
    uniqueBusinessPackageCode: uniqueIndex(
      'packages_business_package_code_unique',
    )
      .on(t.businessId, t.packageCode)
      .where(isNull(t.deletedAt)),
    uniqueBusinessName: uniqueIndex('packages_business_name_unique')
      .on(t.businessId, t.name)
      .where(isNull(t.deletedAt)),

    // Composite indexes for common queries
    businessStatusIndex: index('packages_business_status_index').on(
      t.businessId,
      t.status,
    ),
    businessTypeStatusIndex: index('packages_business_type_status_index').on(
      t.businessId,
      t.type,
      t.status,
    ),
  }),
);
