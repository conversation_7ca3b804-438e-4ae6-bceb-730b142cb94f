import {
  index,
  pgTable,
  text,
  timestamp,
  uuid,
  pgEnum,
  uniqueIndex,
  boolean,
  decimal,
  date,
  integer,
} from 'drizzle-orm/pg-core';
import { isNull } from 'drizzle-orm';
import { business } from './business.schema';
import { customers } from './customers.schema';
import { taxes } from './taxes.schema';
import { media } from './media.schema';
import { products } from './products.schema';
import { auditFields } from './common-fields.schema';
import { CurrencyCode, TaxType } from '../../shared/types';
import { EstimateStatus } from '@app/shared/types/estimate.enum';

// Estimate Status Enum
export const estimateStatusEnum = pgEnum('estimate_status', [
  EstimateStatus.PENDING,
  EstimateStatus.ACCEPTED,
  EstimateStatus.CLOSED,
  EstimateStatus.REJECTED,
]);

// Currency Code Enum for estimates
export const estimateCurrencyCodeEnum = pgEnum('estimate_currency_code', [
  CurrencyCode.USD,
  CurrencyCode.EUR,
  CurrencyCode.GBP,
  CurrencyCode.JPY,
  CurrencyCode.AUD,
  CurrencyCode.CAD,
  CurrencyCode.CHF,
  CurrencyCode.CNY,
  CurrencyCode.INR,
  CurrencyCode.LKR,
]);

// Tax Type Enum for estimates
export const estimateTaxTypeEnum = pgEnum('estimate_tax_type', [
  TaxType.INCLUSIVE,
  TaxType.EXCLUSIVE,
  TaxType.OUT_OF_SCOPE,
]);

// Main estimates table
export const estimates = pgTable(
  'estimates',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),

    // Customer relationship
    customerId: uuid('customer_id')
      .notNull()
      .references(() => customers.id),

    // Estimate identification
    estimateNo: text('estimate_no').notNull(),
    estimateDate: date('estimate_date').notNull(),
    expirationDate: date('expiration_date'),

    // Status
    status: estimateStatusEnum('status')
      .default(EstimateStatus.PENDING)
      .notNull(),

    // Status change tracking
    statusChangedBy: text('status_changed_by'),
    statusChangedAt: timestamp('status_changed_at'),

    // Financial calculations
    subtotal: decimal('subtotal', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    taxTotal: decimal('tax_total', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    total: decimal('total', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    estimateTotal: decimal('estimate_total', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),

    // Email configuration
    recipientEmail: text('recipient_email'),
    ccEmails: text('cc_emails').array(),
    bccEmails: text('bcc_emails').array(),
    sendLater: boolean('send_later').default(false).notNull(),

    // Tax and currency settings
    taxType: estimateTaxTypeEnum('tax_type')
      .default(TaxType.EXCLUSIVE)
      .notNull(),
    currency: estimateCurrencyCodeEnum('currency')
      .default(CurrencyCode.USD)
      .notNull(),
    billingAddress: text('billing_address'),

    // Messages
    messageOnEstimate: text('message_on_estimate'),
    messageOnStatement: text('message_on_statement'),

    // Standard audit fields
    ...auditFields,
  },
  (t) => ({
    idIndex: index('estimates_id_index').on(t.id),
    businessIdIndex: index('estimates_business_id_index').on(t.businessId),
    customerIdIndex: index('estimates_customer_id_index').on(t.customerId),
    estimateNoIndex: index('estimates_estimate_no_index').on(t.estimateNo),
    estimateDateIndex: index('estimates_estimate_date_index').on(
      t.estimateDate,
    ),
    statusIndex: index('estimates_status_index').on(t.status),
    createdByIndex: index('estimates_created_by_index').on(t.createdBy),
    // Unique constraint for estimate number with soft deletion support
    uniqueEstimateNo: uniqueIndex('estimates_estimate_no_unique')
      .on(t.businessId, t.estimateNo)
      .where(isNull(t.deletedAt)),
    // Composite indexes for common queries
    businessCustomerIndex: index('estimates_business_customer_index').on(
      t.businessId,
      t.customerId,
    ),
    businessStatusIndex: index('estimates_business_status_index').on(
      t.businessId,
      t.status,
    ),
  }),
);

// Estimate line items table
export const estimateLineItems = pgTable(
  'estimate_line_items',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    estimateId: uuid('estimate_id')
      .notNull()
      .references(() => estimates.id, { onDelete: 'cascade' }),

    // Line item details
    lineNumber: integer('line_number').notNull(),
    serviceDate: date('service_date'),
    productServiceId: uuid('product_service_id').references(() => products.id),
    description: text('description'),

    // Quantity and pricing
    quantity: decimal('quantity', { precision: 15, scale: 4 })
      .default('1.0000')
      .notNull(),
    rate: decimal('rate', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    subtotal: decimal('subtotal', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    amount: decimal('amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),

    // Tax information
    taxId: uuid('tax_id').references(() => taxes.id),
    taxRate: decimal('tax_rate', { precision: 5, scale: 4 }).default('0.0000'),
    taxAmount: decimal('tax_amount', { precision: 15, scale: 2 }).default(
      '0.00',
    ),

    // Standard audit fields
    ...auditFields,
  },
  (t) => ({
    idIndex: index('estimate_line_items_id_index').on(t.id),
    estimateIdIndex: index('estimate_line_items_estimate_id_index').on(
      t.estimateId,
    ),
    productServiceIdIndex: index(
      'estimate_line_items_product_service_id_index',
    ).on(t.productServiceId),
    taxIdIndex: index('estimate_line_items_tax_id_index').on(t.taxId),
    lineNumberIndex: index('estimate_line_items_line_number_index').on(
      t.lineNumber,
    ),
    // Unique constraint for line number within estimate
    uniqueEstimateLineNumber: uniqueIndex(
      'estimate_line_items_estimate_line_unique',
    ).on(t.estimateId, t.lineNumber),
    // Composite indexes for common queries
    estimateLineIndex: index('estimate_line_items_estimate_line_index').on(
      t.estimateId,
      t.lineNumber,
    ),
  }),
);

// Junction table for estimate attachments (many-to-many relationship)
export const estimateAttachments = pgTable(
  'estimate_attachments',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    estimateId: uuid('estimate_id')
      .notNull()
      .references(() => estimates.id, { onDelete: 'cascade' }),
    mediaId: uuid('media_id')
      .notNull()
      .references(() => media.id, { onDelete: 'cascade' }),
    attachToEmail: boolean('attach_to_email').default(false).notNull(),
    // Standard audit fields
    ...auditFields,
  },
  (t) => ({
    estimateIdIndex: index('estimate_attachments_estimate_id_index').on(
      t.estimateId,
    ),
    mediaIdIndex: index('estimate_attachments_media_id_index').on(t.mediaId),
    attachToEmailIndex: index('estimate_attachments_attach_to_email_index').on(
      t.attachToEmail,
    ),
    uniqueEstimateMedia: uniqueIndex('estimate_attachments_unique').on(
      t.estimateId,
      t.mediaId,
    ),
  }),
);
