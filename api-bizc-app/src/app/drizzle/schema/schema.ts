import { relations } from 'drizzle-orm';
import {
  users,
  userStatusEnum,
  userAccounts,
  userTwoFactor,
  userRefreshTokens,
  userPasswordResetTokens,
  userEmailVerificationTokens,
  userRoleEnum,
  twoFactorMethodEnum,
  userAccountTypeEnum,
} from './users.schema';
import { refreshTokens } from './refresh-token.schema';
import {
  business,
  businessStatusEnum,
  businessTypeEnum,
} from './business.schema';
import { addresses } from './address.schema';
import { brands, brandStatusEnum } from './brands.schema';
import {
  customerGroups,
  customerGroupStatusEnum,
} from './customer-groups.schema';
import {
  customers,
  customerLocations,
  customerRewards,
  pointTransactions,
} from './customers.schema';
import {
  warrantyTemplates,
  durationTypeEnum,
  warrantyTypeEnum,
  coverageTypeEnum,
} from './warranties.schema';
import { warranties, warrantyStatusEnum } from './warranties.schema';
import {
  categories,
  categoryLocations,
  categoryStatusEnum,
} from './categories.schema';
import {
  rentalItemCategories,
  rentalItemCategoryLocations,
  rentalItemCategoryStatusEnum,
} from './rental-item-categories.schema';
import { rentalItems, rentalItemStatusEnum } from './rental-items.schema';
import {
  equipmentTypes,
  equipmentTypeLocations,
  equipmentTypeStatusEnum,
} from './equipment-types.schema';
import {
  services,
  serviceStatusEnum,
  serviceStaff,
  serviceLocations,
} from './services.schema';
import {
  serviceCategories,
  serviceCategoryLocations,
  serviceCategoryStatusEnum,
} from './service-categories.schema';
import { serviceOrderPriorities } from './service-order-priorities.schema';
import {
  serviceOrderStatuses,
  statusTypeEnum,
} from './service-order-statuses.schema';
import {
  serviceOrderTypes,
  serviceCategoryEnum,
  serviceOrderTypeStatusEnum,
} from './service-order-types.schema';
import {
  ServiceOrderStatusType,
  ServiceCategory,
  ServiceOrderTypeStatus,
} from '../../shared/types/service-order.enum';
import { moduleTypeEnum, businessModules } from './modules.schema';
import { activityLog, activityLogNameEnum } from './activity-log.schema';
import { departments, departmentStatusEnum } from './departments.schema';
import { designations, designationStatusEnum } from './designations.schema';
import {
  staffMembers,
  staffStatusEnum,
  employmentTypeEnum,
  staffEmergencyContacts,
  staffFamilyDetails,
  staffPhysicalInfo,
  maritalStatusEnum,
  genderEnum,
  bloodTypeEnum,
  visionConditionEnum,
  hearingConditionEnum,
  limbConditionEnum,
} from './staff.schema';
import { timeSlots } from './time-slots.schema';
import { restaurantTimeSlots } from './restaurant-time-slots.schema';
import {
  staffInvitations,
  invitationStatusEnum,
  invitationDocumentStatusEnum,
} from './staff-invitations.schema';
import {
  userInvitations,
  userInvitationStatusEnum,
  userInvitationDocumentStatusEnum,
} from './user-invitations.schema';
import { performanceReviews } from './performance-reviews.schema';
import { attendance, attendanceStatusEnum } from './attendance.schema';
import { businessRoles, businessRoleStatusEnum } from './business-roles.schema';
import {
  userBusinessRoles,
  userBusinessRoleStatusEnum,
} from './user-business-roles.schema';
import {
  businessUsers,
  businessUserStatusEnum,
  businessUserRoleEnum,
} from './business-users.schema';
import {
  locations,
  locationTypeEnum,
  locationStatusEnum,
} from './locations.schema';
import { userLocations, userLocationStatusEnum } from './user-locations.schema';
import { variationTemplates } from './variation-templates.schema';
import { variationValueTemplates } from './variation-value-templates.schema';
import { modifierGroups } from './modifier-groups.schema';
import { modifiers } from './modifiers.schema';
import {
  restaurantMenuItems,
  restaurantMenuItemStatusEnum,
} from './restaurant-menu-items.schema';
import { recipes } from './menu-items.schema';
import {
  floorPlans,
  restaurantTables,
  tableReservations,
} from './restaurant-tables.schema';
import {
  taxes,
  taxRates,
  groupSubTaxes,
  taxGroupItems,
  taxStatusEnum,
  taxPeriodStartMonthEnum,
  taxFilingFrequencyEnum,
  taxApplicableOnEnum,
} from './taxes.schema';
import {
  products,
  productVariants,
  productLocations,
  comboProducts,
  inventory,
  serialNumbers,
  batchNumbers,
  productGroupPrices,
  productTypeEnum,
  taxTypeEnum,
  productStatusEnum,
} from './products.schema';
import {
  packages,
  packageStatusEnum,
  packageTypeEnum,
} from './packages.schema';
import {
  serviceTimeSlots,
  serviceTimeSlotStaff,
  serviceTimeSlotServices,
  dayOfWeekEnum,
  scheduleTypeEnum,
} from './service-time-slots.schema';
import { productRacks } from './product-racks.schema';
import { variations } from './variations.schema';
import { variationLocationDetails } from './variation-location-details.schema';
import {
  customFields,
  customFieldTypeEnum,
  entityTypeEnum,
} from './custom-fields.schema';
import {
  paymentAccountTypes,
  paymentAccountTypeStatusEnum,
} from './payment-account-types.schema';
import {
  paymentAccounts,
  paymentAccountStatusEnum,
} from './payment-accounts.schema';
import {
  paymentMethods,
  paymentMethodStatusEnum,
} from './payment-methods.schema';
import { assets, assetStatusEnum } from './assets.schema';
import {
  assetTransactions,
  assetTransactionTypeEnum,
} from './asset-transactions.schema';
import { assetWarranties } from './asset-warranties.schema';
import {
  assetMaintenances,
  maintenanceStatusEnum,
  maintenancePriorityEnum,
} from './asset-maintenances.schema';
import {
  assetCategories,
  assetCategoryStatusEnum,
} from './asset-categories.schema';
import { assetTypes, assetTypeStatusEnum } from './asset-types.schema';
import {
  assetRepairOrders,
  repairStatusEnum,
  repairPriorityEnum,
  repairTypeEnum,
} from './asset-repair-orders.schema';
import {
  repairOrders,
  repairStatusHistory,
  repairPriorityEnum as repairOrderPriorityEnum,
  customerApprovalStatusEnum,
  repairWarrantyStatusEnum,
} from './repair-orders.schema';

import { suppliers, supplierStatusEnum } from './suppliers.schema';
import {
  projects,
  projectStatusEnum,
  projectAttachments,
} from './projects.schema';
import { notifications } from './notifications.schema';
import {
  vehicleTypes,
  vehicleTypeLocations,
  vehicleCategoryStatusEnum,
} from './vehicle-categories.schema';
import {
  reservationTypes,
  reservationTypeLocations,
  reservationTypeStatusEnum,
} from './reservation-types.schema';
import {
  accommodationUnits,
  bedTypeEnum,
  viewTypeEnum,
  roomStatusEnum,
  accommodationUnitStatusEnum,
} from './accommodation-units.schema';
import {
  vehicles,
  vehicleStatusEnum,
  depreciationDurationUnitEnum as vehicleDepreciationDurationUnitEnum,
} from './vehicles.schema';
import { vehicleBlockedPeriods } from './vehicle-blocked-periods.schema';
import { vehicleFines, vehicleFineTypeEnum } from './vehicle-fines.schema';
import { assetDamage } from './asset-damage.schema';
import {
  vehicleRepairOrders,
  repairOrderStatusEnum,
  maintenanceTypeEnum,
  workshopTypeEnum,
  RepairOrderStatus,
  MaintenanceType,
  WorkshopType,
} from './vehicle-repair-orders.schema';
import {
  expenses,
  expenseCategoryLineItems,
  expenseProductServiceLineItems,
  amountTypeEnum,
  expenseStatusEnum,
  payeeTypeEnum,
  AmountType,
  ExpenseStatus,
  PayeeType,
} from './expenses.schema';
import { media, mediaTypeEnum } from './media.schema';
import { mediaArray } from './media-array.schema';
import { comments, commentStatusEnum } from './comments.schema';

import {
  smsTemplates,
  whatsappTemplates,
  emailTemplates,
  templateStatusEnum,
  templateCategoryEnum,
  templateTypeEnum,
} from './templates.schema';
import {
  providers,
  providerStatusEnum,
  providerTypeEnum,
  providerNameEnum,
} from './providers.schema';
import {
  bankAccounts,
  bankAccountTypeEnum,
  currencyCodeEnum,
  bankAccountEntityTypeEnum,
} from './bank-accounts.schema';
import {
  accounts,
  accountCategoryEnum,
  accountDetailTypeEnum,
  accountStatusEnum,
  chartAccountTypeEnum,
} from './accounts.schema';
import {
  transactions,
  transactionTypeEnum as financialTransactionTypeEnum,
  transactionStatusEnum,
  paymentMethodEnum,
  transactionCurrencyCodeEnum,
  transactionDirectionEnum,
} from './transactions.schema';
import {
  transactionPayments,
  transactionPaymentMethodEnum,
  transactionPaymentReferenceTypeEnum,
} from './transaction-payments.schema';
import {
  journalEntries,
  journalEntryLines,
  JournalEntryStatus,
  JournalReferenceType,
  EntityType,
  journalEntryStatusEnum,
  journalReferenceTypeEnum,
  journalEntityTypeEnum,
} from './journal-entries.schema';

import {
  tasks,
  taskAttachments,
  taskStatusEnum,
  taskPriorityEnum,
  TaskStatus,
  TaskPriority,
} from './tasks.schema';
import {
  recurringActivities,
  recurringEntityTypeEnum,
  recurrencePatternEnum,
  recurrenceEndTypeEnum,
  recurringActivityStatusEnum,
  recurringDayOfWeekEnum,
  RecurringEntityType,
  RecurrencePattern,
  RecurrenceEndType,
  RecurringActivityStatus,
} from './recurring-activities.schema';
import {
  leads,
  leadLocations,
  leadTypeEnum,
  leadStatusEnum,
  leadSourceEnum,
  leadPriorityEnum,
} from './leads.schema';
import {
  newsletterSubscribers,
  newsletterSubscriberStatusEnum,
  subscriptionSourceEnum,
  NewsletterSubscriberStatus,
} from './newsletter-subscribers.schema';
import {
  estimates,
  estimateLineItems,
  estimateAttachments,
  estimateStatusEnum,
  estimateCurrencyCodeEnum,
  estimateTaxTypeEnum,
} from './estimates.schema';
import {
  discountPlans,
  discountPlanProducts,
  discountPlanServices,
  discountPlanProductCategories,
  discountPlanServiceCategories,
  discountPlanCustomerGroups,
  discountPlanCustomers,
  discountMethodEnum,
  discountTypeEnum,
  discountPlanStatusEnum,
  discountDayOfWeekEnum,
  timeTypeEnum,
  DiscountType,
} from './discount-plans.schema';
import {
  promoCodes,
  promoCodeStatusEnum,
  PromoCodeStatus,
} from './promo-codes.schema';
import { referrals, referralStatusEnum } from './referrals.schema';
import { gameEnum, games, gameStatusEnum } from './games.schema';
import { gameParticipants } from './game-participants.schema';
import {
  meetings,
  meetingStatusEnum,
  meetingTypeEnum,
} from './meetings.schema';
import {
  demoAppointments,
  appointmentStatusEnum,
} from './demo-appointments.schema';
import { businessRewardSettings } from './business-reward-settings.schema';
import { businessAccountingSettings } from './business-accounting-settings.schema';
import { campaigns, campaignsRelations } from './campaigns.schema';
import {
  workingHours,
  WeeklySchedule,
  WorkingHoursDay,
  WorkingHoursBreak,
  DEFAULT_WORKING_HOURS,
} from './working-hours.schema';
import { units, unitStatusEnum } from './units.schema';
import { unitConversions } from './unit-conversions.schema';
import { leaveTypes, leaveTypeStatusEnum } from './leave-types.schema';
import {
  billOfMaterials,
  bomLines,
  bomTypeEnum,
  bomStatusEnum,
  componentTypeEnum,
} from './bom.schema';
import { leaveRequests, leaveRequestStatusEnum } from './leave-requests.schema';
import { leaveBalances } from './leave-balances.schema';
import { staffLeaveTypes } from './staff-leave-types.schema';
import { allowanceTypes, employeeAllowances } from './allowance-types.schema';
import { deductionTypes, employeeDeductions } from './deduction-types.schema';
import {
  subscriptionPlans,
  subscriptionPlanProducts,
  subscriptionPlanServices,
  subscriptionPlanCustomers,
  subscriptionPlanCustomerGroups,
} from './subscription-plans.schema';
import { businessAmenities } from './amenities.schema';
import { businessUserLocations } from './business-user-locations.schema';

// Define relations
export const usersRelations = relations(users, ({ many }) => ({
  businesses: many(business),
  refreshTokens: many(refreshTokens),
  // New normalized user tables
  userAccounts: many(userAccounts),
  userTwoFactor: many(userTwoFactor),
  userRefreshTokens: many(userRefreshTokens),
  userPasswordResetTokens: many(userPasswordResetTokens),
  userEmailVerificationTokens: many(userEmailVerificationTokens),
  addresses: many(addresses, { relationName: 'userAddresses' }),
  brands: many(brands, { relationName: 'createdBrands' }),
  customerGroups: many(customerGroups, {
    relationName: 'createdCustomerGroups',
  }),
  createdCustomers: many(customers, { relationName: 'createdCustomers' }),
  updatedCustomers: many(customers, { relationName: 'updatedCustomers' }),
  deletedCustomers: many(customers, { relationName: 'deletedCustomers' }),
  warrantyTemplates: many(warrantyTemplates, {
    relationName: 'createdWarrantyTemplates',
  }),
  categories: many(categories, { relationName: 'createdCategories' }),
  rentalItemCategories: many(rentalItemCategories, {
    relationName: 'createdRentalItemCategories',
  }),
  equipmentTypes: many(equipmentTypes, {
    relationName: 'createdEquipmentTypes',
  }),
  services: many(services, { relationName: 'createdServices' }),
  serviceCategories: many(serviceCategories, {
    relationName: 'createdServiceCategories',
  }),
  // Service Order Priorities Relations
  createdServiceOrderPriorities: many(serviceOrderPriorities, {
    relationName: 'createdServiceOrderPriorities',
  }),
  updatedServiceOrderPriorities: many(serviceOrderPriorities, {
    relationName: 'updatedServiceOrderPriorities',
  }),
  deletedServiceOrderPriorities: many(serviceOrderPriorities, {
    relationName: 'deletedServiceOrderPriorities',
  }),
  // Service Order Statuses Relations
  createdServiceOrderStatuses: many(serviceOrderStatuses, {
    relationName: 'createdServiceOrderStatuses',
  }),
  updatedServiceOrderStatuses: many(serviceOrderStatuses, {
    relationName: 'updatedServiceOrderStatuses',
  }),
  deletedServiceOrderStatuses: many(serviceOrderStatuses, {
    relationName: 'deletedServiceOrderStatuses',
  }),
  // Service Order Types Relations
  createdServiceOrderTypes: many(serviceOrderTypes, {
    relationName: 'createdServiceOrderTypes',
  }),
  updatedServiceOrderTypes: many(serviceOrderTypes, {
    relationName: 'updatedServiceOrderTypes',
  }),
  deletedServiceOrderTypes: many(serviceOrderTypes, {
    relationName: 'deletedServiceOrderTypes',
  }),
  staffMember: many(staffMembers, { relationName: 'userStaffMember' }),
  userInvitations: many(userInvitations),
  userBusinessRoles: many(userBusinessRoles),
  businessUsers: many(businessUsers),
  userLocations: many(userLocations),
  taxes: many(taxes, { relationName: 'createdTaxes' }),
  taxRates: many(taxRates, { relationName: 'createdTaxRates' }),
  products: many(products, { relationName: 'createdProducts' }),
  customFields: many(customFields, { relationName: 'createdCustomFields' }),
  paymentAccountTypes: many(paymentAccountTypes, {
    relationName: 'createdPaymentAccountTypes',
  }),
  paymentAccounts: many(paymentAccounts, {
    relationName: 'createdPaymentAccounts',
  }),
  paymentMethods: many(paymentMethods, {
    relationName: 'createdPaymentMethods',
  }),
  assets: many(assets, { relationName: 'createdAssets' }),
  updatedAssets: many(assets, { relationName: 'updatedAssets' }),
  deletedAssets: many(assets, { relationName: 'deletedAssets' }),
  assetTransactions: many(assetTransactions, {
    relationName: 'createdAssetTransactions',
  }),
  assetMaintenances: many(assetMaintenances, {
    relationName: 'createdAssetMaintenances',
  }),
  updatedAssetMaintenances: many(assetMaintenances, {
    relationName: 'updatedAssetMaintenances',
  }),
  deletedAssetMaintenances: many(assetMaintenances, {
    relationName: 'deletedAssetMaintenances',
  }),
  // Asset Repair Orders Relations
  createdAssetRepairOrders: many(assetRepairOrders, {
    relationName: 'createdAssetRepairOrders',
  }),
  updatedAssetRepairOrders: many(assetRepairOrders, {
    relationName: 'updatedAssetRepairOrders',
  }),
  deletedAssetRepairOrders: many(assetRepairOrders, {
    relationName: 'deletedAssetRepairOrders',
  }),
  assetCategories: many(assetCategories, {
    relationName: 'createdAssetCategories',
  }),
  assetTypes: many(assetTypes, { relationName: 'createdAssetTypes' }),
  suppliers: many(suppliers, { relationName: 'createdSuppliers' }),
  updatedSuppliers: many(suppliers, { relationName: 'updatedSuppliers' }),
  deletedSuppliers: many(suppliers, { relationName: 'deletedSuppliers' }),
  projects: many(projects, { relationName: 'createdProjects' }),
  updatedProjects: many(projects, { relationName: 'updatedProjects' }),
  deletedProjects: many(projects, { relationName: 'deletedProjects' }),
  bankAccounts: many(bankAccounts, { relationName: 'createdBankAccounts' }),
  userBankAccounts: many(bankAccounts, { relationName: 'userBankAccounts' }),
  vehicleTypes: many(vehicleTypes, { relationName: 'createdVehicleTypes' }),
  reservationTypes: many(reservationTypes, {
    relationName: 'createdReservationTypes',
  }),
  accommodationUnits: many(accommodationUnits, {
    relationName: 'createdAccommodationUnits',
  }),
  vehicles: many(vehicles, { relationName: 'createdVehicles' }),
  createdAssetDamage: many(assetDamage, {
    relationName: 'createdAssetDamage',
  }),
  fixedAssetDamage: many(assetDamage, {
    relationName: 'fixedAssetDamage',
  }),
  createdVehicleBlockedPeriods: many(vehicleBlockedPeriods, {
    relationName: 'createdVehicleBlockedPeriods',
  }),
  createdVehicleFines: many(vehicleFines, {
    relationName: 'createdVehicleFines',
  }),

  // Vehicle Repair Order Relations
  createdRepairOrders: many(vehicleRepairOrders, {
    relationName: 'createdRepairOrders',
  }),

  // SMS Templates Relations
  createdSmsTemplates: many(smsTemplates, {
    relationName: 'createdSmsTemplates',
  }),
  updatedSmsTemplates: many(smsTemplates, {
    relationName: 'updatedSmsTemplates',
  }),
  deletedSmsTemplates: many(smsTemplates, {
    relationName: 'deletedSmsTemplates',
  }),
  // WhatsApp Templates Relations
  createdWhatsappTemplates: many(whatsappTemplates, {
    relationName: 'createdWhatsappTemplates',
  }),
  updatedWhatsappTemplates: many(whatsappTemplates, {
    relationName: 'updatedWhatsappTemplates',
  }),
  deletedWhatsappTemplates: many(whatsappTemplates, {
    relationName: 'deletedWhatsappTemplates',
  }),
  // Email Templates Relations
  createdEmailTemplates: many(emailTemplates, {
    relationName: 'createdEmailTemplates',
  }),
  updatedEmailTemplates: many(emailTemplates, {
    relationName: 'updatedEmailTemplates',
  }),
  deletedEmailTemplates: many(emailTemplates, {
    relationName: 'deletedEmailTemplates',
  }),
  // Providers Relations
  createdProviders: many(providers, {
    relationName: 'createdProviders',
  }),
  updatedProviders: many(providers, {
    relationName: 'updatedProviders',
  }),
  deletedProviders: many(providers, {
    relationName: 'deletedProviders',
  }),
  // Accounts Relations
  createdAccounts: many(accounts, {
    relationName: 'createdAccounts',
  }),
  updatedAccounts: many(accounts, {
    relationName: 'updatedAccounts',
  }),
  deletedAccounts: many(accounts, {
    relationName: 'deletedAccounts',
  }),
  // Tasks Relations
  createdTasks: many(tasks, {
    relationName: 'createdTasks',
  }),
  updatedTasks: many(tasks, {
    relationName: 'updatedTasks',
  }),
  deletedTasks: many(tasks, {
    relationName: 'deletedTasks',
  }),
  // Leads Relations
  createdLeads: many(leads, {
    relationName: 'createdLeads',
  }),
  updatedLeads: many(leads, {
    relationName: 'updatedLeads',
  }),
  deletedLeads: many(leads, {
    relationName: 'deletedLeads',
  }),
  // Newsletter Subscribers Relations
  createdNewsletterSubscribers: many(newsletterSubscribers, {
    relationName: 'createdNewsletterSubscribers',
  }),
  updatedNewsletterSubscribers: many(newsletterSubscribers, {
    relationName: 'updatedNewsletterSubscribers',
  }),
  deletedNewsletterSubscribers: many(newsletterSubscribers, {
    relationName: 'deletedNewsletterSubscribers',
  }),
  // Lead Locations Relations
  createdLeadLocations: many(leadLocations, {
    relationName: 'createdLeadLocations',
  }),
  updatedLeadLocations: many(leadLocations, {
    relationName: 'updatedLeadLocations',
  }),
  deletedLeadLocations: many(leadLocations, {
    relationName: 'deletedLeadLocations',
  }),
  // Customer Locations Relations
  createdCustomerLocations: many(customerLocations, {
    relationName: 'createdCustomerLocations',
  }),
  updatedCustomerLocations: many(customerLocations, {
    relationName: 'updatedCustomerLocations',
  }),
  deletedCustomerLocations: many(customerLocations, {
    relationName: 'deletedCustomerLocations',
  }),
  // Category Locations Relations
  createdCategoryLocations: many(categoryLocations, {
    relationName: 'createdCategoryLocations',
  }),
  updatedCategoryLocations: many(categoryLocations, {
    relationName: 'updatedCategoryLocations',
  }),
  deletedCategoryLocations: many(categoryLocations, {
    relationName: 'deletedCategoryLocations',
  }),
  // Rental Item Type Locations Relations
  createdRentalItemCategoryLocations: many(rentalItemCategoryLocations, {
    relationName: 'createdRentalItemCategoryLocations',
  }),
  updatedRentalItemCategoryLocations: many(rentalItemCategoryLocations, {
    relationName: 'updatedRentalItemCategoryLocations',
  }),
  deletedRentalItemCategoryLocations: many(rentalItemCategoryLocations, {
    relationName: 'deletedRentalItemCategoryLocations',
  }),
  // Vehicle Type Locations Relations
  createdVehicleTypeLocations: many(vehicleTypeLocations, {
    relationName: 'createdVehicleTypeLocations',
  }),
  updatedVehicleTypeLocations: many(vehicleTypeLocations, {
    relationName: 'updatedVehicleTypeLocations',
  }),
  deletedVehicleTypeLocations: many(vehicleTypeLocations, {
    relationName: 'deletedVehicleTypeLocations',
  }),
  // Room Type Locations Relations
  createdReservationTypeLocations: many(reservationTypeLocations, {
    relationName: 'createdReservationTypeLocations',
  }),
  updatedReservationTypeLocations: many(reservationTypeLocations, {
    relationName: 'updatedReservationTypeLocations',
  }),
  deletedReservationTypeLocations: many(reservationTypeLocations, {
    relationName: 'deletedReservationTypeLocations',
  }),
  // Service Type Locations Relations
  createdServiceCategoryLocations: many(serviceCategoryLocations, {
    relationName: 'createdServiceCategoryLocations',
  }),
  updatedServiceCategoryLocations: many(serviceCategoryLocations, {
    relationName: 'updatedServiceCategoryLocations',
  }),
  deletedServiceCategoryLocations: many(serviceCategoryLocations, {
    relationName: 'deletedServiceCategoryLocations',
  }),
  // Recurring Activities Relations
  createdRecurringActivities: many(recurringActivities, {
    relationName: 'createdRecurringActivities',
  }),
  updatedRecurringActivities: many(recurringActivities, {
    relationName: 'updatedRecurringActivities',
  }),
  deletedRecurringActivities: many(recurringActivities, {
    relationName: 'deletedRecurringActivities',
  }),
  // Transactions Relations
  createdTransactions: many(transactions, {
    relationName: 'createdTransactions',
  }),
  updatedTransactions: many(transactions, {
    relationName: 'updatedTransactions',
  }),
  deletedTransactions: many(transactions, {
    relationName: 'deletedTransactions',
  }),
  reconciledTransactions: many(transactions, {
    relationName: 'reconciledTransactions',
  }),
  reversedTransactions: many(transactions, {
    relationName: 'reversedTransactions',
  }),
  // Transaction Payments Relations
  createdTransactionPayments: many(transactionPayments, {
    relationName: 'createdTransactionPayments',
  }),
  updatedTransactionPayments: many(transactionPayments, {
    relationName: 'updatedTransactionPayments',
  }),
  deletedTransactionPayments: many(transactionPayments, {
    relationName: 'deletedTransactionPayments',
  }),
  // Journal Entries Relations
  createdJournalEntries: many(journalEntries, {
    relationName: 'createdJournalEntries',
  }),
  updatedJournalEntries: many(journalEntries, {
    relationName: 'updatedJournalEntries',
  }),
  deletedJournalEntries: many(journalEntries, {
    relationName: 'deletedJournalEntries',
  }),
  // Journal Entry Lines Relations
  createdJournalEntryLines: many(journalEntryLines, {
    relationName: 'createdJournalEntryLines',
  }),
  updatedJournalEntryLines: many(journalEntryLines, {
    relationName: 'updatedJournalEntryLines',
  }),
  deletedJournalEntryLines: many(journalEntryLines, {
    relationName: 'deletedJournalEntryLines',
  }),
  // Estimates Relations
  createdEstimates: many(estimates, {
    relationName: 'createdEstimates',
  }),
  updatedEstimates: many(estimates, {
    relationName: 'updatedEstimates',
  }),
  deletedEstimates: many(estimates, {
    relationName: 'deletedEstimates',
  }),
  // Estimate Line Items Relations
  createdEstimateLineItems: many(estimateLineItems, {
    relationName: 'createdEstimateLineItems',
  }),
  updatedEstimateLineItems: many(estimateLineItems, {
    relationName: 'updatedEstimateLineItems',
  }),
  deletedEstimateLineItems: many(estimateLineItems, {
    relationName: 'deletedEstimateLineItems',
  }),
  // Estimate Attachments Relations
  createdEstimateAttachments: many(estimateAttachments, {
    relationName: 'createdEstimateAttachments',
  }),
  updatedEstimateAttachments: many(estimateAttachments, {
    relationName: 'updatedEstimateAttachments',
  }),
  deletedEstimateAttachments: many(estimateAttachments, {
    relationName: 'deletedEstimateAttachments',
  }),
  // Variation Templates Relations
  createdVariationTemplates: many(variationTemplates, {
    relationName: 'createdVariationTemplates',
  }),
  updatedVariationTemplates: many(variationTemplates, {
    relationName: 'updatedVariationTemplates',
  }),
  deletedVariationTemplates: many(variationTemplates, {
    relationName: 'deletedVariationTemplates',
  }),
  // Variation Value Templates Relations
  createdVariationValueTemplates: many(variationValueTemplates, {
    relationName: 'createdVariationValueTemplates',
  }),
  updatedVariationValueTemplates: many(variationValueTemplates, {
    relationName: 'updatedVariationValueTemplates',
  }),
  deletedVariationValueTemplates: many(variationValueTemplates, {
    relationName: 'deletedVariationValueTemplates',
  }),
  // Modifier Groups Relations
  createdModifierGroups: many(modifierGroups, {
    relationName: 'createdModifierGroups',
  }),
  updatedModifierGroups: many(modifierGroups, {
    relationName: 'updatedModifierGroups',
  }),
  deletedModifierGroups: many(modifierGroups, {
    relationName: 'deletedModifierGroups',
  }),
  // Modifiers Relations
  createdModifiers: many(modifiers, {
    relationName: 'createdModifiers',
  }),
  updatedModifiers: many(modifiers, {
    relationName: 'updatedModifiers',
  }),
  deletedModifiers: many(modifiers, {
    relationName: 'deletedModifiers',
  }),
  // Discount Plans Relations
  createdDiscountPlans: many(discountPlans, {
    relationName: 'createdDiscountPlans',
  }),
  updatedDiscountPlans: many(discountPlans, {
    relationName: 'updatedDiscountPlans',
  }),
  deletedDiscountPlans: many(discountPlans, {
    relationName: 'deletedDiscountPlans',
  }),
  // Promo Codes Relations
  createdPromoCodes: many(promoCodes, {
    relationName: 'createdPromoCodes',
  }),
  updatedPromoCodes: many(promoCodes, {
    relationName: 'updatedPromoCodes',
  }),
  deletedPromoCodes: many(promoCodes, {
    relationName: 'deletedPromoCodes',
  }),
  // Referrals Relations
  createdReferrals: many(referrals, {
    relationName: 'createdReferrals',
  }),
  updatedReferrals: many(referrals, {
    relationName: 'updatedReferrals',
  }),
  deletedReferrals: many(referrals, {
    relationName: 'deletedReferrals',
  }),
  // Games Relations
  createdGames: many(games, {
    relationName: 'createdGames',
  }),
  updatedGames: many(games, {
    relationName: 'updatedGames',
  }),
  deletedGames: many(games, {
    relationName: 'deletedGames',
  }),
  // Game Participants Relations
  createdGameParticipants: many(gameParticipants, {
    relationName: 'createdGameParticipants',
  }),
  updatedGameParticipants: many(gameParticipants, {
    relationName: 'updatedGameParticipants',
  }),
  deletedGameParticipants: many(gameParticipants, {
    relationName: 'deletedGameParticipants',
  }),

  // Expenses Relations
  createdExpenses: many(expenses, {
    relationName: 'createdExpenses',
  }),
  updatedExpenses: many(expenses, {
    relationName: 'updatedExpenses',
  }),
  deletedExpenses: many(expenses, {
    relationName: 'deletedExpenses',
  }),
  // Expense Category Line Items Relations
  createdExpenseCategoryLineItems: many(expenseCategoryLineItems, {
    relationName: 'createdExpenseCategoryLineItems',
  }),
  updatedExpenseCategoryLineItems: many(expenseCategoryLineItems, {
    relationName: 'updatedExpenseCategoryLineItems',
  }),
  deletedExpenseCategoryLineItems: many(expenseCategoryLineItems, {
    relationName: 'deletedExpenseCategoryLineItems',
  }),
  // Expense Product Service Line Items Relations
  createdExpenseProductServiceLineItems: many(expenseProductServiceLineItems, {
    relationName: 'createdExpenseProductServiceLineItems',
  }),
  updatedExpenseProductServiceLineItems: many(expenseProductServiceLineItems, {
    relationName: 'updatedExpenseProductServiceLineItems',
  }),
  deletedExpenseProductServiceLineItems: many(expenseProductServiceLineItems, {
    relationName: 'deletedExpenseProductServiceLineItems',
  }),

  // Meetings Relations
  assignedMeetings: many(meetings, {
    relationName: 'assignedMeetings',
  }),
  createdMeetings: many(meetings, {
    relationName: 'createdMeetings',
  }),
  updatedMeetings: many(meetings, {
    relationName: 'updatedMeetings',
  }),
  deletedMeetings: many(meetings, {
    relationName: 'deletedMeetings',
  }),

  // Demo Appointments Relations
  createdDemoAppointments: many(demoAppointments, {
    relationName: 'createdDemoAppointments',
  }),
  updatedDemoAppointments: many(demoAppointments, {
    relationName: 'updatedDemoAppointments',
  }),
  deletedDemoAppointments: many(demoAppointments, {
    relationName: 'deletedDemoAppointments',
  }),
  assignedDemoAppointments: many(demoAppointments, {
    relationName: 'assignedDemoAppointments',
  }),
  // Campaigns Relations
  createdCampaigns: many(campaigns, {
    relationName: 'createdCampaigns',
  }),
  updatedCampaigns: many(campaigns, {
    relationName: 'updatedCampaigns',
  }),
  deletedCampaigns: many(campaigns, {
    relationName: 'deletedCampaigns',
  }),
  // Units Relations
  createdUnits: many(units, {
    relationName: 'createdUnits',
  }),
  updatedUnits: many(units, {
    relationName: 'updatedUnits',
  }),
  deletedUnits: many(units, {
    relationName: 'deletedUnits',
  }),
  // Unit Conversions Relations
  createdUnitConversions: many(unitConversions, {
    relationName: 'createdUnitConversions',
  }),
  updatedUnitConversions: many(unitConversions, {
    relationName: 'updatedUnitConversions',
  }),
  deletedUnitConversions: many(unitConversions, {
    relationName: 'deletedUnitConversions',
  }),
  // Restaurant Menu Items Relations
  createdRestaurantMenuItems: many(restaurantMenuItems, {
    relationName: 'createdRestaurantMenuItems',
  }),
  updatedRestaurantMenuItems: many(restaurantMenuItems, {
    relationName: 'updatedRestaurantMenuItems',
  }),
  deletedRestaurantMenuItems: many(restaurantMenuItems, {
    relationName: 'deletedRestaurantMenuItems',
  }),
  // Restaurant Tables Relations
  createdFloorPlans: many(floorPlans, {
    relationName: 'createdFloorPlans',
  }),
  updatedFloorPlans: many(floorPlans, {
    relationName: 'updatedFloorPlans',
  }),
  deletedFloorPlans: many(floorPlans, {
    relationName: 'deletedFloorPlans',
  }),
  createdRestaurantTables: many(restaurantTables, {
    relationName: 'createdRestaurantTables',
  }),
  updatedRestaurantTables: many(restaurantTables, {
    relationName: 'updatedRestaurantTables',
  }),
  deletedRestaurantTables: many(restaurantTables, {
    relationName: 'deletedRestaurantTables',
  }),
  createdTableReservations: many(tableReservations, {
    relationName: 'createdTableReservations',
  }),
  updatedTableReservations: many(tableReservations, {
    relationName: 'updatedTableReservations',
  }),
  deletedTableReservations: many(tableReservations, {
    relationName: 'deletedTableReservations',
  }),
  // Business Amenities Relations
  createdBusinessAmenities: many(businessAmenities, {
    relationName: 'createdBusinessAmenities',
  }),
  updatedBusinessAmenities: many(businessAmenities, {
    relationName: 'updatedBusinessAmenities',
  }),
  deletedBusinessAmenities: many(businessAmenities, {
    relationName: 'deletedBusinessAmenities',
  }),

  // Bill of Materials Relations
  createdBillOfMaterials: many(billOfMaterials, {
    relationName: 'createdBillOfMaterials',
  }),
  approvedBillOfMaterials: many(billOfMaterials, {
    relationName: 'approvedBillOfMaterials',
  }),
  updatedBillOfMaterials: many(billOfMaterials, {
    relationName: 'updatedBillOfMaterials',
  }),
  deletedBillOfMaterials: many(billOfMaterials, {
    relationName: 'deletedBillOfMaterials',
  }),
}));

// User Accounts Relations
export const userAccountsRelations = relations(userAccounts, ({ one }) => ({
  user: one(users, {
    fields: [userAccounts.userId],
    references: [users.id],
  }),
}));

// User Two Factor Relations
export const userTwoFactorRelations = relations(userTwoFactor, ({ one }) => ({
  user: one(users, {
    fields: [userTwoFactor.userId],
    references: [users.id],
  }),
}));

// User Refresh Tokens Relations
export const userRefreshTokensRelations = relations(
  userRefreshTokens,
  ({ one }) => ({
    user: one(users, {
      fields: [userRefreshTokens.userId],
      references: [users.id],
    }),
  }),
);

// User Password Reset Tokens Relations
export const userPasswordResetTokensRelations = relations(
  userPasswordResetTokens,
  ({ one }) => ({
    user: one(users, {
      fields: [userPasswordResetTokens.userId],
      references: [users.id],
    }),
  }),
);

// User Email Verification Tokens Relations
export const userEmailVerificationTokensRelations = relations(
  userEmailVerificationTokens,
  ({ one }) => ({
    user: one(users, {
      fields: [userEmailVerificationTokens.userId],
      references: [users.id],
    }),
  }),
);

export const businessRelations = relations(business, ({ one, many }) => ({
  owner: one(users, {
    fields: [business.ownerId],
    references: [users.id],
  }),
  addresses: many(addresses, { relationName: 'businessAddresses' }),
  brands: many(brands),
  customerGroups: many(customerGroups),
  customers: many(customers),
  warrantyTemplates: many(warrantyTemplates),
  categories: many(categories),
  rentalItemCategories: many(rentalItemCategories),
  equipmentTypes: many(equipmentTypes),
  services: many(services),
  serviceCategories: many(serviceCategories),
  serviceOrderPriorities: many(serviceOrderPriorities),
  serviceOrderStatuses: many(serviceOrderStatuses),
  serviceOrderTypes: many(serviceOrderTypes),
  departments: many(departments),
  designations: many(designations),
  staffMembers: many(staffMembers),
  staffFamilyDetails: many(staffFamilyDetails),
  staffEmergencyContacts: many(staffEmergencyContacts),
  staffPhysicalInfo: many(staffPhysicalInfo),

  staffInvitations: many(staffInvitations),
  userInvitations: many(userInvitations),
  businessRoles: many(businessRoles),
  userBusinessRoles: many(userBusinessRoles),
  businessUsers: many(businessUsers),
  locations: many(locations),
  variationTemplates: many(variationTemplates),
  variationValueTemplates: many(variationValueTemplates),
  modifierGroups: many(modifierGroups),
  modifiers: many(modifiers),
  restaurantMenuItems: many(restaurantMenuItems),
  floorPlans: many(floorPlans),
  restaurantTables: many(restaurantTables),
  tableReservations: many(tableReservations),
  taxes: many(taxes),
  taxGroupItems: many(taxGroupItems),
  taxRates: many(taxRates),
  products: many(products),
  customFields: many(customFields),
  paymentAccountTypes: many(paymentAccountTypes),
  paymentAccounts: many(paymentAccounts),
  paymentMethods: many(paymentMethods),
  assets: many(assets),
  assetTransactions: many(assetTransactions),
  assetMaintenances: many(assetMaintenances),
  assetRepairOrders: many(assetRepairOrders),
  assetCategories: many(assetCategories),
  assetTypes: many(assetTypes),
  suppliers: many(suppliers),
  projects: many(projects),
  bankAccounts: many(bankAccounts),
  accounts: many(accounts),
  notifications: many(notifications),
  vehicleTypes: many(vehicleTypes),
  reservationTypes: many(reservationTypes),
  accommodationUnits: many(accommodationUnits),
  vehicles: many(vehicles),
  assetDamages: many(assetDamage),
  vehicleBlockedPeriods: many(vehicleBlockedPeriods),
  vehicleFines: many(vehicleFines),
  expenses: many(expenses),
  vehicleRepairOrders: many(vehicleRepairOrders),
  smsTemplates: many(smsTemplates),
  whatsappTemplates: many(whatsappTemplates),
  emailTemplates: many(emailTemplates),
  providers: many(providers),
  tasks: many(tasks),
  recurringActivities: many(recurringActivities),
  leads: many(leads),
  newsletterSubscribers: many(newsletterSubscribers),
  transactions: many(transactions),
  transactionPayments: many(transactionPayments),
  journalEntries: many(journalEntries),
  journalEntryLines: many(journalEntryLines),
  estimates: many(estimates),
  discountPlans: many(discountPlans),
  promoCodes: many(promoCodes),
  referrals: many(referrals),
  games: many(games),
  businessModules: many(businessModules),
  meetings: many(meetings),
  demoAppointments: many(demoAppointments),
  campaigns: many(campaigns),
  units: many(units),
  unitConversions: many(unitConversions),
  rewardSettings: one(businessRewardSettings, {
    fields: [business.id],
    references: [businessRewardSettings.businessId],
  }),
  workingHours: one(workingHours, {
    fields: [business.id],
    references: [workingHours.businessId],
  }),
  businessAmenities: one(businessAmenities, {
    fields: [business.id],
    references: [businessAmenities.businessId],
  }),
  billOfMaterials: many(billOfMaterials),
  bomLines: many(bomLines),
}));

export const brandsRelations = relations(brands, ({ one }) => ({
  business: one(business, {
    fields: [brands.businessId],
    references: [business.id],
  }),

  creator: one(users, {
    fields: [brands.createdBy],
    references: [users.id],
    relationName: 'createdBrands',
  }),
}));

export const customerGroupsRelations = relations(
  customerGroups,
  ({ one, many }) => ({
    business: one(business, {
      fields: [customerGroups.businessId],
      references: [business.id],
    }),
    creator: one(users, {
      fields: [customerGroups.createdBy],
      references: [users.id],
      relationName: 'createdCustomerGroups',
    }),
    customers: many(customers),
    discountPlanCustomerGroups: many(discountPlanCustomerGroups),
  }),
);

export const customersRelations = relations(customers, ({ one, many }) => ({
  business: one(business, {
    fields: [customers.businessId],
    references: [business.id],
  }),
  creator: one(users, {
    fields: [customers.createdBy],
    references: [users.id],
    relationName: 'createdCustomers',
  }),
  updater: one(users, {
    fields: [customers.updatedBy],
    references: [users.id],
    relationName: 'updatedCustomers',
  }),
  deleter: one(users, {
    fields: [customers.deletedBy],
    references: [users.id],
    relationName: 'deletedCustomers',
  }),
  customerGroup: one(customerGroups, {
    fields: [customers.customerGroupId],
    references: [customerGroups.id],
  }),
  billingAddress: one(addresses, {
    fields: [customers.billingAddressId],
    references: [addresses.id],
    relationName: 'customerBillingAddress',
  }),
  shippingAddress: one(addresses, {
    fields: [customers.shippingAddressId],
    references: [addresses.id],
    relationName: 'customerShippingAddress',
  }),
  customerLocations: many(customerLocations),
  projects: many(projects),
  transactions: many(transactions),
  tasks: many(tasks, { relationName: 'customerTasks' }),
  estimates: many(estimates),
  discountPlanCustomers: many(discountPlanCustomers),
  // Expense relations as payee
  expensesAsPayee: many(expenses, { relationName: 'customerExpenses' }),
  // Customer rewards relations
  customerReward: one(customerRewards, {
    fields: [customers.id],
    references: [customerRewards.customerId],
  }),
  pointTransactions: many(pointTransactions),
  tableReservations: many(tableReservations),
  warranties: many(warranties),
  transferredWarranties: many(warranties, {
    relationName: 'transferredWarranties',
  }),
}));

export const customerLocationsRelations = relations(
  customerLocations,
  ({ one }) => ({
    customer: one(customers, {
      fields: [customerLocations.customerId],
      references: [customers.id],
    }),
    location: one(locations, {
      fields: [customerLocations.locationId],
      references: [locations.id],
    }),
    creator: one(users, {
      fields: [customerLocations.createdBy],
      references: [users.id],
      relationName: 'createdCustomerLocations',
    }),
    updater: one(users, {
      fields: [customerLocations.updatedBy],
      references: [users.id],
      relationName: 'updatedCustomerLocations',
    }),
    deleter: one(users, {
      fields: [customerLocations.deletedBy],
      references: [users.id],
      relationName: 'deletedCustomerLocations',
    }),
  }),
);

export const customerRewardsRelations = relations(
  customerRewards,
  ({ one }) => ({
    customer: one(customers, {
      fields: [customerRewards.customerId],
      references: [customers.id],
    }),
    creator: one(users, {
      fields: [customerRewards.createdBy],
      references: [users.id],
      relationName: 'createdCustomerRewards',
    }),
    updater: one(users, {
      fields: [customerRewards.updatedBy],
      references: [users.id],
      relationName: 'updatedCustomerRewards',
    }),
    deleter: one(users, {
      fields: [customerRewards.deletedBy],
      references: [users.id],
      relationName: 'deletedCustomerRewards',
    }),
  }),
);

export const pointTransactionsRelations = relations(
  pointTransactions,
  ({ one }) => ({
    customer: one(customers, {
      fields: [pointTransactions.customerId],
      references: [customers.id],
    }),
    creator: one(users, {
      fields: [pointTransactions.createdBy],
      references: [users.id],
      relationName: 'createdPointTransactions',
    }),
    updater: one(users, {
      fields: [pointTransactions.updatedBy],
      references: [users.id],
      relationName: 'updatedPointTransactions',
    }),
    deleter: one(users, {
      fields: [pointTransactions.deletedBy],
      references: [users.id],
      relationName: 'deletedPointTransactions',
    }),
  }),
);

export const warrantyTemplatesRelations = relations(
  warrantyTemplates,
  ({ one, many }) => ({
    business: one(business, {
      fields: [warrantyTemplates.businessId],
      references: [business.id],
    }),
    creator: one(users, {
      fields: [warrantyTemplates.createdBy],
      references: [users.id],
      relationName: 'createdWarrantyTemplates',
    }),
    services: many(services),
    warranties: many(warranties),
  }),
);

export const categoriesRelations = relations(categories, ({ one, many }) => ({
  business: one(business, {
    fields: [categories.businessId],
    references: [business.id],
  }),
  creator: one(users, {
    fields: [categories.createdBy],
    references: [users.id],
    relationName: 'createdCategories',
  }),
  parent: one(categories, {
    fields: [categories.parentId],
    references: [categories.id],
  }),
  children: many(categories, { relationName: 'children' }),
  image: one(media, {
    fields: [categories.image],
    references: [media.id],
    relationName: 'categoryImage',
  }),
  ogImage: one(media, {
    fields: [categories.ogImage],
    references: [media.id],
    relationName: 'categoryOgImage',
  }),
  categoryLocations: many(categoryLocations),
  discountPlanProductCategories: many(discountPlanProductCategories),
  restaurantMenuItems: many(restaurantMenuItems),
}));

export const rentalItemCategoriesRelations = relations(
  rentalItemCategories,
  ({ one, many }) => ({
    business: one(business, {
      fields: [rentalItemCategories.businessId],
      references: [business.id],
    }),
    creator: one(users, {
      fields: [rentalItemCategories.createdBy],
      references: [users.id],
      relationName: 'createdRentalItemCategories',
    }),
    parent: one(rentalItemCategories, {
      fields: [rentalItemCategories.parentId],
      references: [rentalItemCategories.id],
    }),
    children: many(rentalItemCategories, { relationName: 'children' }),
    image: one(media, {
      fields: [rentalItemCategories.image],
      references: [media.id],
      relationName: 'rentalItemCategoryImage',
    }),
    ogImage: one(media, {
      fields: [rentalItemCategories.ogImage],
      references: [media.id],
      relationName: 'rentalItemCategoryOgImage',
    }),
    rentalItemCategoryLocations: many(rentalItemCategoryLocations),
    rentalItemsAsType: many(rentalItems, {
      relationName: 'rentalItemCategory',
    }),
    rentalItemsAsSubType: many(rentalItems, {
      relationName: 'rentalItemSubCategory',
    }),
  }),
);

export const rentalItemsRelations = relations(rentalItems, ({ one }) => ({
  business: one(business, {
    fields: [rentalItems.businessId],
    references: [business.id],
  }),
  creator: one(users, {
    fields: [rentalItems.createdBy],
    references: [users.id],
    relationName: 'createdRentalItems',
  }),
  updater: one(users, {
    fields: [rentalItems.updatedBy],
    references: [users.id],
    relationName: 'updatedRentalItems',
  }),
  deleter: one(users, {
    fields: [rentalItems.deletedBy],
    references: [users.id],
    relationName: 'deletedRentalItems',
  }),
  type: one(rentalItemCategories, {
    fields: [rentalItems.categoryId],
    references: [rentalItemCategories.id],
    relationName: 'rentalItemCategory',
  }),
  subType: one(rentalItemCategories, {
    fields: [rentalItems.subCategoryId],
    references: [rentalItemCategories.id],
    relationName: 'rentalItemSubCategory',
  }),
  assetType: one(assetTypes, {
    fields: [rentalItems.assetCategoryId],
    references: [assetTypes.id],
    relationName: 'rentalItemAssetType',
  }),
  assetSubType: one(assetTypes, {
    fields: [rentalItems.assetSubCategoryId],
    references: [assetTypes.id],
    relationName: 'rentalItemAssetSubType',
  }),
  fixedAssetAccount: one(accounts, {
    fields: [rentalItems.fixedAssetAccountId],
    references: [accounts.id],
    relationName: 'rentalItemFixedAssetAccount',
  }),
  depreciationAccount: one(accounts, {
    fields: [rentalItems.depreciationAccountId],
    references: [accounts.id],
    relationName: 'rentalItemDepreciationAccount',
  }),
  expenseAccount: one(accounts, {
    fields: [rentalItems.expenseAccountId],
    references: [accounts.id],
    relationName: 'rentalItemExpenseAccount',
  }),
  ogImage: one(media, {
    fields: [rentalItems.ogImage],
    references: [media.id],
    relationName: 'rentalItemOgImage',
  }),
}));

export const equipmentTypesRelations = relations(
  equipmentTypes,
  ({ one, many }) => ({
    business: one(business, {
      fields: [equipmentTypes.businessId],
      references: [business.id],
    }),
    creator: one(users, {
      fields: [equipmentTypes.createdBy],
      references: [users.id],
      relationName: 'createdEquipmentTypes',
    }),
    parent: one(equipmentTypes, {
      fields: [equipmentTypes.parentId],
      references: [equipmentTypes.id],
    }),
    children: many(equipmentTypes, { relationName: 'children' }),
    image: one(media, {
      fields: [equipmentTypes.image],
      references: [media.id],
      relationName: 'equipmentTypeImage',
    }),
    ogImage: one(media, {
      fields: [equipmentTypes.ogImage],
      references: [media.id],
      relationName: 'equipmentTypeOgImage',
    }),
    equipmentTypeLocations: many(equipmentTypeLocations),
  }),
);

export const equipmentTypeLocationsRelations = relations(
  equipmentTypeLocations,
  ({ one }) => ({
    equipmentType: one(equipmentTypes, {
      fields: [equipmentTypeLocations.equipmentTypeId],
      references: [equipmentTypes.id],
    }),
    location: one(locations, {
      fields: [equipmentTypeLocations.locationId],
      references: [locations.id],
    }),
  }),
);

export const servicesRelations = relations(services, ({ one, many }) => ({
  business: one(business, {
    fields: [services.businessId],
    references: [business.id],
  }),
  creator: one(users, {
    fields: [services.createdBy],
    references: [users.id],
    relationName: 'createdServices',
  }),
  updater: one(users, {
    fields: [services.updatedBy],
    references: [users.id],
    relationName: 'updatedServices',
  }),
  deleter: one(users, {
    fields: [services.deletedBy],
    references: [users.id],
    relationName: 'deletedServices',
  }),
  serviceCategory: one(serviceCategories, {
    fields: [services.serviceCategoryId],
    references: [serviceCategories.id],
  }),
  incomeAccount: one(accounts, {
    fields: [services.incomeAccountId],
    references: [accounts.id],
    relationName: 'serviceIncomeAccount',
  }),
  expenseAccount: one(accounts, {
    fields: [services.expenseAccountId],
    references: [accounts.id],
    relationName: 'serviceExpenseAccount',
  }),
  salesTax: one(taxes, {
    fields: [services.salesTaxId],
    references: [taxes.id],
    relationName: 'serviceSalesTax',
  }),
  preferredSupplier: one(suppliers, {
    fields: [services.preferredSupplierId],
    references: [suppliers.id],
    relationName: 'servicePreferredSupplier',
  }),
  serviceStaff: many(serviceStaff),
  serviceLocations: many(serviceLocations),
  discountPlanServices: many(discountPlanServices),
}));

export const serviceCategoriesRelations = relations(
  serviceCategories,
  ({ one, many }) => ({
    business: one(business, {
      fields: [serviceCategories.businessId],
      references: [business.id],
    }),
    creator: one(users, {
      fields: [serviceCategories.createdBy],
      references: [users.id],
      relationName: 'createdServiceCategories',
    }),
    parent: one(serviceCategories, {
      fields: [serviceCategories.parentId],
      references: [serviceCategories.id],
    }),
    children: many(serviceCategories, { relationName: 'children' }),
    image: one(media, {
      fields: [serviceCategories.image],
      references: [media.id],
      relationName: 'serviceCategoryImage',
    }),
    ogImage: one(media, {
      fields: [serviceCategories.ogImage],
      references: [media.id],
      relationName: 'serviceCategoryOgImage',
    }),
    serviceCategoryLocations: many(serviceCategoryLocations),
    services: many(services),
    discountPlanServiceCategories: many(discountPlanServiceCategories),
  }),
);

export const serviceCategoryLocationsRelations = relations(
  serviceCategoryLocations,
  ({ one }) => ({
    serviceCategory: one(serviceCategories, {
      fields: [serviceCategoryLocations.serviceCategoryId],
      references: [serviceCategories.id],
    }),
    location: one(locations, {
      fields: [serviceCategoryLocations.locationId],
      references: [locations.id],
    }),
    creator: one(users, {
      fields: [serviceCategoryLocations.createdBy],
      references: [users.id],
      relationName: 'createdServiceCategoryLocations',
    }),
    updater: one(users, {
      fields: [serviceCategoryLocations.updatedBy],
      references: [users.id],
      relationName: 'updatedServiceCategoryLocations',
    }),
    deleter: one(users, {
      fields: [serviceCategoryLocations.deletedBy],
      references: [users.id],
      relationName: 'deletedServiceCategoryLocations',
    }),
  }),
);

export const serviceOrderPrioritiesRelations = relations(
  serviceOrderPriorities,
  ({ one }) => ({
    business: one(business, {
      fields: [serviceOrderPriorities.businessId],
      references: [business.id],
    }),
    creator: one(users, {
      fields: [serviceOrderPriorities.createdBy],
      references: [users.id],
      relationName: 'createdServiceOrderPriorities',
    }),
    updater: one(users, {
      fields: [serviceOrderPriorities.updatedBy],
      references: [users.id],
      relationName: 'updatedServiceOrderPriorities',
    }),
    deleter: one(users, {
      fields: [serviceOrderPriorities.deletedBy],
      references: [users.id],
      relationName: 'deletedServiceOrderPriorities',
    }),
  }),
);

export const serviceOrderStatusesRelations = relations(
  serviceOrderStatuses,
  ({ one }) => ({
    business: one(business, {
      fields: [serviceOrderStatuses.businessId],
      references: [business.id],
    }),
    creator: one(users, {
      fields: [serviceOrderStatuses.createdBy],
      references: [users.id],
      relationName: 'createdServiceOrderStatuses',
    }),
    updater: one(users, {
      fields: [serviceOrderStatuses.updatedBy],
      references: [users.id],
      relationName: 'updatedServiceOrderStatuses',
    }),
    deleter: one(users, {
      fields: [serviceOrderStatuses.deletedBy],
      references: [users.id],
      relationName: 'deletedServiceOrderStatuses',
    }),
  }),
);

export const serviceOrderTypesRelations = relations(
  serviceOrderTypes,
  ({ one }) => ({
    business: one(business, {
      fields: [serviceOrderTypes.businessId],
      references: [business.id],
    }),
    creator: one(users, {
      fields: [serviceOrderTypes.createdBy],
      references: [users.id],
      relationName: 'createdServiceOrderTypes',
    }),
    updater: one(users, {
      fields: [serviceOrderTypes.updatedBy],
      references: [users.id],
      relationName: 'updatedServiceOrderTypes',
    }),
    deleter: one(users, {
      fields: [serviceOrderTypes.deletedBy],
      references: [users.id],
      relationName: 'deletedServiceOrderTypes',
    }),
  }),
);

export const serviceStaffRelations = relations(serviceStaff, ({ one }) => ({
  service: one(services, {
    fields: [serviceStaff.serviceId],
    references: [services.id],
  }),
  staffMember: one(staffMembers, {
    fields: [serviceStaff.staffMemberId],
    references: [staffMembers.id],
  }),
  creator: one(users, {
    fields: [serviceStaff.createdBy],
    references: [users.id],
    relationName: 'createdServiceStaff',
  }),
  updater: one(users, {
    fields: [serviceStaff.updatedBy],
    references: [users.id],
    relationName: 'updatedServiceStaff',
  }),
  deleter: one(users, {
    fields: [serviceStaff.deletedBy],
    references: [users.id],
    relationName: 'deletedServiceStaff',
  }),
}));

export const serviceLocationsRelations = relations(
  serviceLocations,
  ({ one }) => ({
    service: one(services, {
      fields: [serviceLocations.serviceId],
      references: [services.id],
    }),
    location: one(locations, {
      fields: [serviceLocations.locationId],
      references: [locations.id],
    }),
    creator: one(users, {
      fields: [serviceLocations.createdBy],
      references: [users.id],
      relationName: 'createdServiceLocations',
    }),
    updater: one(users, {
      fields: [serviceLocations.updatedBy],
      references: [users.id],
      relationName: 'updatedServiceLocations',
    }),
    deleter: one(users, {
      fields: [serviceLocations.deletedBy],
      references: [users.id],
      relationName: 'deletedServiceLocations',
    }),
  }),
);

export const addressRelations = relations(addresses, ({ one, many }) => ({
  business: one(business, {
    fields: [addresses.businessId],
    references: [business.id],
    relationName: 'businessAddresses',
  }),
  user: one(users, {
    fields: [addresses.userId],
    references: [users.id],
    relationName: 'userAddresses',
  }),
  locations: many(locations),
  staffMembers: many(staffMembers),
  emergencyContacts: many(staffEmergencyContacts),
  suppliers: many(suppliers),
  projectsBillingAddress: many(projects, {
    relationName: 'projectBillingAddress',
  }),
  projectsShippingAddress: many(projects, {
    relationName: 'projectShippingAddress',
  }),
  customersBillingAddress: many(customers, {
    relationName: 'customerBillingAddress',
  }),
  customersShippingAddress: many(customers, {
    relationName: 'customerShippingAddress',
  }),
}));

export const projectsRelations = relations(projects, ({ one, many }) => ({
  business: one(business, {
    fields: [projects.businessId],
    references: [business.id],
  }),
  creator: one(users, {
    fields: [projects.createdBy],
    references: [users.id],
    relationName: 'createdProjects',
  }),
  updater: one(users, {
    fields: [projects.updatedBy],
    references: [users.id],
    relationName: 'updatedProjects',
  }),
  deleter: one(users, {
    fields: [projects.deletedBy],
    references: [users.id],
    relationName: 'deletedProjects',
  }),
  customer: one(customers, {
    fields: [projects.customerId],
    references: [customers.id],
  }),
  billingAddress: one(addresses, {
    fields: [projects.billingAddressId],
    references: [addresses.id],
    relationName: 'projectBillingAddress',
  }),
  shippingAddress: one(addresses, {
    fields: [projects.shippingAddressId],
    references: [addresses.id],
    relationName: 'projectShippingAddress',
  }),
  attachments: many(projectAttachments),
  tasks: many(tasks, { relationName: 'projectTasks' }),
}));

export const projectAttachmentsRelations = relations(
  projectAttachments,
  ({ one }) => ({
    project: one(projects, {
      fields: [projectAttachments.projectId],
      references: [projects.id],
    }),
    attachment: one(media, {
      fields: [projectAttachments.attachmentId],
      references: [media.id],
    }),
  }),
);

export const mediaRelations = relations(media, ({ one, many }) => ({
  business: one(business, {
    fields: [media.businessId],
    references: [business.id],
  }),
  uploadedBy: one(users, {
    fields: [media.uploadedBy],
    references: [users.id],
  }),
  // Relations for entities using this media
  staffMembersProfileImage: many(staffMembers, {
    relationName: 'profileImageStaffMembers',
  }),

  suppliersProfileImage: many(suppliers, {
    relationName: 'supplierProfileImage',
  }),
  categoriesImage: many(categories, {
    relationName: 'categoryImage',
  }),
  categoriesOgImage: many(categories, {
    relationName: 'categoryOgImage',
  }),
  rentalItemCategoriesImage: many(rentalItemCategories, {
    relationName: 'rentalItemCategoryImage',
  }),
  rentalItemCategoriesOgImage: many(rentalItemCategories, {
    relationName: 'rentalItemCategoryOgImage',
  }),
  rentalItemsOgImage: many(rentalItems, {
    relationName: 'rentalItemOgImage',
  }),
  equipmentTypesImage: many(equipmentTypes, {
    relationName: 'equipmentTypeImage',
  }),
  equipmentTypesOgImage: many(equipmentTypes, {
    relationName: 'equipmentTypeOgImage',
  }),
  vehicleTypesImage: many(vehicleTypes, {
    relationName: 'vehicleTypeImage',
  }),
  vehicleTypesOgImage: many(vehicleTypes, {
    relationName: 'vehicleTypeOgImage',
  }),
  reservationTypesImage: many(reservationTypes, {
    relationName: 'reservationTypeImage',
  }),
  reservationTypesOgImage: many(reservationTypes, {
    relationName: 'reservationTypeOgImage',
  }),
  accommodationUnitsOgImage: many(accommodationUnits, {
    relationName: 'accommodationUnitOgImage',
  }),
  serviceCategoriesImage: many(serviceCategories, {
    relationName: 'serviceCategoryImage',
  }),
  serviceCategoriesOgImage: many(serviceCategories, {
    relationName: 'serviceCategoryOgImage',
  }),
  vehicleFinesScannedFile: many(vehicleFines),
  vehiclesImage: many(vehicles, { relationName: 'vehicleImage' }),
  vehiclesOgImage: many(vehicles, { relationName: 'vehicleOgImage' }),
  whatsappTemplatesMedia: many(whatsappTemplates),
  projectAttachments: many(projectAttachments),
  taskAttachments: many(taskAttachments),
  leadsProfileImage: many(leads),
  estimateAttachments: many(estimateAttachments),
  assetsImage: many(assets, { relationName: 'assetImage' }),
  // Restaurant Menu Items media relations
  restaurantMenuItemsVideo: many(restaurantMenuItems, {
    relationName: 'restaurantMenuItemVideo',
  }),
  restaurantMenuItemsOgImage: many(restaurantMenuItems, {
    relationName: 'restaurantMenuItemOgImage',
  }),
  mediaArrays: many(mediaArray),
  // Comments media relations
  commentAttachments: many(comments, {
    relationName: 'commentAttachment',
  }),
}));

export const mediaArrayRelations = relations(mediaArray, ({ one }) => ({
  media: one(media, {
    fields: [mediaArray.mediaId],
    references: [media.id],
  }),
}));

export const commentsRelations = relations(comments, ({ one, many }) => ({
  business: one(business, {
    fields: [comments.businessId],
    references: [business.id],
  }),
  author: one(staffMembers, {
    fields: [comments.authorId],
    references: [staffMembers.id],
    relationName: 'authorComments',
  }),
  attachment: one(media, {
    fields: [comments.attachmentId],
    references: [media.id],
    relationName: 'commentAttachment',
  }),
  parent: one(comments, {
    fields: [comments.parentId],
    references: [comments.id],
    relationName: 'parentComment',
  }),
  replies: many(comments, {
    relationName: 'parentComment',
  }),
  creator: one(users, {
    fields: [comments.createdBy],
    references: [users.id],
    relationName: 'createdComments',
  }),
  updater: one(users, {
    fields: [comments.updatedBy],
    references: [users.id],
    relationName: 'updatedComments',
  }),
  deleter: one(users, {
    fields: [comments.deletedBy],
    references: [users.id],
    relationName: 'deletedComments',
  }),
}));

export const refreshTokensRelations = relations(refreshTokens, ({ one }) => ({
  user: one(users, {
    fields: [refreshTokens.userId],
    references: [users.id],
  }),
}));

export const activityLogRelations = relations(activityLog, () => ({
  // Relations can be added here if needed in the future
}));

export const departmentsRelations = relations(departments, ({ one, many }) => ({
  business: one(business, {
    fields: [departments.businessId],
    references: [business.id],
  }),
  parent: one(departments, {
    fields: [departments.parentId],
    references: [departments.id],
  }),
  children: many(departments, { relationName: 'children' }),
  staffMembers: many(staffMembers),
}));

export const designationsRelations = relations(
  designations,
  ({ one, many }) => ({
    business: one(business, {
      fields: [designations.businessId],
      references: [business.id],
    }),
    parent: one(designations, {
      fields: [designations.parentId],
      references: [designations.id],
    }),
    children: many(designations, { relationName: 'children' }),
    staffMembers: many(staffMembers),
  }),
);

export const staffMembersRelations = relations(
  staffMembers,
  ({ one, many }) => ({
    business: one(business, {
      fields: [staffMembers.businessId],
      references: [business.id],
    }),
    user: one(users, {
      fields: [staffMembers.userId],
      references: [users.id],
      relationName: 'userStaffMember',
    }),
    department: one(departments, {
      fields: [staffMembers.departmentId],
      references: [departments.id],
    }),
    designation: one(designations, {
      fields: [staffMembers.designationId],
      references: [designations.id],
    }),
    // Address and Media Relations
    address: one(addresses, {
      fields: [staffMembers.addressId],
      references: [addresses.id],
    }),
    profileImage: one(media, {
      fields: [staffMembers.profileImageId],
      references: [media.id],
      relationName: 'profileImageStaffMembers',
    }),
    invitations: many(staffInvitations),
    userInvitations: many(userInvitations),
    // Family Details Relations
    familyDetails: one(staffFamilyDetails),
    emergencyContacts: many(staffEmergencyContacts),
    physicalInfo: one(staffPhysicalInfo),

    // Vehicle Repair Order Relations
    mechanicRepairOrders: many(vehicleRepairOrders, {
      relationName: 'mechanicRepairOrders',
    }),
    qualityCheckRepairOrders: many(vehicleRepairOrders, {
      relationName: 'qualityCheckRepairOrders',
    }),
    // Tasks Relations
    assignedTasks: many(tasks, {
      relationName: 'assignedTasks',
    }),
    // Leads Relations
    ownedLeads: many(leads),
    // Service Relations
    serviceStaff: many(serviceStaff),
    // Asset Transaction Relations
    receivedAssetTransactions: many(assetTransactions, {
      relationName: 'receivedAssetTransactions',
    }),
    // Asset Maintenance Relations
    assignedAssetMaintenances: many(assetMaintenances, {
      relationName: 'assignedAssetMaintenances',
    }),
    qualityCheckAssetMaintenances: many(assetMaintenances, {
      relationName: 'qualityCheckAssetMaintenances',
    }),
    // Asset Repair Order Relations
    reportedAssetRepairOrders: many(assetRepairOrders, {
      relationName: 'reportedAssetRepairOrders',
    }),
    assignedAssetRepairOrders: many(assetRepairOrders, {
      relationName: 'assignedAssetRepairOrders',
    }),
    qualityCheckAssetRepairOrders: many(assetRepairOrders, {
      relationName: 'qualityCheckAssetRepairOrders',
    }),
    // Referrals Relations
    referrals: many(referrals),
    // Asset Damage Relations
    fixedAssetDamages: many(assetDamage, {
      relationName: 'fixedAssetDamage',
    }),
    checkedAssetDamages: many(assetDamage, {
      relationName: 'checkedAssetDamage',
    }),
    // Performance Review Relations
    performanceReviews: many(performanceReviews, {
      relationName: 'staffPerformanceReviews',
    }),
    reviewedPerformanceReviews: many(performanceReviews, {
      relationName: 'reviewerPerformanceReviews',
    }),
    // Leave Request Relations
    leaveRequests: many(leaveRequests, {
      relationName: 'employeeLeaveRequests',
    }),
    approvedLeaveRequests: many(leaveRequests, {
      relationName: 'approvedLeaveRequests',
    }),
    // Leave Balance Relations
    leaveBalances: many(leaveBalances, {
      relationName: 'employeeLeaveBalances',
    }),
    // Staff Leave Types Relations
    staffLeaveTypes: many(staffLeaveTypes, {
      relationName: 'staffLeaveTypeAssignments',
    }),
    // Employee Allowances Relations
    employeeAllowances: many(employeeAllowances, {
      relationName: 'staffEmployeeAllowances',
    }),
    // Employee Deductions Relations
    employeeDeductions: many(employeeDeductions, {
      relationName: 'staffEmployeeDeductions',
    }),
    // Time Slots Relations
    timeSlots: many(timeSlots),
    // Comments Relations
    authoredComments: many(comments, {
      relationName: 'authorComments',
    }),
  }),
);

export const timeSlotsRelations = relations(timeSlots, ({ one }) => ({
  staffMember: one(staffMembers, {
    fields: [timeSlots.staffMemberId],
    references: [staffMembers.id],
  }),
}));

export const restaurantTimeSlotsRelations = relations(
  restaurantTimeSlots,
  ({ one }) => ({
    business: one(business, {
      fields: [restaurantTimeSlots.businessId],
      references: [business.id],
    }),
    creator: one(users, {
      fields: [restaurantTimeSlots.createdBy],
      references: [users.id],
      relationName: 'createdRestaurantTimeSlots',
    }),
    updater: one(users, {
      fields: [restaurantTimeSlots.updatedBy],
      references: [users.id],
      relationName: 'updatedRestaurantTimeSlots',
    }),
    deleter: one(users, {
      fields: [restaurantTimeSlots.deletedBy],
      references: [users.id],
      relationName: 'deletedRestaurantTimeSlots',
    }),
  }),
);

export const staffInvitationsRelations = relations(
  staffInvitations,
  ({ one }) => ({
    business: one(business, {
      fields: [staffInvitations.businessId],
      references: [business.id],
    }),
    staffMember: one(staffMembers, {
      fields: [staffInvitations.staffMemberId],
      references: [staffMembers.id],
    }),
  }),
);

export const userInvitationsRelations = relations(
  userInvitations,
  ({ one }) => ({
    user: one(users, {
      fields: [userInvitations.userId],
      references: [users.id],
    }),
    business: one(business, {
      fields: [userInvitations.businessId],
      references: [business.id],
    }),
    staffMember: one(staffMembers, {
      fields: [userInvitations.staffMemberId],
      references: [staffMembers.id],
    }),
  }),
);

export const performanceReviewsRelations = relations(
  performanceReviews,
  ({ one }) => ({
    business: one(business, {
      fields: [performanceReviews.businessId],
      references: [business.id],
    }),
    staff: one(staffMembers, {
      fields: [performanceReviews.staffId],
      references: [staffMembers.id],
      relationName: 'staffPerformanceReviews',
    }),
    reviewer: one(staffMembers, {
      fields: [performanceReviews.reviewerId],
      references: [staffMembers.id],
      relationName: 'reviewerPerformanceReviews',
    }),
    creator: one(users, {
      fields: [performanceReviews.createdBy],
      references: [users.id],
      relationName: 'createdPerformanceReviews',
    }),
    updater: one(users, {
      fields: [performanceReviews.updatedBy],
      references: [users.id],
      relationName: 'updatedPerformanceReviews',
    }),
    deleter: one(users, {
      fields: [performanceReviews.deletedBy],
      references: [users.id],
      relationName: 'deletedPerformanceReviews',
    }),
  }),
);

export const staffFamilyDetailsRelations = relations(
  staffFamilyDetails,
  ({ one }) => ({
    business: one(business, {
      fields: [staffFamilyDetails.businessId],
      references: [business.id],
    }),
    staffMember: one(staffMembers, {
      fields: [staffFamilyDetails.staffMemberId],
      references: [staffMembers.id],
    }),
    creator: one(users, {
      fields: [staffFamilyDetails.createdBy],
      references: [users.id],
    }),
    updater: one(users, {
      fields: [staffFamilyDetails.updatedBy],
      references: [users.id],
    }),
    deleter: one(users, {
      fields: [staffFamilyDetails.deletedBy],
      references: [users.id],
    }),
  }),
);

export const staffEmergencyContactsRelations = relations(
  staffEmergencyContacts,
  ({ one }) => ({
    business: one(business, {
      fields: [staffEmergencyContacts.businessId],
      references: [business.id],
    }),
    staffMember: one(staffMembers, {
      fields: [staffEmergencyContacts.staffMemberId],
      references: [staffMembers.id],
    }),
    address: one(addresses, {
      fields: [staffEmergencyContacts.addressId],
      references: [addresses.id],
    }),
    creator: one(users, {
      fields: [staffEmergencyContacts.createdBy],
      references: [users.id],
    }),
    updater: one(users, {
      fields: [staffEmergencyContacts.updatedBy],
      references: [users.id],
    }),
    deleter: one(users, {
      fields: [staffEmergencyContacts.deletedBy],
      references: [users.id],
    }),
  }),
);

export const staffPhysicalInfoRelations = relations(
  staffPhysicalInfo,
  ({ one }) => ({
    business: one(business, {
      fields: [staffPhysicalInfo.businessId],
      references: [business.id],
    }),
    staffMember: one(staffMembers, {
      fields: [staffPhysicalInfo.staffMemberId],
      references: [staffMembers.id],
    }),
    creator: one(users, {
      fields: [staffPhysicalInfo.createdBy],
      references: [users.id],
    }),
    updater: one(users, {
      fields: [staffPhysicalInfo.updatedBy],
      references: [users.id],
    }),
    deleter: one(users, {
      fields: [staffPhysicalInfo.deletedBy],
      references: [users.id],
    }),
  }),
);

export const tasksRelations = relations(tasks, ({ one, many }) => ({
  business: one(business, {
    fields: [tasks.businessId],
    references: [business.id],
  }),
  assignedStaffMember: one(staffMembers, {
    fields: [tasks.assignedTo],
    references: [staffMembers.id],
    relationName: 'assignedTasks',
  }),

  recurringActivity: one(recurringActivities, {
    fields: [tasks.recurringActivityId],
    references: [recurringActivities.id],
    relationName: 'taskRecurringActivity',
  }),
  parentTask: one(tasks, {
    fields: [tasks.parentTaskId],
    references: [tasks.id],
    relationName: 'parentTask',
  }),
  childTasks: many(tasks, { relationName: 'parentTask' }),
  creator: one(users, {
    fields: [tasks.createdBy],
    references: [users.id],
    relationName: 'createdTasks',
  }),
  updater: one(users, {
    fields: [tasks.updatedBy],
    references: [users.id],
    relationName: 'updatedTasks',
  }),
  deleter: one(users, {
    fields: [tasks.deletedBy],
    references: [users.id],
    relationName: 'deletedTasks',
  }),
  attachments: many(taskAttachments),
}));

export const taskAttachmentsRelations = relations(
  taskAttachments,
  ({ one }) => ({
    task: one(tasks, {
      fields: [taskAttachments.taskId],
      references: [tasks.id],
    }),
    attachment: one(media, {
      fields: [taskAttachments.attachmentId],
      references: [media.id],
    }),
  }),
);

export const recurringActivitiesRelations = relations(
  recurringActivities,
  ({ one }) => ({
    business: one(business, {
      fields: [recurringActivities.businessId],
      references: [business.id],
    }),
    creator: one(users, {
      fields: [recurringActivities.createdBy],
      references: [users.id],
      relationName: 'createdRecurringActivities',
    }),
    updater: one(users, {
      fields: [recurringActivities.updatedBy],
      references: [users.id],
      relationName: 'updatedRecurringActivities',
    }),
    deleter: one(users, {
      fields: [recurringActivities.deletedBy],
      references: [users.id],
      relationName: 'deletedRecurringActivities',
    }),
  }),
);

export const leadsRelations = relations(leads, ({ one, many }) => ({
  business: one(business, {
    fields: [leads.businessId],
    references: [business.id],
  }),
  leadOwner: one(staffMembers, {
    fields: [leads.leadOwnerId],
    references: [staffMembers.id],
  }),
  address: one(addresses, {
    fields: [leads.addressId],
    references: [addresses.id],
  }),
  profileImage: one(media, {
    fields: [leads.profileImageId],
    references: [media.id],
  }),
  leadLocations: many(leadLocations),
  creator: one(users, {
    fields: [leads.createdBy],
    references: [users.id],
    relationName: 'createdLeads',
  }),
  updater: one(users, {
    fields: [leads.updatedBy],
    references: [users.id],
    relationName: 'updatedLeads',
  }),
  deleter: one(users, {
    fields: [leads.deletedBy],
    references: [users.id],
    relationName: 'deletedLeads',
  }),
}));

export const leadLocationsRelations = relations(leadLocations, ({ one }) => ({
  lead: one(leads, {
    fields: [leadLocations.leadId],
    references: [leads.id],
  }),
  location: one(locations, {
    fields: [leadLocations.locationId],
    references: [locations.id],
  }),
  creator: one(users, {
    fields: [leadLocations.createdBy],
    references: [users.id],
    relationName: 'createdLeadLocations',
  }),
  updater: one(users, {
    fields: [leadLocations.updatedBy],
    references: [users.id],
    relationName: 'updatedLeadLocations',
  }),
  deleter: one(users, {
    fields: [leadLocations.deletedBy],
    references: [users.id],
    relationName: 'deletedLeadLocations',
  }),
}));

export const newsletterSubscribersRelations = relations(
  newsletterSubscribers,
  ({ one }) => ({
    business: one(business, {
      fields: [newsletterSubscribers.businessId],
      references: [business.id],
    }),
    creator: one(users, {
      fields: [newsletterSubscribers.createdBy],
      references: [users.id],
      relationName: 'createdNewsletterSubscribers',
    }),
    updater: one(users, {
      fields: [newsletterSubscribers.updatedBy],
      references: [users.id],
      relationName: 'updatedNewsletterSubscribers',
    }),
    deleter: one(users, {
      fields: [newsletterSubscribers.deletedBy],
      references: [users.id],
      relationName: 'deletedNewsletterSubscribers',
    }),
  }),
);

export const categoryLocationsRelations = relations(
  categoryLocations,
  ({ one }) => ({
    category: one(categories, {
      fields: [categoryLocations.categoryId],
      references: [categories.id],
    }),
    location: one(locations, {
      fields: [categoryLocations.locationId],
      references: [locations.id],
    }),
    creator: one(users, {
      fields: [categoryLocations.createdBy],
      references: [users.id],
      relationName: 'createdCategoryLocations',
    }),
    updater: one(users, {
      fields: [categoryLocations.updatedBy],
      references: [users.id],
      relationName: 'updatedCategoryLocations',
    }),
    deleter: one(users, {
      fields: [categoryLocations.deletedBy],
      references: [users.id],
      relationName: 'deletedCategoryLocations',
    }),
  }),
);

export const rentalItemCategoryLocationsRelations = relations(
  rentalItemCategoryLocations,
  ({ one }) => ({
    rentalItemCategory: one(rentalItemCategories, {
      fields: [rentalItemCategoryLocations.rentalItemCategoryId],
      references: [rentalItemCategories.id],
    }),
    location: one(locations, {
      fields: [rentalItemCategoryLocations.locationId],
      references: [locations.id],
    }),
    creator: one(users, {
      fields: [rentalItemCategoryLocations.createdBy],
      references: [users.id],
      relationName: 'createdRentalItemCategoryLocations',
    }),
    updater: one(users, {
      fields: [rentalItemCategoryLocations.updatedBy],
      references: [users.id],
      relationName: 'updatedRentalItemCategoryLocations',
    }),
    deleter: one(users, {
      fields: [rentalItemCategoryLocations.deletedBy],
      references: [users.id],
      relationName: 'deletedRentalItemCategoryLocations',
    }),
  }),
);

export const businessRolesRelations = relations(
  businessRoles,
  ({ one, many }) => ({
    business: one(business, {
      fields: [businessRoles.businessId],
      references: [business.id],
    }),
    creator: one(users, {
      fields: [businessRoles.createdBy],
      references: [users.id],
    }),
    updater: one(users, {
      fields: [businessRoles.updatedBy],
      references: [users.id],
    }),
    deleter: one(users, {
      fields: [businessRoles.deletedBy],
      references: [users.id],
    }),
    userBusinessRoles: many(userBusinessRoles),
    businessUsers: many(businessUsers),
  }),
);

export const userBusinessRolesRelations = relations(
  userBusinessRoles,
  ({ one }) => ({
    user: one(users, {
      fields: [userBusinessRoles.userId],
      references: [users.id],
    }),
    business: one(business, {
      fields: [userBusinessRoles.businessId],
      references: [business.id],
    }),
    role: one(businessRoles, {
      fields: [userBusinessRoles.roleId],
      references: [businessRoles.id],
    }),
    assignedBy: one(users, {
      fields: [userBusinessRoles.assignedBy],
      references: [users.id],
    }),
  }),
);

export const businessUsersRelations = relations(
  businessUsers,
  ({ one, many }) => ({
    user: one(users, {
      fields: [businessUsers.userId],
      references: [users.id],
    }),
    business: one(business, {
      fields: [businessUsers.businessId],
      references: [business.id],
    }),
    businessRole: one(businessRoles, {
      fields: [businessUsers.businessRoleId],
      references: [businessRoles.id],
    }),
    invitedBy: one(users, {
      fields: [businessUsers.invitedBy],
      references: [users.id],
    }),
    createdBy: one(users, {
      fields: [businessUsers.createdBy],
      references: [users.id],
    }),
    updatedBy: one(users, {
      fields: [businessUsers.updatedBy],
      references: [users.id],
    }),
    deletedBy: one(users, {
      fields: [businessUsers.deletedBy],
      references: [users.id],
    }),
    businessUserLocations: many(businessUserLocations),
  }),
);

export const businessUserLocationsRelations = relations(
  businessUserLocations,
  ({ one }) => ({
    businessUser: one(businessUsers, {
      fields: [businessUserLocations.businessUserId],
      references: [businessUsers.id],
    }),
    location: one(locations, {
      fields: [businessUserLocations.locationId],
      references: [locations.id],
    }),
    creator: one(users, {
      fields: [businessUserLocations.createdBy],
      references: [users.id],
      relationName: 'createdBusinessUserLocations',
    }),
    updater: one(users, {
      fields: [businessUserLocations.updatedBy],
      references: [users.id],
      relationName: 'updatedBusinessUserLocations',
    }),
    deleter: one(users, {
      fields: [businessUserLocations.deletedBy],
      references: [users.id],
      relationName: 'deletedBusinessUserLocations',
    }),
  }),
);

export const locationsRelations = relations(locations, ({ one, many }) => ({
  business: one(business, {
    fields: [locations.businessId],
    references: [business.id],
  }),
  address: one(addresses, {
    fields: [locations.addressId],
    references: [addresses.id],
  }),
  creator: one(users, {
    fields: [locations.createdBy],
    references: [users.id],
  }),
  userLocations: many(userLocations),
  productRacks: many(productRacks),
  variationLocationDetails: many(variationLocationDetails),
  products: many(products, {
    relationName: 'productLocations',
  }),
  assets: many(assets),
  vehicles: many(vehicles, { relationName: 'vehicleLocation' }),
  categoryLocations: many(categoryLocations),
  rentalItemCategoryLocations: many(rentalItemCategoryLocations),
  leadLocations: many(leadLocations),
  customerLocations: many(customerLocations),
  vehicleTypeLocations: many(vehicleTypeLocations),
  reservationTypeLocations: many(reservationTypeLocations),
  serviceCategoryLocations: many(serviceCategoryLocations),
  serviceLocations: many(serviceLocations),
  // Asset Transaction Relations
  assetTransactionsFromLocation: many(assetTransactions, {
    relationName: 'assetTransactionsFromLocation',
  }),
  assetTransactionsToLocation: many(assetTransactions, {
    relationName: 'assetTransactionsToLocation',
  }),
}));

export const userLocationsRelations = relations(userLocations, ({ one }) => ({
  user: one(users, {
    fields: [userLocations.userId],
    references: [users.id],
  }),
  location: one(locations, {
    fields: [userLocations.locationId],
    references: [locations.id],
  }),
  business: one(business, {
    fields: [userLocations.businessId],
    references: [business.id],
  }),
}));

export const variationTemplatesRelations = relations(
  variationTemplates,
  ({ one, many }) => ({
    business: one(business, {
      fields: [variationTemplates.businessId],
      references: [business.id],
    }),
    createdBy: one(users, {
      fields: [variationTemplates.createdBy],
      references: [users.id],
      relationName: 'createdVariationTemplates',
    }),
    updatedBy: one(users, {
      fields: [variationTemplates.updatedBy],
      references: [users.id],
      relationName: 'updatedVariationTemplates',
    }),
    deletedBy: one(users, {
      fields: [variationTemplates.deletedBy],
      references: [users.id],
      relationName: 'deletedVariationTemplates',
    }),
    variationValueTemplates: many(variationValueTemplates),
  }),
);

export const modifierGroupsRelations = relations(
  modifierGroups,
  ({ one, many }) => ({
    business: one(business, {
      fields: [modifierGroups.businessId],
      references: [business.id],
    }),
    createdBy: one(users, {
      fields: [modifierGroups.createdBy],
      references: [users.id],
      relationName: 'createdModifierGroups',
    }),
    updatedBy: one(users, {
      fields: [modifierGroups.updatedBy],
      references: [users.id],
      relationName: 'updatedModifierGroups',
    }),
    deletedBy: one(users, {
      fields: [modifierGroups.deletedBy],
      references: [users.id],
      relationName: 'deletedModifierGroups',
    }),
    modifiers: many(modifiers),
  }),
);

export const modifiersRelations = relations(modifiers, ({ one }) => ({
  business: one(business, {
    fields: [modifiers.businessId],
    references: [business.id],
  }),
  modifierGroup: one(modifierGroups, {
    fields: [modifiers.modifierGroupId],
    references: [modifierGroups.id],
  }),
  createdBy: one(users, {
    fields: [modifiers.createdBy],
    references: [users.id],
    relationName: 'createdModifiers',
  }),
  updatedBy: one(users, {
    fields: [modifiers.updatedBy],
    references: [users.id],
    relationName: 'updatedModifiers',
  }),
  deletedBy: one(users, {
    fields: [modifiers.deletedBy],
    references: [users.id],
    relationName: 'deletedModifiers',
  }),
}));

export const variationValueTemplatesRelations = relations(
  variationValueTemplates,
  ({ one }) => ({
    business: one(business, {
      fields: [variationValueTemplates.businessId],
      references: [business.id],
    }),
    variationTemplate: one(variationTemplates, {
      fields: [variationValueTemplates.variationTemplateId],
      references: [variationTemplates.id],
    }),
    createdBy: one(users, {
      fields: [variationValueTemplates.createdBy],
      references: [users.id],
      relationName: 'createdVariationValueTemplates',
    }),
    updatedBy: one(users, {
      fields: [variationValueTemplates.updatedBy],
      references: [users.id],
      relationName: 'updatedVariationValueTemplates',
    }),
    deletedBy: one(users, {
      fields: [variationValueTemplates.deletedBy],
      references: [users.id],
      relationName: 'deletedVariationValueTemplates',
    }),
  }),
);

export const restaurantMenuItemsRelations = relations(
  restaurantMenuItems,
  ({ one }) => ({
    business: one(business, {
      fields: [restaurantMenuItems.businessId],
      references: [business.id],
    }),
    creator: one(users, {
      fields: [restaurantMenuItems.createdBy],
      references: [users.id],
      relationName: 'createdRestaurantMenuItems',
    }),
    updater: one(users, {
      fields: [restaurantMenuItems.updatedBy],
      references: [users.id],
      relationName: 'updatedRestaurantMenuItems',
    }),
    deleter: one(users, {
      fields: [restaurantMenuItems.deletedBy],
      references: [users.id],
      relationName: 'deletedRestaurantMenuItems',
    }),
    category: one(categories, {
      fields: [restaurantMenuItems.categoryId],
      references: [categories.id],
    }),
    incomeAccount: one(accounts, {
      fields: [restaurantMenuItems.incomeAccountId],
      references: [accounts.id],
      relationName: 'restaurantMenuItemIncomeAccount',
    }),
    expenseAccount: one(accounts, {
      fields: [restaurantMenuItems.expenseAccountId],
      references: [accounts.id],
      relationName: 'restaurantMenuItemExpenseAccount',
    }),
    salesTax: one(taxes, {
      fields: [restaurantMenuItems.salesTaxId],
      references: [taxes.id],
      relationName: 'restaurantMenuItemSalesTax',
    }),
    ogImageMedia: one(media, {
      fields: [restaurantMenuItems.ogImage],
      references: [media.id],
      relationName: 'restaurantMenuItemOgImage',
    }),
  }),
);

export const floorPlansRelations = relations(floorPlans, ({ one, many }) => ({
  business: one(business, {
    fields: [floorPlans.businessId],
    references: [business.id],
  }),
  creator: one(users, {
    fields: [floorPlans.createdBy],
    references: [users.id],
    relationName: 'createdFloorPlans',
  }),
  updater: one(users, {
    fields: [floorPlans.updatedBy],
    references: [users.id],
    relationName: 'updatedFloorPlans',
  }),
  deleter: one(users, {
    fields: [floorPlans.deletedBy],
    references: [users.id],
    relationName: 'deletedFloorPlans',
  }),
  restaurantTables: many(restaurantTables),
}));

export const restaurantTablesRelations = relations(
  restaurantTables,
  ({ one, many }) => ({
    business: one(business, {
      fields: [restaurantTables.businessId],
      references: [business.id],
    }),
    floorPlan: one(floorPlans, {
      fields: [restaurantTables.floorId],
      references: [floorPlans.id],
    }),
    creator: one(users, {
      fields: [restaurantTables.createdBy],
      references: [users.id],
      relationName: 'createdRestaurantTables',
    }),
    updater: one(users, {
      fields: [restaurantTables.updatedBy],
      references: [users.id],
      relationName: 'updatedRestaurantTables',
    }),
    deleter: one(users, {
      fields: [restaurantTables.deletedBy],
      references: [users.id],
      relationName: 'deletedRestaurantTables',
    }),
    tableReservations: many(tableReservations),
  }),
);

export const tableReservationsRelations = relations(
  tableReservations,
  ({ one }) => ({
    customer: one(customers, {
      fields: [tableReservations.customerId],
      references: [customers.id],
    }),
    table: one(restaurantTables, {
      fields: [tableReservations.tableId],
      references: [restaurantTables.id],
    }),
    creator: one(users, {
      fields: [tableReservations.createdBy],
      references: [users.id],
      relationName: 'createdTableReservations',
    }),
    updater: one(users, {
      fields: [tableReservations.updatedBy],
      references: [users.id],
      relationName: 'updatedTableReservations',
    }),
    deleter: one(users, {
      fields: [tableReservations.deletedBy],
      references: [users.id],
      relationName: 'deletedTableReservations',
    }),
  }),
);

export const taxRatesRelations = relations(taxRates, ({ one, many }) => ({
  business: one(business, {
    fields: [taxRates.businessId],
    references: [business.id],
  }),
  creator: one(users, {
    fields: [taxRates.createdBy],
    references: [users.id],
    relationName: 'createdTaxRates',
  }),
  groupSubTaxesAsGroup: many(groupSubTaxes, { relationName: 'groupTax' }),
  groupSubTaxesAsTax: many(groupSubTaxes, { relationName: 'subTax' }),
}));

export const groupSubTaxesRelations = relations(groupSubTaxes, ({ one }) => ({
  groupTax: one(taxRates, {
    fields: [groupSubTaxes.groupTaxId],
    references: [taxRates.id],
    relationName: 'groupTax',
  }),
  subTax: one(taxRates, {
    fields: [groupSubTaxes.taxId],
    references: [taxRates.id],
    relationName: 'subTax',
  }),
}));

export const taxesRelations = relations(taxes, ({ one, many }) => ({
  business: one(business, {
    fields: [taxes.businessId],
    references: [business.id],
  }),
  creator: one(users, {
    fields: [taxes.createdBy],
    references: [users.id],
    relationName: 'createdTaxes',
  }),
  updater: one(users, {
    fields: [taxes.updatedBy],
    references: [users.id],
    relationName: 'updatedTaxes',
  }),
  deleter: one(users, {
    fields: [taxes.deletedBy],
    references: [users.id],
    relationName: 'deletedTaxes',
  }),
  // Relations for group taxes
  groupItems: many(taxGroupItems, { relationName: 'groupTax' }),
  // Relations for individual taxes in groups
  individualTaxItems: many(taxGroupItems, { relationName: 'individualTax' }),
  // Relations for accounts using this tax as default
  accountsWithDefaultTax: many(accounts),
  // Relations for services using this tax
  servicesWithSalesTax: many(services, { relationName: 'serviceSalesTax' }),
  // Relations for asset maintenances using this tax
  assetMaintenances: many(assetMaintenances, {
    relationName: 'assetMaintenanceTax',
  }),
  // Relations for asset repair orders using this tax
  assetRepairOrders: many(assetRepairOrders, {
    relationName: 'assetRepairOrderTax',
  }),
  // Relations for restaurant menu items using this tax
  restaurantMenuItemsWithSalesTax: many(restaurantMenuItems, {
    relationName: 'restaurantMenuItemSalesTax',
  }),
}));

export const taxGroupItemsRelations = relations(taxGroupItems, ({ one }) => ({
  groupTax: one(taxes, {
    fields: [taxGroupItems.groupTaxId],
    references: [taxes.id],
    relationName: 'groupTax',
  }),
  individualTax: one(taxes, {
    fields: [taxGroupItems.individualTaxId],
    references: [taxes.id],
    relationName: 'individualTax',
  }),
}));

export const productsRelations = relations(products, ({ one, many }) => ({
  business: one(business, {
    fields: [products.businessId],
    references: [business.id],
  }),
  brand: one(brands, {
    fields: [products.brandId],
    references: [brands.id],
  }),
  category: one(categories, {
    fields: [products.categoryId],
    references: [categories.id],
  }),
  subCategory: one(categories, {
    fields: [products.subCategoryId],
    references: [categories.id],
  }),
  warranty: one(warrantyTemplates, {
    fields: [products.warrantyId],
    references: [warrantyTemplates.id],
  }),
  preferredSupplier: one(suppliers, {
    fields: [products.preferredSupplierId],
    references: [suppliers.id],
  }),
  customUnit: one(units, {
    fields: [products.customUnitId],
    references: [units.id],
  }),
  incomeAccount: one(accounts, {
    fields: [products.incomeAccountId],
    references: [accounts.id],
  }),
  expenseAccount: one(accounts, {
    fields: [products.expenseAccountId],
    references: [accounts.id],
  }),
  inventoryAccount: one(accounts, {
    fields: [products.inventoryAccountId],
    references: [accounts.id],
  }),
  defaultTaxRate: one(taxes, {
    fields: [products.defaultTaxRateId],
    references: [taxes.id],
  }),
  ogImage: one(media, {
    fields: [products.ogImage],
    references: [media.id],
    relationName: 'productOgImage',
  }),
  creator: one(users, {
    fields: [products.createdBy],
    references: [users.id],
    relationName: 'createdProducts',
  }),
  updater: one(users, {
    fields: [products.updatedBy],
    references: [users.id],
    relationName: 'updatedProducts',
  }),
  deleter: one(users, {
    fields: [products.deletedBy],
    references: [users.id],
    relationName: 'deletedProducts',
  }),

  // Product-related tables
  productVariants: many(productVariants),
  productLocations: many(productLocations),
  comboProductsParent: many(comboProducts, {
    relationName: 'parentProduct',
  }),
  comboProductsChild: many(comboProducts, {
    relationName: 'childProduct',
  }),
  inventory: many(inventory),
  serialNumbers: many(serialNumbers),
  batchNumbers: many(batchNumbers),
  productGroupPrices: many(productGroupPrices),

  // Existing relations
  productRacks: many(productRacks),
  variations: many(variations),
  variationLocationDetails: many(variationLocationDetails),
  locations: many(locations, {
    relationName: 'productLocations',
  }),
  estimateLineItems: many(estimateLineItems),
  discountPlanProducts: many(discountPlanProducts),
  warranties: many(warranties),
  billOfMaterials: many(billOfMaterials),
  bomLineComponents: many(bomLines, {
    relationName: 'componentProduct',
  }),
}));

// Packages Relations
export const packagesRelations = relations(packages, ({ one }) => ({
  business: one(business, {
    fields: [packages.businessId],
    references: [business.id],
  }),
  incomeAccount: one(accounts, {
    fields: [packages.incomeAccountId],
    references: [accounts.id],
  }),
  expenseAccount: one(accounts, {
    fields: [packages.expenseAccountId],
    references: [accounts.id],
  }),
  ogImage: one(media, {
    fields: [packages.ogImage],
    references: [media.id],
    relationName: 'packageOgImage',
  }),
  defaultTaxRate: one(taxes, {
    fields: [packages.defaultTaxRateId],
    references: [taxes.id],
  }),
  creator: one(users, {
    fields: [packages.createdBy],
    references: [users.id],
    relationName: 'createdPackages',
  }),
  updater: one(users, {
    fields: [packages.updatedBy],
    references: [users.id],
    relationName: 'updatedPackages',
  }),
  deleter: one(users, {
    fields: [packages.deletedBy],
    references: [users.id],
    relationName: 'deletedPackages',
  }),
}));

// Service Time Slots Relations
export const serviceTimeSlotsRelations = relations(
  serviceTimeSlots,
  ({ one, many }) => ({
    location: one(locations, {
      fields: [serviceTimeSlots.locationId],
      references: [locations.id],
    }),
    creator: one(users, {
      fields: [serviceTimeSlots.createdBy],
      references: [users.id],
      relationName: 'createdServiceTimeSlots',
    }),
    updater: one(users, {
      fields: [serviceTimeSlots.updatedBy],
      references: [users.id],
      relationName: 'updatedServiceTimeSlots',
    }),
    deleter: one(users, {
      fields: [serviceTimeSlots.deletedBy],
      references: [users.id],
      relationName: 'deletedServiceTimeSlots',
    }),

    // Many-to-many relations through join tables
    staffAssignments: many(serviceTimeSlotStaff),
    serviceAssignments: many(serviceTimeSlotServices),
  }),
);

// Service Time Slot Staff Relations
export const serviceTimeSlotStaffRelations = relations(
  serviceTimeSlotStaff,
  ({ one }) => ({
    timeSlot: one(serviceTimeSlots, {
      fields: [serviceTimeSlotStaff.timeSlotId],
      references: [serviceTimeSlots.id],
    }),
    staff: one(staffMembers, {
      fields: [serviceTimeSlotStaff.staffId],
      references: [staffMembers.id],
    }),
    creator: one(users, {
      fields: [serviceTimeSlotStaff.createdBy],
      references: [users.id],
      relationName: 'createdServiceTimeSlotStaff',
    }),
    updater: one(users, {
      fields: [serviceTimeSlotStaff.updatedBy],
      references: [users.id],
      relationName: 'updatedServiceTimeSlotStaff',
    }),
    deleter: one(users, {
      fields: [serviceTimeSlotStaff.deletedBy],
      references: [users.id],
      relationName: 'deletedServiceTimeSlotStaff',
    }),
  }),
);

// Service Time Slot Services Relations
export const serviceTimeSlotServicesRelations = relations(
  serviceTimeSlotServices,
  ({ one }) => ({
    timeSlot: one(serviceTimeSlots, {
      fields: [serviceTimeSlotServices.timeSlotId],
      references: [serviceTimeSlots.id],
    }),
    service: one(services, {
      fields: [serviceTimeSlotServices.serviceId],
      references: [services.id],
    }),
    creator: one(users, {
      fields: [serviceTimeSlotServices.createdBy],
      references: [users.id],
      relationName: 'createdServiceTimeSlotServices',
    }),
    updater: one(users, {
      fields: [serviceTimeSlotServices.updatedBy],
      references: [users.id],
      relationName: 'updatedServiceTimeSlotServices',
    }),
    deleter: one(users, {
      fields: [serviceTimeSlotServices.deletedBy],
      references: [users.id],
      relationName: 'deletedServiceTimeSlotServices',
    }),
  }),
);

export const productRacksRelations = relations(productRacks, ({ one }) => ({
  product: one(products, {
    fields: [productRacks.productId],
    references: [products.id],
  }),
  location: one(locations, {
    fields: [productRacks.locationId],
    references: [locations.id],
  }),
  business: one(business, {
    fields: [productRacks.businessId],
    references: [business.id],
  }),
}));

export const productVariantsRelations = relations(
  productVariants,
  ({ one, many }) => ({
    product: one(products, {
      fields: [productVariants.productId],
      references: [products.id],
    }),
    business: one(business, {
      fields: [productVariants.businessId],
      references: [business.id],
    }),
    variationTemplate: one(variationTemplates, {
      fields: [productVariants.variationTemplateId],
      references: [variationTemplates.id],
    }),
    variationValueTemplate: one(variationValueTemplates, {
      fields: [productVariants.variationValueTemplateId],
      references: [variationValueTemplates.id],
    }),
    image: one(media, {
      fields: [productVariants.image],
      references: [media.id],
      relationName: 'productVariantImage',
    }),
    creator: one(users, {
      fields: [productVariants.createdBy],
      references: [users.id],
      relationName: 'createdProductVariants',
    }),
    updater: one(users, {
      fields: [productVariants.updatedBy],
      references: [users.id],
      relationName: 'updatedProductVariants',
    }),
    deleter: one(users, {
      fields: [productVariants.deletedBy],
      references: [users.id],
      relationName: 'deletedProductVariants',
    }),

    productLocations: many(productLocations),
    comboProducts: many(comboProducts, {
      relationName: 'childVariant',
    }),
    inventory: many(inventory),
    serialNumbers: many(serialNumbers),
    batchNumbers: many(batchNumbers),
    productGroupPrices: many(productGroupPrices),
    warranties: many(warranties),
  }),
);

export const productLocationsRelations = relations(
  productLocations,
  ({ one }) => ({
    product: one(products, {
      fields: [productLocations.productId],
      references: [products.id],
    }),
    variant: one(productVariants, {
      fields: [productLocations.variantId],
      references: [productVariants.id],
    }),
    location: one(locations, {
      fields: [productLocations.locationId],
      references: [locations.id],
    }),
    creator: one(users, {
      fields: [productLocations.createdBy],
      references: [users.id],
      relationName: 'createdProductLocations',
    }),
    updater: one(users, {
      fields: [productLocations.updatedBy],
      references: [users.id],
      relationName: 'updatedProductLocations',
    }),
  }),
);

export const comboProductsRelations = relations(comboProducts, ({ one }) => ({
  parentProduct: one(products, {
    fields: [comboProducts.parentProductId],
    references: [products.id],
    relationName: 'parentProduct',
  }),
  childProduct: one(products, {
    fields: [comboProducts.childProductId],
    references: [products.id],
    relationName: 'childProduct',
  }),
  childVariant: one(productVariants, {
    fields: [comboProducts.childVariantId],
    references: [productVariants.id],
    relationName: 'childVariant',
  }),
  creator: one(users, {
    fields: [comboProducts.createdBy],
    references: [users.id],
    relationName: 'createdComboProducts',
  }),
  updater: one(users, {
    fields: [comboProducts.updatedBy],
    references: [users.id],
    relationName: 'updatedComboProducts',
  }),
}));

export const inventoryRelations = relations(inventory, ({ one }) => ({
  product: one(products, {
    fields: [inventory.productId],
    references: [products.id],
  }),
  variant: one(productVariants, {
    fields: [inventory.variantId],
    references: [productVariants.id],
  }),
  location: one(locations, {
    fields: [inventory.locationId],
    references: [locations.id],
  }),
}));

export const serialNumbersRelations = relations(serialNumbers, ({ one }) => ({
  product: one(products, {
    fields: [serialNumbers.productId],
    references: [products.id],
  }),
  variant: one(productVariants, {
    fields: [serialNumbers.variantId],
    references: [productVariants.id],
  }),
  location: one(locations, {
    fields: [serialNumbers.locationId],
    references: [locations.id],
  }),
}));

export const batchNumbersRelations = relations(batchNumbers, ({ one }) => ({
  product: one(products, {
    fields: [batchNumbers.productId],
    references: [products.id],
  }),
  variant: one(productVariants, {
    fields: [batchNumbers.variantId],
    references: [productVariants.id],
  }),
  location: one(locations, {
    fields: [batchNumbers.locationId],
    references: [locations.id],
  }),
}));

export const productGroupPricesRelations = relations(
  productGroupPrices,
  ({ one }) => ({
    product: one(products, {
      fields: [productGroupPrices.productId],
      references: [products.id],
    }),
    variant: one(productVariants, {
      fields: [productGroupPrices.variantId],
      references: [productVariants.id],
    }),
    group: one(customerGroups, {
      fields: [productGroupPrices.groupId],
      references: [customerGroups.id],
    }),
    creator: one(users, {
      fields: [productGroupPrices.createdBy],
      references: [users.id],
      relationName: 'createdProductGroupPrices',
    }),
  }),
);

export const variationsRelations = relations(variations, ({ one, many }) => ({
  business: one(business, {
    fields: [variations.businessId],
    references: [business.id],
  }),
  product: one(products, {
    fields: [variations.productId],
    references: [products.id],
  }),
  variationValue: one(variationValueTemplates, {
    fields: [variations.variationValueId],
    references: [variationValueTemplates.id],
  }),
  creator: one(users, {
    fields: [variations.createdBy],
    references: [users.id],
  }),
  locationDetails: many(variationLocationDetails),
}));

export const variationLocationDetailsRelations = relations(
  variationLocationDetails,
  ({ one }) => ({
    business: one(business, {
      fields: [variationLocationDetails.businessId],
      references: [business.id],
    }),
    product: one(products, {
      fields: [variationLocationDetails.productId],
      references: [products.id],
    }),
    variation: one(variations, {
      fields: [variationLocationDetails.variationId],
      references: [variations.id],
    }),
    location: one(locations, {
      fields: [variationLocationDetails.locationId],
      references: [locations.id],
    }),
  }),
);

export const customFieldsRelations = relations(customFields, ({ one }) => ({
  business: one(business, {
    fields: [customFields.businessId],
    references: [business.id],
  }),
  creator: one(users, {
    fields: [customFields.createdBy],
    references: [users.id],
    relationName: 'createdCustomFields',
  }),
}));

export const paymentAccountTypesRelations = relations(
  paymentAccountTypes,
  ({ one, many }) => ({
    business: one(business, {
      fields: [paymentAccountTypes.businessId],
      references: [business.id],
    }),
    creator: one(users, {
      fields: [paymentAccountTypes.createdBy],
      references: [users.id],
      relationName: 'createdPaymentAccountTypes',
    }),
    parent: one(paymentAccountTypes, {
      fields: [paymentAccountTypes.parentAccountTypeId],
      references: [paymentAccountTypes.id],
    }),
    children: many(paymentAccountTypes, { relationName: 'children' }),
    paymentAccounts: many(paymentAccounts),
  }),
);

export const paymentAccountsRelations = relations(
  paymentAccounts,
  ({ one, many }) => ({
    business: one(business, {
      fields: [paymentAccounts.businessId],
      references: [business.id],
    }),
    creator: one(users, {
      fields: [paymentAccounts.createdBy],
      references: [users.id],
      relationName: 'createdPaymentAccounts',
    }),
    accountType: one(paymentAccountTypes, {
      fields: [paymentAccounts.accountTypeId],
      references: [paymentAccountTypes.id],
    }),
    transactions: many(transactions),
  }),
);

export const paymentMethodsRelations = relations(
  paymentMethods,
  ({ one, many }) => ({
    business: one(business, {
      fields: [paymentMethods.businessId],
      references: [business.id],
    }),
    creator: one(users, {
      fields: [paymentMethods.createdBy],
      references: [users.id],
      relationName: 'createdPaymentMethods',
    }),
    updater: one(users, {
      fields: [paymentMethods.updatedBy],
      references: [users.id],
      relationName: 'updatedPaymentMethods',
    }),
    deleter: one(users, {
      fields: [paymentMethods.deletedBy],
      references: [users.id],
      relationName: 'deletedPaymentMethods',
    }),
    // Asset Maintenance relations
    assetMaintenances: many(assetMaintenances, {
      relationName: 'assetMaintenancePaymentMethod',
    }),
    // Asset Repair Orders relations
    assetRepairOrders: many(assetRepairOrders, {
      relationName: 'assetRepairOrderPaymentMethod',
    }),
    // Vehicle Fines relations
    vehicleFines: many(vehicleFines),
  }),
);

export const assetsRelations = relations(assets, ({ one, many }) => ({
  business: one(business, {
    fields: [assets.businessId],
    references: [business.id],
  }),
  type: one(assetTypes, {
    fields: [assets.typeId],
    references: [assetTypes.id],
    relationName: 'assetType',
  }),
  subType: one(assetTypes, {
    fields: [assets.subTypeId],
    references: [assetTypes.id],
    relationName: 'assetSubType',
  }),
  location: one(locations, {
    fields: [assets.locationId],
    references: [locations.id],
  }),
  vendor: one(suppliers, {
    fields: [assets.vendorId],
    references: [suppliers.id],
  }),
  image: one(media, {
    fields: [assets.image],
    references: [media.id],
    relationName: 'assetImage',
  }),
  // Account relationships
  fixedAssetAccount: one(accounts, {
    fields: [assets.fixedAssetAccountId],
    references: [accounts.id],
    relationName: 'assetFixedAssetAccount',
  }),
  depreciationAccount: one(accounts, {
    fields: [assets.depreciationAccountId],
    references: [accounts.id],
    relationName: 'assetDepreciationAccount',
  }),
  expenseAccount: one(accounts, {
    fields: [assets.expenseAccountId],
    references: [accounts.id],
    relationName: 'assetExpenseAccount',
  }),
  // User relationships
  creator: one(users, {
    fields: [assets.createdBy],
    references: [users.id],
    relationName: 'createdAssets',
  }),
  updater: one(users, {
    fields: [assets.updatedBy],
    references: [users.id],
    relationName: 'updatedAssets',
  }),
  deleter: one(users, {
    fields: [assets.deletedBy],
    references: [users.id],
    relationName: 'deletedAssets',
  }),

  transactions: many(assetTransactions),
  warranties: many(assetWarranties),
  maintenances: many(assetMaintenances),
  repairOrders: many(assetRepairOrders),
}));

export const assetTransactionsRelations = relations(
  assetTransactions,
  ({ one }) => ({
    business: one(business, {
      fields: [assetTransactions.businessId],
      references: [business.id],
    }),
    asset: one(assets, {
      fields: [assetTransactions.assetId],
      references: [assets.id],
    }),
    receiver: one(staffMembers, {
      fields: [assetTransactions.receiver],
      references: [staffMembers.id],
      relationName: 'receivedAssetTransactions',
    }),
    fromLocation: one(locations, {
      fields: [assetTransactions.fromLocationId],
      references: [locations.id],
      relationName: 'assetTransactionsFromLocation',
    }),
    toLocation: one(locations, {
      fields: [assetTransactions.toLocationId],
      references: [locations.id],
      relationName: 'assetTransactionsToLocation',
    }),
    creator: one(users, {
      fields: [assetTransactions.createdBy],
      references: [users.id],
      relationName: 'createdAssetTransactions',
    }),
    parent: one(assetTransactions, {
      fields: [assetTransactions.parentId],
      references: [assetTransactions.id],
    }),
  }),
);

export const assetWarrantiesRelations = relations(
  assetWarranties,
  ({ one }) => ({
    asset: one(assets, {
      fields: [assetWarranties.assetId],
      references: [assets.id],
    }),
  }),
);

export const assetMaintenancesRelations = relations(
  assetMaintenances,
  ({ one }) => ({
    business: one(business, {
      fields: [assetMaintenances.businessId],
      references: [business.id],
    }),
    asset: one(assets, {
      fields: [assetMaintenances.assetId],
      references: [assets.id],
    }),
    supplier: one(suppliers, {
      fields: [assetMaintenances.supplierId],
      references: [suppliers.id],
      relationName: 'assetMaintenanceSupplier',
    }),
    assignedStaffMember: one(staffMembers, {
      fields: [assetMaintenances.assignedTo],
      references: [staffMembers.id],
      relationName: 'assignedAssetMaintenances',
    }),
    qualityCheckStaffMember: one(staffMembers, {
      fields: [assetMaintenances.qualityCheckBy],
      references: [staffMembers.id],
      relationName: 'qualityCheckAssetMaintenances',
    }),
    expenseAccount: one(accounts, {
      fields: [assetMaintenances.expenseAccountId],
      references: [accounts.id],
      relationName: 'assetMaintenanceExpenseAccount',
    }),
    paymentAccount: one(accounts, {
      fields: [assetMaintenances.paymentAccountId],
      references: [accounts.id],
      relationName: 'assetMaintenancePaymentAccount',
    }),
    paymentMethod: one(paymentMethods, {
      fields: [assetMaintenances.paymentMethodId],
      references: [paymentMethods.id],
      relationName: 'assetMaintenancePaymentMethod',
    }),
    tax: one(taxes, {
      fields: [assetMaintenances.taxId],
      references: [taxes.id],
      relationName: 'assetMaintenanceTax',
    }),
    creator: one(users, {
      fields: [assetMaintenances.createdBy],
      references: [users.id],
      relationName: 'createdAssetMaintenances',
    }),
    updater: one(users, {
      fields: [assetMaintenances.updatedBy],
      references: [users.id],
      relationName: 'updatedAssetMaintenances',
    }),
    deleter: one(users, {
      fields: [assetMaintenances.deletedBy],
      references: [users.id],
      relationName: 'deletedAssetMaintenances',
    }),
  }),
);

export const assetRepairOrdersRelations = relations(
  assetRepairOrders,
  ({ one }) => ({
    business: one(business, {
      fields: [assetRepairOrders.businessId],
      references: [business.id],
    }),
    asset: one(assets, {
      fields: [assetRepairOrders.assetId],
      references: [assets.id],
    }),
    reportedByStaffMember: one(staffMembers, {
      fields: [assetRepairOrders.reportedBy],
      references: [staffMembers.id],
      relationName: 'reportedAssetRepairOrders',
    }),
    assignedStaffMember: one(staffMembers, {
      fields: [assetRepairOrders.assignedTo],
      references: [staffMembers.id],
      relationName: 'assignedAssetRepairOrders',
    }),
    qualityCheckStaffMember: one(staffMembers, {
      fields: [assetRepairOrders.qualityCheckBy],
      references: [staffMembers.id],
      relationName: 'qualityCheckAssetRepairOrders',
    }),
    supplier: one(suppliers, {
      fields: [assetRepairOrders.supplierId],
      references: [suppliers.id],
      relationName: 'assetRepairOrderSupplier',
    }),
    expenseAccount: one(accounts, {
      fields: [assetRepairOrders.expenseAccountId],
      references: [accounts.id],
      relationName: 'assetRepairOrderExpenseAccount',
    }),
    paymentAccount: one(accounts, {
      fields: [assetRepairOrders.paymentAccountId],
      references: [accounts.id],
      relationName: 'assetRepairOrderPaymentAccount',
    }),
    paymentMethod: one(paymentMethods, {
      fields: [assetRepairOrders.paymentMethodId],
      references: [paymentMethods.id],
      relationName: 'assetRepairOrderPaymentMethod',
    }),
    tax: one(taxes, {
      fields: [assetRepairOrders.taxId],
      references: [taxes.id],
      relationName: 'assetRepairOrderTax',
    }),
    creator: one(users, {
      fields: [assetRepairOrders.createdBy],
      references: [users.id],
      relationName: 'createdAssetRepairOrders',
    }),
    updater: one(users, {
      fields: [assetRepairOrders.updatedBy],
      references: [users.id],
      relationName: 'updatedAssetRepairOrders',
    }),
    deleter: one(users, {
      fields: [assetRepairOrders.deletedBy],
      references: [users.id],
      relationName: 'deletedAssetRepairOrders',
    }),
  }),
);

export const assetCategoriesRelations = relations(
  assetCategories,
  ({ one, many }) => ({
    business: one(business, {
      fields: [assetCategories.businessId],
      references: [business.id],
    }),
    creator: one(users, {
      fields: [assetCategories.createdBy],
      references: [users.id],
      relationName: 'createdAssetCategories',
    }),
    parent: one(assetCategories, {
      fields: [assetCategories.parentId],
      references: [assetCategories.id],
    }),
    children: many(assetCategories, { relationName: 'children' }),
    assets: many(assets),
  }),
);

export const assetTypesRelations = relations(assetTypes, ({ one, many }) => ({
  business: one(business, {
    fields: [assetTypes.businessId],
    references: [business.id],
  }),
  creator: one(users, {
    fields: [assetTypes.createdBy],
    references: [users.id],
    relationName: 'createdAssetTypes',
  }),
  parent: one(assetTypes, {
    fields: [assetTypes.parentId],
    references: [assetTypes.id],
  }),
  children: many(assetTypes, { relationName: 'children' }),
  assetsAsType: many(assets, { relationName: 'assetType' }),
  assetsAsSubType: many(assets, { relationName: 'assetSubType' }),
  rentalItemsAsAssetType: many(rentalItems, {
    relationName: 'rentalItemAssetType',
  }),
  rentalItemsAsAssetSubType: many(rentalItems, {
    relationName: 'rentalItemAssetSubType',
  }),
}));

export const suppliersRelations = relations(suppliers, ({ one, many }) => ({
  business: one(business, {
    fields: [suppliers.businessId],
    references: [business.id],
  }),
  creator: one(users, {
    fields: [suppliers.createdBy],
    references: [users.id],
    relationName: 'createdSuppliers',
  }),
  updater: one(users, {
    fields: [suppliers.updatedBy],
    references: [users.id],
    relationName: 'updatedSuppliers',
  }),
  deleter: one(users, {
    fields: [suppliers.deletedBy],
    references: [users.id],
    relationName: 'deletedSuppliers',
  }),
  profileImage: one(media, {
    fields: [suppliers.profileImage],
    references: [media.id],
    relationName: 'supplierProfileImage',
  }),
  // Direct foreign key relations
  address: one(addresses, {
    fields: [suppliers.addressId],
    references: [addresses.id],
  }),
  bankAccount: one(bankAccounts, {
    fields: [suppliers.bankAccountId],
    references: [bankAccounts.id],
  }),
  // Other relations
  assets: many(assets),
  transactions: many(transactions),
  tasks: many(tasks, { relationName: 'supplierTasks' }),
  // Service relations
  preferredServices: many(services, {
    relationName: 'servicePreferredSupplier',
  }),
  // Asset Maintenance relations
  assetMaintenances: many(assetMaintenances, {
    relationName: 'assetMaintenanceSupplier',
  }),
  // Asset Repair Orders relations
  assetRepairOrders: many(assetRepairOrders, {
    relationName: 'assetRepairOrderSupplier',
  }),
}));

export const notificationsRelations = relations(notifications, ({ one }) => ({
  business: one(business, {
    fields: [notifications.businessId],
    references: [business.id],
  }),
  // Notifications are polymorphic, so they can relate to different entities
  // based on notifiableType and notifiableId
  // Additional relations would be defined based on business requirements
}));

export const vehicleTypesRelations = relations(
  vehicleTypes,
  ({ one, many }) => ({
    business: one(business, {
      fields: [vehicleTypes.businessId],
      references: [business.id],
    }),
    creator: one(users, {
      fields: [vehicleTypes.createdBy],
      references: [users.id],
      relationName: 'createdVehicleTypes',
    }),
    parent: one(vehicleTypes, {
      fields: [vehicleTypes.parentId],
      references: [vehicleTypes.id],
    }),
    children: many(vehicleTypes, { relationName: 'children' }),
    image: one(media, {
      fields: [vehicleTypes.image],
      references: [media.id],
      relationName: 'vehicleTypeImage',
    }),
    ogImage: one(media, {
      fields: [vehicleTypes.ogImage],
      references: [media.id],
      relationName: 'vehicleTypeOgImage',
    }),
    vehiclesAsType: many(vehicles, { relationName: 'vehicleType' }),
    vehiclesAsSubType: many(vehicles, { relationName: 'vehicleSubType' }),
    vehicleTypeLocations: many(vehicleTypeLocations),
  }),
);

export const vehicleTypeLocationsRelations = relations(
  vehicleTypeLocations,
  ({ one }) => ({
    vehicleType: one(vehicleTypes, {
      fields: [vehicleTypeLocations.vehicleTypeId],
      references: [vehicleTypes.id],
    }),
    location: one(locations, {
      fields: [vehicleTypeLocations.locationId],
      references: [locations.id],
    }),
    creator: one(users, {
      fields: [vehicleTypeLocations.createdBy],
      references: [users.id],
      relationName: 'createdVehicleTypeLocations',
    }),
    updater: one(users, {
      fields: [vehicleTypeLocations.updatedBy],
      references: [users.id],
      relationName: 'updatedVehicleTypeLocations',
    }),
    deleter: one(users, {
      fields: [vehicleTypeLocations.deletedBy],
      references: [users.id],
      relationName: 'deletedVehicleTypeLocations',
    }),
  }),
);

export const reservationTypesRelations = relations(
  reservationTypes,
  ({ one, many }) => ({
    business: one(business, {
      fields: [reservationTypes.businessId],
      references: [business.id],
    }),
    creator: one(users, {
      fields: [reservationTypes.createdBy],
      references: [users.id],
      relationName: 'createdReservationTypes',
    }),
    parent: one(reservationTypes, {
      fields: [reservationTypes.parentId],
      references: [reservationTypes.id],
    }),
    children: many(reservationTypes, { relationName: 'children' }),
    image: one(media, {
      fields: [reservationTypes.image],
      references: [media.id],
      relationName: 'reservationTypeImage',
    }),
    ogImage: one(media, {
      fields: [reservationTypes.ogImage],
      references: [media.id],
      relationName: 'reservationTypeOgImage',
    }),
    reservationTypeLocations: many(reservationTypeLocations),
  }),
);

export const reservationTypeLocationsRelations = relations(
  reservationTypeLocations,
  ({ one }) => ({
    reservationType: one(reservationTypes, {
      fields: [reservationTypeLocations.reservationTypeId],
      references: [reservationTypes.id],
    }),
    location: one(locations, {
      fields: [reservationTypeLocations.locationId],
      references: [locations.id],
    }),
    creator: one(users, {
      fields: [reservationTypeLocations.createdBy],
      references: [users.id],
      relationName: 'createdReservationTypeLocations',
    }),
    updater: one(users, {
      fields: [reservationTypeLocations.updatedBy],
      references: [users.id],
      relationName: 'updatedReservationTypeLocations',
    }),
    deleter: one(users, {
      fields: [reservationTypeLocations.deletedBy],
      references: [users.id],
      relationName: 'deletedReservationTypeLocations',
    }),
  }),
);

export const accommodationUnitsRelations = relations(
  accommodationUnits,
  ({ one }) => ({
    business: one(business, {
      fields: [accommodationUnits.businessId],
      references: [business.id],
    }),
    creator: one(users, {
      fields: [accommodationUnits.createdBy],
      references: [users.id],
      relationName: 'createdAccommodationUnits',
    }),
    updater: one(users, {
      fields: [accommodationUnits.updatedBy],
      references: [users.id],
      relationName: 'updatedAccommodationUnits',
    }),
    deleter: one(users, {
      fields: [accommodationUnits.deletedBy],
      references: [users.id],
      relationName: 'deletedAccommodationUnits',
    }),
    type: one(reservationTypes, {
      fields: [accommodationUnits.type],
      references: [reservationTypes.id],
      relationName: 'accommodationUnitType',
    }),
    subType: one(reservationTypes, {
      fields: [accommodationUnits.subType],
      references: [reservationTypes.id],
      relationName: 'accommodationUnitSubType',
    }),
    incomeAccount: one(accounts, {
      fields: [accommodationUnits.incomeAccountId],
      references: [accounts.id],
      relationName: 'accommodationUnitIncomeAccount',
    }),
    expenseAccount: one(accounts, {
      fields: [accommodationUnits.expenseAccountId],
      references: [accounts.id],
      relationName: 'accommodationUnitExpenseAccount',
    }),
    defaultTaxRate: one(taxes, {
      fields: [accommodationUnits.defaultTaxRateId],
      references: [taxes.id],
      relationName: 'accommodationUnitDefaultTaxRate',
    }),
    ogImage: one(media, {
      fields: [accommodationUnits.ogImage],
      references: [media.id],
      relationName: 'accommodationUnitOgImage',
    }),
  }),
);

export const vehiclesRelations = relations(vehicles, ({ one, many }) => ({
  business: one(business, {
    fields: [vehicles.businessId],
    references: [business.id],
  }),
  vehicleType: one(vehicleTypes, {
    fields: [vehicles.vehicleTypeId],
    references: [vehicleTypes.id],
    relationName: 'vehicleType',
  }),
  subType: one(vehicleTypes, {
    fields: [vehicles.subTypeId],
    references: [vehicleTypes.id],
    relationName: 'vehicleSubType',
  }),
  currentLocation: one(locations, {
    fields: [vehicles.currentLocationId],
    references: [locations.id],
    relationName: 'vehicleLocation',
  }),
  creator: one(users, {
    fields: [vehicles.createdBy],
    references: [users.id],
    relationName: 'createdVehicles',
  }),
  // Media relationships
  image: one(media, {
    fields: [vehicles.image],
    references: [media.id],
    relationName: 'vehicleImage',
  }),
  ogImage: one(media, {
    fields: [vehicles.ogImage],
    references: [media.id],
    relationName: 'vehicleOgImage',
  }),
  // Account relationships
  fixedAssetAccount: one(accounts, {
    fields: [vehicles.fixedAssetAccountId],
    references: [accounts.id],
    relationName: 'vehicleFixedAssetAccount',
  }),
  depreciationAccount: one(accounts, {
    fields: [vehicles.depreciationAccountId],
    references: [accounts.id],
    relationName: 'vehicleDepreciationAccount',
  }),
  expenseAccount: one(accounts, {
    fields: [vehicles.expenseAccountId],
    references: [accounts.id],
    relationName: 'vehicleExpenseAccount',
  }),
  damages: many(assetDamage),
  blockedPeriods: many(vehicleBlockedPeriods),
  fines: many(vehicleFines),
  expenses: many(expenses),
  repairOrders: many(vehicleRepairOrders),
}));

export const assetDamageRelations = relations(assetDamage, ({ one }) => ({
  business: one(business, {
    fields: [assetDamage.businessId],
    references: [business.id],
  }),
  asset: one(assets, {
    fields: [assetDamage.assetId],
    references: [assets.id],
  }),
  creator: one(users, {
    fields: [assetDamage.createdBy],
    references: [users.id],
    relationName: 'createdAssetDamage',
  }),
  fixedByStaff: one(staffMembers, {
    fields: [assetDamage.fixedBy],
    references: [staffMembers.id],
    relationName: 'fixedAssetDamage',
  }),
  checkedByStaff: one(staffMembers, {
    fields: [assetDamage.checkedBy],
    references: [staffMembers.id],
    relationName: 'checkedAssetDamage',
  }),
}));

export const vehicleBlockedPeriodsRelations = relations(
  vehicleBlockedPeriods,
  ({ one }) => ({
    business: one(business, {
      fields: [vehicleBlockedPeriods.businessId],
      references: [business.id],
    }),
    vehicle: one(vehicles, {
      fields: [vehicleBlockedPeriods.vehicleId],
      references: [vehicles.id],
    }),
    creator: one(users, {
      fields: [vehicleBlockedPeriods.createdBy],
      references: [users.id],
      relationName: 'createdVehicleBlockedPeriods',
    }),
  }),
);

export const vehicleFinesRelations = relations(vehicleFines, ({ one }) => ({
  business: one(business, {
    fields: [vehicleFines.businessId],
    references: [business.id],
  }),
  vehicle: one(vehicles, {
    fields: [vehicleFines.vehicleId],
    references: [vehicles.id],
  }),
  creator: one(users, {
    fields: [vehicleFines.createdBy],
    references: [users.id],
    relationName: 'createdVehicleFines',
  }),
  scannedFile: one(media, {
    fields: [vehicleFines.scannedFileId],
    references: [media.id],
  }),
  expenseAccount: one(accounts, {
    fields: [vehicleFines.expenseAccountId],
    references: [accounts.id],
    relationName: 'vehicleFineExpenseAccount',
  }),
  paymentAccount: one(accounts, {
    fields: [vehicleFines.paymentAccountId],
    references: [accounts.id],
    relationName: 'vehicleFinePaymentAccount',
  }),
  paymentMethod: one(paymentMethods, {
    fields: [vehicleFines.paymentMethodId],
    references: [paymentMethods.id],
  }),
}));

// Vehicle Repair Order Relations
export const vehicleRepairOrdersRelations = relations(
  vehicleRepairOrders,
  ({ one }) => ({
    business: one(business, {
      fields: [vehicleRepairOrders.businessId],
      references: [business.id],
    }),
    vehicle: one(vehicles, {
      fields: [vehicleRepairOrders.vehicleId],
      references: [vehicles.id],
    }),
    mechanic: one(staffMembers, {
      fields: [vehicleRepairOrders.mechanicId],
      references: [staffMembers.id],
      relationName: 'mechanicRepairOrders',
    }),
    qualityCheckUser: one(staffMembers, {
      fields: [vehicleRepairOrders.qualityCheckBy],
      references: [staffMembers.id],
      relationName: 'qualityCheckRepairOrders',
    }),
    creator: one(users, {
      fields: [vehicleRepairOrders.createdBy],
      references: [users.id],
      relationName: 'createdRepairOrders',
    }),
  }),
);

// SMS Templates Relations
export const smsTemplatesRelations = relations(smsTemplates, ({ one }) => ({
  business: one(business, {
    fields: [smsTemplates.businessId],
    references: [business.id],
  }),
  creator: one(users, {
    fields: [smsTemplates.createdBy],
    references: [users.id],
    relationName: 'createdSmsTemplates',
  }),
  updater: one(users, {
    fields: [smsTemplates.updatedBy],
    references: [users.id],
    relationName: 'updatedSmsTemplates',
  }),
  deleter: one(users, {
    fields: [smsTemplates.deletedBy],
    references: [users.id],
    relationName: 'deletedSmsTemplates',
  }),
}));

// WhatsApp Templates Relations
export const whatsappTemplatesRelations = relations(
  whatsappTemplates,
  ({ one }) => ({
    business: one(business, {
      fields: [whatsappTemplates.businessId],
      references: [business.id],
    }),
    creator: one(users, {
      fields: [whatsappTemplates.createdBy],
      references: [users.id],
      relationName: 'createdWhatsappTemplates',
    }),
    updater: one(users, {
      fields: [whatsappTemplates.updatedBy],
      references: [users.id],
      relationName: 'updatedWhatsappTemplates',
    }),
    deleter: one(users, {
      fields: [whatsappTemplates.deletedBy],
      references: [users.id],
      relationName: 'deletedWhatsappTemplates',
    }),
    mediaFile: one(media, {
      fields: [whatsappTemplates.mediaId],
      references: [media.id],
    }),
  }),
);

// Email Templates Relations
export const emailTemplatesRelations = relations(emailTemplates, ({ one }) => ({
  business: one(business, {
    fields: [emailTemplates.businessId],
    references: [business.id],
  }),
  creator: one(users, {
    fields: [emailTemplates.createdBy],
    references: [users.id],
    relationName: 'createdEmailTemplates',
  }),
  updater: one(users, {
    fields: [emailTemplates.updatedBy],
    references: [users.id],
    relationName: 'updatedEmailTemplates',
  }),
  deleter: one(users, {
    fields: [emailTemplates.deletedBy],
    references: [users.id],
    relationName: 'deletedEmailTemplates',
  }),
}));

export const providersRelations = relations(providers, ({ one }) => ({
  business: one(business, {
    fields: [providers.businessId],
    references: [business.id],
  }),
  creator: one(users, {
    fields: [providers.createdBy],
    references: [users.id],
    relationName: 'createdProviders',
  }),
  updater: one(users, {
    fields: [providers.updatedBy],
    references: [users.id],
    relationName: 'updatedProviders',
  }),
  deleter: one(users, {
    fields: [providers.deletedBy],
    references: [users.id],
    relationName: 'deletedProviders',
  }),
}));

export const bankAccountsRelations = relations(
  bankAccounts,
  ({ one, many }) => ({
    business: one(business, {
      fields: [bankAccounts.businessId],
      references: [business.id],
    }),
    creator: one(users, {
      fields: [bankAccounts.createdBy],
      references: [users.id],
      relationName: 'createdBankAccounts',
    }),
    updater: one(users, {
      fields: [bankAccounts.updatedBy],
      references: [users.id],
    }),
    deleter: one(users, {
      fields: [bankAccounts.deletedBy],
      references: [users.id],
    }),
    suppliers: many(suppliers),
  }),
);

export const accountsRelations = relations(accounts, ({ one, many }) => ({
  business: one(business, {
    fields: [accounts.businessId],
    references: [business.id],
  }),
  creator: one(users, {
    fields: [accounts.createdBy],
    references: [users.id],
    relationName: 'createdAccounts',
  }),
  updater: one(users, {
    fields: [accounts.updatedBy],
    references: [users.id],
    relationName: 'updatedAccounts',
  }),
  deleter: one(users, {
    fields: [accounts.deletedBy],
    references: [users.id],
    relationName: 'deletedAccounts',
  }),
  defaultTax: one(taxes, {
    fields: [accounts.defaultTaxId],
    references: [taxes.id],
  }),
  parentAccount: one(accounts, {
    fields: [accounts.parentAccountId],
    references: [accounts.id],
    relationName: 'parentAccount',
  }),
  childAccounts: many(accounts, { relationName: 'parentAccount' }),
  incomeAccount: one(accounts, {
    fields: [accounts.incomeAccountId],
    references: [accounts.id],
    relationName: 'incomeAccount',
  }),
  expenseAccounts: many(accounts, { relationName: 'incomeAccount' }),
  transactions: many(transactions),
  journalEntryLines: many(journalEntryLines),
  // Asset account relationships
  assetsAsFixedAsset: many(assets, {
    relationName: 'assetFixedAssetAccount',
  }),
  assetsAsDepreciation: many(assets, {
    relationName: 'assetDepreciationAccount',
  }),
  assetsAsExpense: many(assets, { relationName: 'assetExpenseAccount' }),
  // Vehicle account relationships
  vehiclesAsFixedAsset: many(vehicles, {
    relationName: 'vehicleFixedAssetAccount',
  }),
  vehiclesAsDepreciation: many(vehicles, {
    relationName: 'vehicleDepreciationAccount',
  }),
  vehiclesAsExpense: many(vehicles, { relationName: 'vehicleExpenseAccount' }),
  // Service account relationships
  servicesAsIncome: many(services, { relationName: 'serviceIncomeAccount' }),
  servicesAsExpense: many(services, { relationName: 'serviceExpenseAccount' }),
  // Asset Maintenance account relationships
  assetMaintenancesAsExpense: many(assetMaintenances, {
    relationName: 'assetMaintenanceExpenseAccount',
  }),
  assetMaintenancesAsPayment: many(assetMaintenances, {
    relationName: 'assetMaintenancePaymentAccount',
  }),
  // Asset Repair Orders account relationships
  assetRepairOrdersAsExpense: many(assetRepairOrders, {
    relationName: 'assetRepairOrderExpenseAccount',
  }),
  assetRepairOrdersAsPayment: many(assetRepairOrders, {
    relationName: 'assetRepairOrderPaymentAccount',
  }),
  // Vehicle Fines account relationships
  vehicleFinesAsExpense: many(vehicleFines, {
    relationName: 'vehicleFineExpenseAccount',
  }),
  vehicleFinesAsPayment: many(vehicleFines, {
    relationName: 'vehicleFinePaymentAccount',
  }),
  // Rental Items account relationships
  rentalItemsAsFixedAsset: many(rentalItems, {
    relationName: 'rentalItemFixedAssetAccount',
  }),
  rentalItemsAsDepreciation: many(rentalItems, {
    relationName: 'rentalItemDepreciationAccount',
  }),
  rentalItemsAsExpense: many(rentalItems, {
    relationName: 'rentalItemExpenseAccount',
  }),
  // Restaurant Menu Items account relationships
  restaurantMenuItemsAsIncome: many(restaurantMenuItems, {
    relationName: 'restaurantMenuItemIncomeAccount',
  }),
  restaurantMenuItemsAsExpense: many(restaurantMenuItems, {
    relationName: 'restaurantMenuItemExpenseAccount',
  }),
}));

export const transactionsRelations = relations(
  transactions,
  ({ one, many }) => ({
    business: one(business, {
      fields: [transactions.businessId],
      references: [business.id],
    }),
    creator: one(users, {
      fields: [transactions.createdBy],
      references: [users.id],
      relationName: 'createdTransactions',
    }),
    updater: one(users, {
      fields: [transactions.updatedBy],
      references: [users.id],
      relationName: 'updatedTransactions',
    }),
    deleter: one(users, {
      fields: [transactions.deletedBy],
      references: [users.id],
      relationName: 'deletedTransactions',
    }),
    account: one(accounts, {
      fields: [transactions.accountId],
      references: [accounts.id],
    }),
    customer: one(customers, {
      fields: [transactions.customerId],
      references: [customers.id],
    }),
    supplier: one(suppliers, {
      fields: [transactions.supplierId],
      references: [suppliers.id],
    }),
    paymentAccount: one(paymentAccounts, {
      fields: [transactions.paymentAccountId],
      references: [paymentAccounts.id],
    }),
    relatedTransaction: one(transactions, {
      fields: [transactions.relatedTransactionId],
      references: [transactions.id],
      relationName: 'relatedTransaction',
    }),
    parentTransaction: one(transactions, {
      fields: [transactions.parentTransactionId],
      references: [transactions.id],
      relationName: 'parentTransaction',
    }),
    reconciledBy: one(users, {
      fields: [transactions.reconciledBy],
      references: [users.id],
      relationName: 'reconciledTransactions',
    }),
    reversedBy: one(users, {
      fields: [transactions.reversedBy],
      references: [users.id],
      relationName: 'reversedTransactions',
    }),
    tasks: many(tasks, { relationName: 'transactionTasks' }),
    transactionPayments: many(transactionPayments),
  }),
);

export const transactionPaymentsRelations = relations(
  transactionPayments,
  ({ one }) => ({
    business: one(business, {
      fields: [transactionPayments.businessId],
      references: [business.id],
    }),
    transaction: one(transactions, {
      fields: [transactionPayments.transactionId],
      references: [transactions.id],
    }),
    creator: one(users, {
      fields: [transactionPayments.createdBy],
      references: [users.id],
      relationName: 'createdTransactionPayments',
    }),
    updater: one(users, {
      fields: [transactionPayments.updatedBy],
      references: [users.id],
      relationName: 'updatedTransactionPayments',
    }),
    deleter: one(users, {
      fields: [transactionPayments.deletedBy],
      references: [users.id],
      relationName: 'deletedTransactionPayments',
    }),
  }),
);

export const journalEntriesRelations = relations(
  journalEntries,
  ({ one, many }) => ({
    business: one(business, {
      fields: [journalEntries.businessId],
      references: [business.id],
    }),
    creator: one(users, {
      fields: [journalEntries.createdBy],
      references: [users.id],
      relationName: 'createdJournalEntries',
    }),
    updater: one(users, {
      fields: [journalEntries.updatedBy],
      references: [users.id],
      relationName: 'updatedJournalEntries',
    }),
    deleter: one(users, {
      fields: [journalEntries.deletedBy],
      references: [users.id],
      relationName: 'deletedJournalEntries',
    }),
    journalEntryLines: many(journalEntryLines),
  }),
);

export const journalEntryLinesRelations = relations(
  journalEntryLines,
  ({ one }) => ({
    journalEntry: one(journalEntries, {
      fields: [journalEntryLines.journalId],
      references: [journalEntries.id],
    }),
    account: one(accounts, {
      fields: [journalEntryLines.accountId],
      references: [accounts.id],
    }),
    creator: one(users, {
      fields: [journalEntryLines.createdBy],
      references: [users.id],
      relationName: 'createdJournalEntryLines',
    }),
    updater: one(users, {
      fields: [journalEntryLines.updatedBy],
      references: [users.id],
      relationName: 'updatedJournalEntryLines',
    }),
    deleter: one(users, {
      fields: [journalEntryLines.deletedBy],
      references: [users.id],
      relationName: 'deletedJournalEntryLines',
    }),
  }),
);

// Estimates Relations
export const estimatesRelations = relations(estimates, ({ one, many }) => ({
  business: one(business, {
    fields: [estimates.businessId],
    references: [business.id],
  }),
  customer: one(customers, {
    fields: [estimates.customerId],
    references: [customers.id],
  }),
  creator: one(users, {
    fields: [estimates.createdBy],
    references: [users.id],
    relationName: 'createdEstimates',
  }),
  updater: one(users, {
    fields: [estimates.updatedBy],
    references: [users.id],
    relationName: 'updatedEstimates',
  }),
  deleter: one(users, {
    fields: [estimates.deletedBy],
    references: [users.id],
    relationName: 'deletedEstimates',
  }),
  lineItems: many(estimateLineItems),
  attachments: many(estimateAttachments),
}));

export const estimateLineItemsRelations = relations(
  estimateLineItems,
  ({ one }) => ({
    estimate: one(estimates, {
      fields: [estimateLineItems.estimateId],
      references: [estimates.id],
    }),
    productService: one(products, {
      fields: [estimateLineItems.productServiceId],
      references: [products.id],
    }),
    tax: one(taxes, {
      fields: [estimateLineItems.taxId],
      references: [taxes.id],
    }),
    creator: one(users, {
      fields: [estimateLineItems.createdBy],
      references: [users.id],
      relationName: 'createdEstimateLineItems',
    }),
    updater: one(users, {
      fields: [estimateLineItems.updatedBy],
      references: [users.id],
      relationName: 'updatedEstimateLineItems',
    }),
    deleter: one(users, {
      fields: [estimateLineItems.deletedBy],
      references: [users.id],
      relationName: 'deletedEstimateLineItems',
    }),
  }),
);

export const estimateAttachmentsRelations = relations(
  estimateAttachments,
  ({ one }) => ({
    estimate: one(estimates, {
      fields: [estimateAttachments.estimateId],
      references: [estimates.id],
    }),
    media: one(media, {
      fields: [estimateAttachments.mediaId],
      references: [media.id],
    }),
    creator: one(users, {
      fields: [estimateAttachments.createdBy],
      references: [users.id],
      relationName: 'createdEstimateAttachments',
    }),
    updater: one(users, {
      fields: [estimateAttachments.updatedBy],
      references: [users.id],
      relationName: 'updatedEstimateAttachments',
    }),
    deleter: one(users, {
      fields: [estimateAttachments.deletedBy],
      references: [users.id],
      relationName: 'deletedEstimateAttachments',
    }),
  }),
);

export {
  users,
  userAccounts,
  userTwoFactor,
  userRefreshTokens,
  userPasswordResetTokens,
  userEmailVerificationTokens,
  userStatusEnum,
  // accountTypeEnum,
  userRoleEnum,
  twoFactorMethodEnum,
  addresses,
  business,
  businessStatusEnum,
  businessTypeEnum,
  refreshTokens,
  moduleTypeEnum,
  businessModules,
  brands,
  brandStatusEnum,
  customerGroups,
  customerGroupStatusEnum,
  customers,
  customerLocations,
  warrantyTemplates,
  warrantyStatusEnum,
  durationTypeEnum,
  warrantyTypeEnum,
  coverageTypeEnum,
  categories,
  categoryLocations,
  categoryStatusEnum,
  rentalItemCategories,
  rentalItemCategoryLocations,
  rentalItemCategoryStatusEnum,
  rentalItems,
  rentalItemStatusEnum,
  equipmentTypes,
  equipmentTypeLocations,
  equipmentTypeStatusEnum,
  services,
  serviceStatusEnum,
  serviceStaff,
  serviceLocations,
  serviceCategories,
  serviceCategoryLocations,
  serviceCategoryStatusEnum,
  serviceOrderPriorities,
  serviceOrderStatuses,
  statusTypeEnum,
  serviceOrderTypes,
  serviceCategoryEnum,
  serviceOrderTypeStatusEnum,
  ServiceOrderStatusType,
  ServiceCategory,
  ServiceOrderTypeStatus,
  activityLog,
  activityLogNameEnum,
  departments,
  departmentStatusEnum,
  designations,
  designationStatusEnum,
  staffMembers,
  staffStatusEnum,
  employmentTypeEnum,
  attendance,
  attendanceStatusEnum,
  // Staff Family Details
  staffFamilyDetails,
  staffEmergencyContacts,
  staffPhysicalInfo,
  maritalStatusEnum,
  genderEnum,
  // Physical Health Enums
  bloodTypeEnum,
  visionConditionEnum,
  hearingConditionEnum,
  limbConditionEnum,
  staffInvitations,
  invitationStatusEnum,
  invitationDocumentStatusEnum,
  userInvitations,
  userInvitationStatusEnum,
  userInvitationDocumentStatusEnum,
  businessRoles,
  businessRoleStatusEnum,
  userBusinessRoles,
  userBusinessRoleStatusEnum,
  businessUsers,
  businessUserStatusEnum,
  businessUserRoleEnum,
  locations,
  locationTypeEnum,
  locationStatusEnum,
  userLocations,
  userLocationStatusEnum,
  variationTemplates,
  variationValueTemplates,
  taxes,
  taxStatusEnum,
  taxPeriodStartMonthEnum,
  taxFilingFrequencyEnum,
  taxApplicableOnEnum,
  taxGroupItems,
  taxRates,
  groupSubTaxes,
  products,
  productTypeEnum,
  taxTypeEnum,
  productStatusEnum,
  packages,
  packageStatusEnum,
  packageTypeEnum,
  serviceTimeSlots,
  serviceTimeSlotStaff,
  serviceTimeSlotServices,
  dayOfWeekEnum,
  scheduleTypeEnum,
  productRacks,
  variations,
  variationLocationDetails,
  customFields,
  customFieldTypeEnum,
  entityTypeEnum,
  paymentAccountTypes,
  paymentAccountTypeStatusEnum,
  paymentAccounts,
  paymentAccountStatusEnum,
  paymentMethods,
  paymentMethodStatusEnum,
  assets,
  assetStatusEnum,
  assetTransactions,
  assetTransactionTypeEnum,
  assetWarranties,
  assetMaintenances,
  maintenanceStatusEnum,
  maintenancePriorityEnum,
  assetCategories,
  assetCategoryStatusEnum,
  assetTypes,
  assetTypeStatusEnum,
  // Asset Repair Orders
  assetRepairOrders,
  repairStatusEnum,
  repairPriorityEnum,
  repairTypeEnum,
  suppliers,
  supplierStatusEnum,
  projects,
  projectStatusEnum,
  projectAttachments,
  notifications,
  vehicleTypes,
  vehicleTypeLocations,
  vehicleCategoryStatusEnum,
  reservationTypes,
  reservationTypeLocations,
  reservationTypeStatusEnum,
  accommodationUnits,
  bedTypeEnum,
  viewTypeEnum,
  roomStatusEnum,
  accommodationUnitStatusEnum,
  vehicles,
  vehicleStatusEnum,
  vehicleDepreciationDurationUnitEnum,
  assetDamage,
  vehicleBlockedPeriods,
  vehicleFines,
  vehicleFineTypeEnum,
  // Expenses
  expenses,
  expenseCategoryLineItems,
  expenseProductServiceLineItems,
  amountTypeEnum,
  expenseStatusEnum,
  payeeTypeEnum,
  AmountType,
  ExpenseStatus,
  PayeeType,
  // Vehicle Repair Orders
  vehicleRepairOrders,
  repairOrderStatusEnum,
  maintenanceTypeEnum,
  workshopTypeEnum,
  RepairOrderStatus,
  MaintenanceType,
  WorkshopType,
  smsTemplates,
  whatsappTemplates,
  emailTemplates,
  templateStatusEnum,
  templateCategoryEnum,
  templateTypeEnum,
  providers,
  providerStatusEnum,
  providerTypeEnum,
  providerNameEnum,
  bankAccounts,
  bankAccountTypeEnum,
  currencyCodeEnum,
  bankAccountEntityTypeEnum,
  accounts,
  accountCategoryEnum,
  chartAccountTypeEnum,
  userAccountTypeEnum,
  accountDetailTypeEnum,
  accountStatusEnum,
  // Tasks
  tasks,
  taskAttachments,
  taskStatusEnum,
  taskPriorityEnum,
  TaskStatus,
  TaskPriority,
  // Recurring Activities
  recurringActivities,
  recurringEntityTypeEnum,
  recurrencePatternEnum,
  recurrenceEndTypeEnum,
  recurringActivityStatusEnum,
  recurringDayOfWeekEnum,
  RecurringEntityType,
  RecurrencePattern,
  RecurrenceEndType,
  RecurringActivityStatus,
  // Leads
  leads,
  leadLocations,
  leadTypeEnum,
  leadStatusEnum,
  leadSourceEnum,
  leadPriorityEnum,
  // Newsletter Subscribers
  newsletterSubscribers,
  newsletterSubscriberStatusEnum,
  subscriptionSourceEnum,
  NewsletterSubscriberStatus,
  // Transactions
  transactions,
  financialTransactionTypeEnum,
  transactionStatusEnum,
  paymentMethodEnum,
  transactionCurrencyCodeEnum,
  transactionDirectionEnum,
  // Transaction Payments
  transactionPayments,
  transactionPaymentMethodEnum,
  transactionPaymentReferenceTypeEnum,
  // Journal Entries
  journalEntries,
  journalEntryLines,
  JournalEntryStatus,
  JournalReferenceType,
  EntityType,
  journalEntryStatusEnum,
  journalReferenceTypeEnum,
  journalEntityTypeEnum,
  // Estimates
  estimates,
  estimateLineItems,
  estimateAttachments,
  estimateStatusEnum,
  estimateCurrencyCodeEnum,
  estimateTaxTypeEnum,
  // Discount Plans
  discountPlans,
  discountPlanProducts,
  discountPlanServices,
  discountPlanProductCategories,
  discountPlanServiceCategories,
  discountPlanCustomerGroups,
  discountMethodEnum,
  discountTypeEnum,
  discountPlanStatusEnum,
  discountDayOfWeekEnum,
  timeTypeEnum,
  // Promo Codes
  promoCodes,
  promoCodeStatusEnum,
  DiscountType,
  PromoCodeStatus,
  // Referrals
  referrals,
  referralStatusEnum,
  // Games
  games,
  gameEnum,
  gameStatusEnum,
  // Game Participants
  gameParticipants,
  // Meetings
  meetings,
  meetingStatusEnum,
  meetingTypeEnum,
  // Demo Appointments
  demoAppointments,
  appointmentStatusEnum,
  // Working Hours
  workingHours,
  WeeklySchedule,
  WorkingHoursDay,
  WorkingHoursBreak,
  DEFAULT_WORKING_HOURS,
  // Media
  media,
  mediaArray,
  mediaTypeEnum,
  // Comments
  comments,
  commentStatusEnum,
};

// Games Relations
export const gamesRelations = relations(games, ({ one, many }) => ({
  business: one(business, {
    fields: [games.businessId],
    references: [business.id],
  }),
  promoCode: one(promoCodes, {
    fields: [games.promoCode],
    references: [promoCodes.id],
  }),
  gameParticipants: many(gameParticipants),
  creator: one(users, {
    fields: [games.createdBy],
    references: [users.id],
    relationName: 'createdGames',
  }),
  updater: one(users, {
    fields: [games.updatedBy],
    references: [users.id],
    relationName: 'updatedGames',
  }),
  deleter: one(users, {
    fields: [games.deletedBy],
    references: [users.id],
    relationName: 'deletedGames',
  }),
}));

// Game Participants Relations
export const gameParticipantsRelations = relations(
  gameParticipants,
  ({ one }) => ({
    business: one(business, {
      fields: [gameParticipants.businessId],
      references: [business.id],
    }),
    game: one(games, {
      fields: [gameParticipants.gameId],
      references: [games.id],
    }),
    creator: one(users, {
      fields: [gameParticipants.createdBy],
      references: [users.id],
      relationName: 'createdGameParticipants',
    }),
    updater: one(users, {
      fields: [gameParticipants.updatedBy],
      references: [users.id],
      relationName: 'updatedGameParticipants',
    }),
    deleter: one(users, {
      fields: [gameParticipants.deletedBy],
      references: [users.id],
      relationName: 'deletedGameParticipants',
    }),
  }),
);

// Meetings Relations
export const meetingsRelations = relations(meetings, ({ one }) => ({
  business: one(business, {
    fields: [meetings.businessId],
    references: [business.id],
  }),
  assignedAdmin: one(users, {
    fields: [meetings.assignedAdminId],
    references: [users.id],
    relationName: 'assignedMeetings',
  }),
  creator: one(users, {
    fields: [meetings.createdBy],
    references: [users.id],
    relationName: 'createdMeetings',
  }),
  updater: one(users, {
    fields: [meetings.updatedBy],
    references: [users.id],
    relationName: 'updatedMeetings',
  }),
  deleter: one(users, {
    fields: [meetings.deletedBy],
    references: [users.id],
    relationName: 'deletedMeetings',
  }),
}));

// Demo Appointments Relations
export const demoAppointmentsRelations = relations(
  demoAppointments,
  ({ one }) => ({
    business: one(business, {
      fields: [demoAppointments.businessId],
      references: [business.id],
    }),
    creator: one(users, {
      fields: [demoAppointments.createdBy],
      references: [users.id],
      relationName: 'createdDemoAppointments',
    }),
    updater: one(users, {
      fields: [demoAppointments.updatedBy],
      references: [users.id],
      relationName: 'updatedDemoAppointments',
    }),
    deleter: one(users, {
      fields: [demoAppointments.deletedBy],
      references: [users.id],
      relationName: 'deletedDemoAppointments',
    }),
    assignedTo: one(users, {
      fields: [demoAppointments.assignedTo],
      references: [users.id],
      relationName: 'assignedDemoAppointments',
    }),
  }),
);

// Discount Plans Relations
export const discountPlansRelations = relations(
  discountPlans,
  ({ one, many }) => ({
    business: one(business, {
      fields: [discountPlans.businessId],
      references: [business.id],
    }),
    creator: one(users, {
      fields: [discountPlans.createdBy],
      references: [users.id],
      relationName: 'createdDiscountPlans',
    }),
    updater: one(users, {
      fields: [discountPlans.updatedBy],
      references: [users.id],
      relationName: 'updatedDiscountPlans',
    }),
    deleter: one(users, {
      fields: [discountPlans.deletedBy],
      references: [users.id],
      relationName: 'deletedDiscountPlans',
    }),
    discountPlanProducts: many(discountPlanProducts),
    discountPlanServices: many(discountPlanServices),
    discountPlanProductCategories: many(discountPlanProductCategories),
    discountPlanServiceCategories: many(discountPlanServiceCategories),
    discountPlanCustomerGroups: many(discountPlanCustomerGroups),
    discountPlanCustomers: many(discountPlanCustomers),
  }),
);

export const discountPlanProductsRelations = relations(
  discountPlanProducts,
  ({ one }) => ({
    discountPlan: one(discountPlans, {
      fields: [discountPlanProducts.discountPlanId],
      references: [discountPlans.id],
    }),
    product: one(products, {
      fields: [discountPlanProducts.productId],
      references: [products.id],
    }),
    creator: one(users, {
      fields: [discountPlanProducts.createdBy],
      references: [users.id],
      relationName: 'createdDiscountPlanProducts',
    }),
    updater: one(users, {
      fields: [discountPlanProducts.updatedBy],
      references: [users.id],
      relationName: 'updatedDiscountPlanProducts',
    }),
    deleter: one(users, {
      fields: [discountPlanProducts.deletedBy],
      references: [users.id],
      relationName: 'deletedDiscountPlanProducts',
    }),
  }),
);

export const discountPlanServicesRelations = relations(
  discountPlanServices,
  ({ one }) => ({
    discountPlan: one(discountPlans, {
      fields: [discountPlanServices.discountPlanId],
      references: [discountPlans.id],
    }),
    service: one(services, {
      fields: [discountPlanServices.serviceId],
      references: [services.id],
    }),
    creator: one(users, {
      fields: [discountPlanServices.createdBy],
      references: [users.id],
      relationName: 'createdDiscountPlanServices',
    }),
    updater: one(users, {
      fields: [discountPlanServices.updatedBy],
      references: [users.id],
      relationName: 'updatedDiscountPlanServices',
    }),
    deleter: one(users, {
      fields: [discountPlanServices.deletedBy],
      references: [users.id],
      relationName: 'deletedDiscountPlanServices',
    }),
  }),
);

export const discountPlanProductCategoriesRelations = relations(
  discountPlanProductCategories,
  ({ one }) => ({
    discountPlan: one(discountPlans, {
      fields: [discountPlanProductCategories.discountPlanId],
      references: [discountPlans.id],
    }),
    category: one(categories, {
      fields: [discountPlanProductCategories.categoryId],
      references: [categories.id],
    }),
    creator: one(users, {
      fields: [discountPlanProductCategories.createdBy],
      references: [users.id],
      relationName: 'createdDiscountPlanProductCategories',
    }),
    updater: one(users, {
      fields: [discountPlanProductCategories.updatedBy],
      references: [users.id],
      relationName: 'updatedDiscountPlanProductCategories',
    }),
    deleter: one(users, {
      fields: [discountPlanProductCategories.deletedBy],
      references: [users.id],
      relationName: 'deletedDiscountPlanProductCategories',
    }),
  }),
);

export const discountPlanServiceCategoriesRelations = relations(
  discountPlanServiceCategories,
  ({ one }) => ({
    discountPlan: one(discountPlans, {
      fields: [discountPlanServiceCategories.discountPlanId],
      references: [discountPlans.id],
    }),
    serviceCategory: one(serviceCategories, {
      fields: [discountPlanServiceCategories.serviceCategoryId],
      references: [serviceCategories.id],
    }),
    creator: one(users, {
      fields: [discountPlanServiceCategories.createdBy],
      references: [users.id],
      relationName: 'createdDiscountPlanServiceCategories',
    }),
    updater: one(users, {
      fields: [discountPlanServiceCategories.updatedBy],
      references: [users.id],
      relationName: 'updatedDiscountPlanServiceCategories',
    }),
    deleter: one(users, {
      fields: [discountPlanServiceCategories.deletedBy],
      references: [users.id],
      relationName: 'deletedDiscountPlanServiceCategories',
    }),
  }),
);

export const discountPlanCustomerGroupsRelations = relations(
  discountPlanCustomerGroups,
  ({ one }) => ({
    discountPlan: one(discountPlans, {
      fields: [discountPlanCustomerGroups.discountPlanId],
      references: [discountPlans.id],
    }),
    customerGroup: one(customerGroups, {
      fields: [discountPlanCustomerGroups.customerGroupId],
      references: [customerGroups.id],
    }),
    creator: one(users, {
      fields: [discountPlanCustomerGroups.createdBy],
      references: [users.id],
      relationName: 'createdDiscountPlanCustomerGroups',
    }),
    updater: one(users, {
      fields: [discountPlanCustomerGroups.updatedBy],
      references: [users.id],
      relationName: 'updatedDiscountPlanCustomerGroups',
    }),
    deleter: one(users, {
      fields: [discountPlanCustomerGroups.deletedBy],
      references: [users.id],
      relationName: 'deletedDiscountPlanCustomerGroups',
    }),
  }),
);

export const discountPlanCustomersRelations = relations(
  discountPlanCustomers,
  ({ one }) => ({
    discountPlan: one(discountPlans, {
      fields: [discountPlanCustomers.discountPlanId],
      references: [discountPlans.id],
    }),
    customer: one(customers, {
      fields: [discountPlanCustomers.customerId],
      references: [customers.id],
    }),
    creator: one(users, {
      fields: [discountPlanCustomers.createdBy],
      references: [users.id],
      relationName: 'createdDiscountPlanCustomers',
    }),
    updater: one(users, {
      fields: [discountPlanCustomers.updatedBy],
      references: [users.id],
      relationName: 'updatedDiscountPlanCustomers',
    }),
    deleter: one(users, {
      fields: [discountPlanCustomers.deletedBy],
      references: [users.id],
      relationName: 'deletedDiscountPlanCustomers',
    }),
  }),
);

// Promo Codes Relations
export const promoCodesRelations = relations(promoCodes, ({ one, many }) => ({
  business: one(business, {
    fields: [promoCodes.businessId],
    references: [business.id],
  }),
  creator: one(users, {
    fields: [promoCodes.createdBy],
    references: [users.id],
    relationName: 'createdPromoCodes',
  }),
  updater: one(users, {
    fields: [promoCodes.updatedBy],
    references: [users.id],
    relationName: 'updatedPromoCodes',
  }),
  deleter: one(users, {
    fields: [promoCodes.deletedBy],
    references: [users.id],
    relationName: 'deletedPromoCodes',
  }),
  games: many(games),
  referrals: many(referrals),
}));

// Referrals Relations
export const referralsRelations = relations(referrals, ({ one }) => ({
  business: one(business, {
    fields: [referrals.businessId],
    references: [business.id],
  }),
  promoCode: one(promoCodes, {
    fields: [referrals.promoCodeId],
    references: [promoCodes.id],
  }),
  staff: one(staffMembers, {
    fields: [referrals.staffId],
    references: [staffMembers.id],
  }),
  creator: one(users, {
    fields: [referrals.createdBy],
    references: [users.id],
    relationName: 'createdReferrals',
  }),
  updater: one(users, {
    fields: [referrals.updatedBy],
    references: [users.id],
    relationName: 'updatedReferrals',
  }),
  deleter: one(users, {
    fields: [referrals.deletedBy],
    references: [users.id],
    relationName: 'deletedReferrals',
  }),
}));

// Expenses Relations
export const expensesRelations = relations(expenses, ({ one, many }) => ({
  business: one(business, {
    fields: [expenses.businessId],
    references: [business.id],
  }),
  creator: one(users, {
    fields: [expenses.createdBy],
    references: [users.id],
    relationName: 'createdExpenses',
  }),
  updater: one(users, {
    fields: [expenses.updatedBy],
    references: [users.id],
    relationName: 'updatedExpenses',
  }),
  deleter: one(users, {
    fields: [expenses.deletedBy],
    references: [users.id],
    relationName: 'deletedExpenses',
  }),
  paymentAccount: one(accounts, {
    fields: [expenses.paymentAccountId],
    references: [accounts.id],
  }),
  paymentMethod: one(paymentMethods, {
    fields: [expenses.paymentMethodId],
    references: [paymentMethods.id],
  }),
  // Polymorphic payee relationships - these would be handled in application logic
  // due to Drizzle's limitations with polymorphic relations
  categoryLineItems: many(expenseCategoryLineItems),
  productServiceLineItems: many(expenseProductServiceLineItems),
}));

// Expense Category Line Items Relations
export const expenseCategoryLineItemsRelations = relations(
  expenseCategoryLineItems,
  ({ one }) => ({
    expense: one(expenses, {
      fields: [expenseCategoryLineItems.expenseId],
      references: [expenses.id],
    }),
    category: one(accounts, {
      fields: [expenseCategoryLineItems.categoryId],
      references: [accounts.id],
    }),
    tax: one(taxes, {
      fields: [expenseCategoryLineItems.taxId],
      references: [taxes.id],
    }),
    creator: one(users, {
      fields: [expenseCategoryLineItems.createdBy],
      references: [users.id],
      relationName: 'createdExpenseCategoryLineItems',
    }),
    updater: one(users, {
      fields: [expenseCategoryLineItems.updatedBy],
      references: [users.id],
      relationName: 'updatedExpenseCategoryLineItems',
    }),
    deleter: one(users, {
      fields: [expenseCategoryLineItems.deletedBy],
      references: [users.id],
      relationName: 'deletedExpenseCategoryLineItems',
    }),
  }),
);

// Expense Product Service Line Items Relations
export const expenseProductServiceLineItemsRelations = relations(
  expenseProductServiceLineItems,
  ({ one }) => ({
    expense: one(expenses, {
      fields: [expenseProductServiceLineItems.expenseId],
      references: [expenses.id],
    }),
    tax: one(taxes, {
      fields: [expenseProductServiceLineItems.taxId],
      references: [taxes.id],
    }),
    creator: one(users, {
      fields: [expenseProductServiceLineItems.createdBy],
      references: [users.id],
      relationName: 'createdExpenseProductServiceLineItems',
    }),
    updater: one(users, {
      fields: [expenseProductServiceLineItems.updatedBy],
      references: [users.id],
      relationName: 'updatedExpenseProductServiceLineItems',
    }),
    deleter: one(users, {
      fields: [expenseProductServiceLineItems.deletedBy],
      references: [users.id],
      relationName: 'deletedExpenseProductServiceLineItems',
    }),
    // Note: product/service relations would be handled in application logic
    // due to polymorphic nature
  }),
);

// Business Modules Relations
export const businessModulesRelations = relations(
  businessModules,
  ({ one }) => ({
    business: one(business, {
      fields: [businessModules.businessId],
      references: [business.id],
    }),
    creator: one(users, {
      fields: [businessModules.createdBy],
      references: [users.id],
      relationName: 'createdBusinessModules',
    }),
    updater: one(users, {
      fields: [businessModules.updatedBy],
      references: [users.id],
      relationName: 'updatedBusinessModules',
    }),
    deleter: one(users, {
      fields: [businessModules.deletedBy],
      references: [users.id],
      relationName: 'deletedBusinessModules',
    }),
  }),
);

// Business Reward Settings Relations
export const businessRewardSettingsRelations = relations(
  businessRewardSettings,
  ({ one }) => ({
    business: one(business, {
      fields: [businessRewardSettings.businessId],
      references: [business.id],
    }),
    creator: one(users, {
      fields: [businessRewardSettings.createdBy],
      references: [users.id],
      relationName: 'createdBusinessRewardSettings',
    }),
    updater: one(users, {
      fields: [businessRewardSettings.updatedBy],
      references: [users.id],
      relationName: 'updatedBusinessRewardSettings',
    }),
    deleter: one(users, {
      fields: [businessRewardSettings.deletedBy],
      references: [users.id],
      relationName: 'deletedBusinessRewardSettings',
    }),
  }),
);

// Working Hours Relations
export const workingHoursRelations = relations(workingHours, ({ one }) => ({
  business: one(business, {
    fields: [workingHours.businessId],
    references: [business.id],
  }),
  creator: one(users, {
    fields: [workingHours.createdBy],
    references: [users.id],
    relationName: 'createdWorkingHours',
  }),
  updater: one(users, {
    fields: [workingHours.updatedBy],
    references: [users.id],
    relationName: 'updatedWorkingHours',
  }),
  deleter: one(users, {
    fields: [workingHours.deletedBy],
    references: [users.id],
    relationName: 'deletedWorkingHours',
  }),
}));

// Business Accounting Settings Relations
export const businessAccountingSettingsRelations = relations(
  businessAccountingSettings,
  ({ one }) => ({
    business: one(business, {
      fields: [businessAccountingSettings.businessId],
      references: [business.id],
    }),
    creator: one(users, {
      fields: [businessAccountingSettings.createdBy],
      references: [users.id],
      relationName: 'createdBusinessAccountingSettings',
    }),
    updater: one(users, {
      fields: [businessAccountingSettings.updatedBy],
      references: [users.id],
      relationName: 'updatedBusinessAccountingSettings',
    }),
    deleter: one(users, {
      fields: [businessAccountingSettings.deletedBy],
      references: [users.id],
      relationName: 'deletedBusinessAccountingSettings',
    }),
  }),
);

// Export campaigns relations
export { campaignsRelations };

// Export units and related enums
export { units, unitStatusEnum, unitConversions };

// Export leave management schemas
export {
  leaveTypes,
  leaveTypeStatusEnum,
  leaveRequests,
  leaveRequestStatusEnum,
  leaveBalances,
  staffLeaveTypes,
};

// Export restaurant menu items schemas
export { restaurantMenuItems, restaurantMenuItemStatusEnum };

// Export repair orders schemas
export {
  repairOrders,
  repairStatusHistory,
  repairOrderPriorityEnum,
  customerApprovalStatusEnum,
  repairWarrantyStatusEnum,
};

// Export BOM (Bill of Materials) schemas
export {
  billOfMaterials,
  bomLines,
  bomTypeEnum,
  bomStatusEnum,
  componentTypeEnum,
};

// Units Relations
export const unitsRelations = relations(units, ({ one, many }) => ({
  business: one(business, {
    fields: [units.businessId],
    references: [business.id],
  }),
  creator: one(users, {
    fields: [units.createdBy],
    references: [users.id],
    relationName: 'createdUnits',
  }),
  updater: one(users, {
    fields: [units.updatedBy],
    references: [users.id],
    relationName: 'updatedUnits',
  }),
  deleter: one(users, {
    fields: [units.deletedBy],
    references: [users.id],
    relationName: 'deletedUnits',
  }),
  fromUnitConversions: many(unitConversions, {
    relationName: 'fromUnitConversions',
  }),
  toUnitConversions: many(unitConversions, {
    relationName: 'toUnitConversions',
  }),
}));

// Unit Conversions Relations
export const unitConversionsRelations = relations(
  unitConversions,
  ({ one }) => ({
    business: one(business, {
      fields: [unitConversions.businessId],
      references: [business.id],
    }),
    fromUnit: one(units, {
      fields: [unitConversions.fromUnitId],
      references: [units.id],
      relationName: 'fromUnitConversions',
    }),
    toUnit: one(units, {
      fields: [unitConversions.toUnitId],
      references: [units.id],
      relationName: 'toUnitConversions',
    }),
    creator: one(users, {
      fields: [unitConversions.createdBy],
      references: [users.id],
      relationName: 'createdUnitConversions',
    }),
    updater: one(users, {
      fields: [unitConversions.updatedBy],
      references: [users.id],
      relationName: 'updatedUnitConversions',
    }),
    deleter: one(users, {
      fields: [unitConversions.deletedBy],
      references: [users.id],
      relationName: 'deletedUnitConversions',
    }),
  }),
);

// Leave Types Relations
export const leaveTypesRelations = relations(leaveTypes, ({ one, many }) => ({
  business: one(business, {
    fields: [leaveTypes.businessId],
    references: [business.id],
  }),
  creator: one(users, {
    fields: [leaveTypes.createdBy],
    references: [users.id],
    relationName: 'createdLeaveTypes',
  }),
  updater: one(users, {
    fields: [leaveTypes.updatedBy],
    references: [users.id],
    relationName: 'updatedLeaveTypes',
  }),
  deleter: one(users, {
    fields: [leaveTypes.deletedBy],
    references: [users.id],
    relationName: 'deletedLeaveTypes',
  }),
  leaveRequests: many(leaveRequests),
  leaveBalances: many(leaveBalances),
  staffLeaveTypes: many(staffLeaveTypes, {
    relationName: 'leaveTypeStaffAssignments',
  }),
}));

// Leave Requests Relations
export const leaveRequestsRelations = relations(leaveRequests, ({ one }) => ({
  business: one(business, {
    fields: [leaveRequests.businessId],
    references: [business.id],
  }),
  employee: one(staffMembers, {
    fields: [leaveRequests.employeeId],
    references: [staffMembers.id],
    relationName: 'employeeLeaveRequests',
  }),
  leaveType: one(leaveTypes, {
    fields: [leaveRequests.leaveTypeId],
    references: [leaveTypes.id],
  }),
  approver: one(staffMembers, {
    fields: [leaveRequests.approvedBy],
    references: [staffMembers.id],
    relationName: 'approvedLeaveRequests',
  }),
  creator: one(users, {
    fields: [leaveRequests.createdBy],
    references: [users.id],
    relationName: 'createdLeaveRequests',
  }),
  updater: one(users, {
    fields: [leaveRequests.updatedBy],
    references: [users.id],
    relationName: 'updatedLeaveRequests',
  }),
  deleter: one(users, {
    fields: [leaveRequests.deletedBy],
    references: [users.id],
    relationName: 'deletedLeaveRequests',
  }),
}));

// Leave Balances Relations
export const leaveBalancesRelations = relations(leaveBalances, ({ one }) => ({
  business: one(business, {
    fields: [leaveBalances.businessId],
    references: [business.id],
  }),
  employee: one(staffMembers, {
    fields: [leaveBalances.employeeId],
    references: [staffMembers.id],
    relationName: 'employeeLeaveBalances',
  }),
  leaveType: one(leaveTypes, {
    fields: [leaveBalances.leaveTypeId],
    references: [leaveTypes.id],
  }),
  creator: one(users, {
    fields: [leaveBalances.createdBy],
    references: [users.id],
    relationName: 'createdLeaveBalances',
  }),
  updater: one(users, {
    fields: [leaveBalances.updatedBy],
    references: [users.id],
    relationName: 'updatedLeaveBalances',
  }),
  deleter: one(users, {
    fields: [leaveBalances.deletedBy],
    references: [users.id],
    relationName: 'deletedLeaveBalances',
  }),
}));

// Staff Leave Types Relations
export const staffLeaveTypesRelations = relations(
  staffLeaveTypes,
  ({ one }) => ({
    business: one(business, {
      fields: [staffLeaveTypes.businessId],
      references: [business.id],
    }),
    staff: one(staffMembers, {
      fields: [staffLeaveTypes.staffId],
      references: [staffMembers.id],
      relationName: 'staffLeaveTypeAssignments',
    }),
    leaveType: one(leaveTypes, {
      fields: [staffLeaveTypes.leaveTypeId],
      references: [leaveTypes.id],
      relationName: 'leaveTypeStaffAssignments',
    }),
    creator: one(users, {
      fields: [staffLeaveTypes.createdBy],
      references: [users.id],
      relationName: 'createdStaffLeaveTypes',
    }),
    updater: one(users, {
      fields: [staffLeaveTypes.updatedBy],
      references: [users.id],
      relationName: 'updatedStaffLeaveTypes',
    }),
    deleter: one(users, {
      fields: [staffLeaveTypes.deletedBy],
      references: [users.id],
      relationName: 'deletedStaffLeaveTypes',
    }),
  }),
);

// Allowance Types Relations
export const allowanceTypesRelations = relations(
  allowanceTypes,
  ({ one, many }) => ({
    business: one(business, {
      fields: [allowanceTypes.businessId],
      references: [business.id],
    }),
    taxCategory: one(taxes, {
      fields: [allowanceTypes.taxCategoryId],
      references: [taxes.id],
    }),
    employeeAllowances: many(employeeAllowances, {
      relationName: 'allowanceTypeEmployeeAllowances',
    }),
    creator: one(users, {
      fields: [allowanceTypes.createdBy],
      references: [users.id],
      relationName: 'createdAllowanceTypes',
    }),
    updater: one(users, {
      fields: [allowanceTypes.updatedBy],
      references: [users.id],
      relationName: 'updatedAllowanceTypes',
    }),
    deleter: one(users, {
      fields: [allowanceTypes.deletedBy],
      references: [users.id],
      relationName: 'deletedAllowanceTypes',
    }),
  }),
);

// Employee Allowances Relations
export const employeeAllowancesRelations = relations(
  employeeAllowances,
  ({ one }) => ({
    business: one(business, {
      fields: [employeeAllowances.businessId],
      references: [business.id],
    }),
    employee: one(staffMembers, {
      fields: [employeeAllowances.employeeId],
      references: [staffMembers.id],
      relationName: 'staffEmployeeAllowances',
    }),
    allowanceType: one(allowanceTypes, {
      fields: [employeeAllowances.allowanceTypeId],
      references: [allowanceTypes.id],
      relationName: 'allowanceTypeEmployeeAllowances',
    }),
    approver: one(staffMembers, {
      fields: [employeeAllowances.approvedBy],
      references: [staffMembers.id],
      relationName: 'approvedEmployeeAllowances',
    }),
    creator: one(users, {
      fields: [employeeAllowances.createdBy],
      references: [users.id],
      relationName: 'createdEmployeeAllowances',
    }),
    updater: one(users, {
      fields: [employeeAllowances.updatedBy],
      references: [users.id],
      relationName: 'updatedEmployeeAllowances',
    }),
    deleter: one(users, {
      fields: [employeeAllowances.deletedBy],
      references: [users.id],
      relationName: 'deletedEmployeeAllowances',
    }),
  }),
);

// Deduction Types Relations
export const deductionTypesRelations = relations(
  deductionTypes,
  ({ one, many }) => ({
    business: one(business, {
      fields: [deductionTypes.businessId],
      references: [business.id],
    }),
    employeeDeductions: many(employeeDeductions, {
      relationName: 'deductionTypeEmployeeDeductions',
    }),
    creator: one(users, {
      fields: [deductionTypes.createdBy],
      references: [users.id],
      relationName: 'createdDeductionTypes',
    }),
    updater: one(users, {
      fields: [deductionTypes.updatedBy],
      references: [users.id],
      relationName: 'updatedDeductionTypes',
    }),
    deleter: one(users, {
      fields: [deductionTypes.deletedBy],
      references: [users.id],
      relationName: 'deletedDeductionTypes',
    }),
  }),
);

// Employee Deductions Relations
export const employeeDeductionsRelations = relations(
  employeeDeductions,
  ({ one }) => ({
    business: one(business, {
      fields: [employeeDeductions.businessId],
      references: [business.id],
    }),
    employee: one(staffMembers, {
      fields: [employeeDeductions.employeeId],
      references: [staffMembers.id],
      relationName: 'staffEmployeeDeductions',
    }),
    deductionType: one(deductionTypes, {
      fields: [employeeDeductions.deductionTypeId],
      references: [deductionTypes.id],
      relationName: 'deductionTypeEmployeeDeductions',
    }),
    approver: one(staffMembers, {
      fields: [employeeDeductions.approvedBy],
      references: [staffMembers.id],
      relationName: 'approvedEmployeeDeductions',
    }),
    creator: one(users, {
      fields: [employeeDeductions.createdBy],
      references: [users.id],
      relationName: 'createdEmployeeDeductions',
    }),
    updater: one(users, {
      fields: [employeeDeductions.updatedBy],
      references: [users.id],
      relationName: 'updatedEmployeeDeductions',
    }),
    deleter: one(users, {
      fields: [employeeDeductions.deletedBy],
      references: [users.id],
      relationName: 'deletedEmployeeDeductions',
    }),
  }),
);

// Subscription Plans Relations
export const subscriptionPlansRelations = relations(
  subscriptionPlans,
  ({ one, many }) => ({
    business: one(business, {
      fields: [subscriptionPlans.businessId],
      references: [business.id],
    }),
    incomeAccount: one(accounts, {
      fields: [subscriptionPlans.incomeAccountId],
      references: [accounts.id],
      relationName: 'subscriptionPlanIncomeAccount',
    }),
    expenseAccount: one(accounts, {
      fields: [subscriptionPlans.expenseAccountId],
      references: [accounts.id],
      relationName: 'subscriptionPlanExpenseAccount',
    }),
    tax: one(taxes, {
      fields: [subscriptionPlans.taxId],
      references: [taxes.id],
      relationName: 'subscriptionPlanTax',
    }),
    creator: one(users, {
      fields: [subscriptionPlans.createdBy],
      references: [users.id],
      relationName: 'createdSubscriptionPlans',
    }),
    updater: one(users, {
      fields: [subscriptionPlans.updatedBy],
      references: [users.id],
      relationName: 'updatedSubscriptionPlans',
    }),
    deleter: one(users, {
      fields: [subscriptionPlans.deletedBy],
      references: [users.id],
      relationName: 'deletedSubscriptionPlans',
    }),
    // Many-to-many relations through junction tables
    subscriptionPlanProducts: many(subscriptionPlanProducts),
    subscriptionPlanServices: many(subscriptionPlanServices),
    subscriptionPlanCustomers: many(subscriptionPlanCustomers),
    subscriptionPlanCustomerGroups: many(subscriptionPlanCustomerGroups),
  }),
);

// Subscription Plan Products Relations
export const subscriptionPlanProductsRelations = relations(
  subscriptionPlanProducts,
  ({ one }) => ({
    subscriptionPlan: one(subscriptionPlans, {
      fields: [subscriptionPlanProducts.subscriptionPlanId],
      references: [subscriptionPlans.id],
    }),
    product: one(products, {
      fields: [subscriptionPlanProducts.productId],
      references: [products.id],
    }),
    variant: one(productVariants, {
      fields: [subscriptionPlanProducts.variantId],
      references: [productVariants.id],
    }),
    customUnit: one(units, {
      fields: [subscriptionPlanProducts.customUnitId],
      references: [units.id],
    }),
    creator: one(users, {
      fields: [subscriptionPlanProducts.createdBy],
      references: [users.id],
      relationName: 'createdSubscriptionPlanProducts',
    }),
    updater: one(users, {
      fields: [subscriptionPlanProducts.updatedBy],
      references: [users.id],
      relationName: 'updatedSubscriptionPlanProducts',
    }),
    deleter: one(users, {
      fields: [subscriptionPlanProducts.deletedBy],
      references: [users.id],
      relationName: 'deletedSubscriptionPlanProducts',
    }),
  }),
);

// Subscription Plan Services Relations
export const subscriptionPlanServicesRelations = relations(
  subscriptionPlanServices,
  ({ one }) => ({
    subscriptionPlan: one(subscriptionPlans, {
      fields: [subscriptionPlanServices.subscriptionPlanId],
      references: [subscriptionPlans.id],
    }),
    service: one(services, {
      fields: [subscriptionPlanServices.serviceId],
      references: [services.id],
    }),
    creator: one(users, {
      fields: [subscriptionPlanServices.createdBy],
      references: [users.id],
      relationName: 'createdSubscriptionPlanServices',
    }),
    updater: one(users, {
      fields: [subscriptionPlanServices.updatedBy],
      references: [users.id],
      relationName: 'updatedSubscriptionPlanServices',
    }),
    deleter: one(users, {
      fields: [subscriptionPlanServices.deletedBy],
      references: [users.id],
      relationName: 'deletedSubscriptionPlanServices',
    }),
  }),
);

// Subscription Plan Customers Relations
export const subscriptionPlanCustomersRelations = relations(
  subscriptionPlanCustomers,
  ({ one }) => ({
    subscriptionPlan: one(subscriptionPlans, {
      fields: [subscriptionPlanCustomers.subscriptionPlanId],
      references: [subscriptionPlans.id],
    }),
    customer: one(customers, {
      fields: [subscriptionPlanCustomers.customerId],
      references: [customers.id],
    }),
    creator: one(users, {
      fields: [subscriptionPlanCustomers.createdBy],
      references: [users.id],
      relationName: 'createdSubscriptionPlanCustomers',
    }),
    updater: one(users, {
      fields: [subscriptionPlanCustomers.updatedBy],
      references: [users.id],
      relationName: 'updatedSubscriptionPlanCustomers',
    }),
    deleter: one(users, {
      fields: [subscriptionPlanCustomers.deletedBy],
      references: [users.id],
      relationName: 'deletedSubscriptionPlanCustomers',
    }),
  }),
);

// Subscription Plan Customer Groups Relations
export const subscriptionPlanCustomerGroupsRelations = relations(
  subscriptionPlanCustomerGroups,
  ({ one }) => ({
    subscriptionPlan: one(subscriptionPlans, {
      fields: [subscriptionPlanCustomerGroups.subscriptionPlanId],
      references: [subscriptionPlans.id],
    }),
    customerGroup: one(customerGroups, {
      fields: [subscriptionPlanCustomerGroups.customerGroupId],
      references: [customerGroups.id],
    }),
    creator: one(users, {
      fields: [subscriptionPlanCustomerGroups.createdBy],
      references: [users.id],
      relationName: 'createdSubscriptionPlanCustomerGroups',
    }),
    updater: one(users, {
      fields: [subscriptionPlanCustomerGroups.updatedBy],
      references: [users.id],
      relationName: 'updatedSubscriptionPlanCustomerGroups',
    }),
    deleter: one(users, {
      fields: [subscriptionPlanCustomerGroups.deletedBy],
      references: [users.id],
      relationName: 'deletedSubscriptionPlanCustomerGroups',
    }),
  }),
);

// Business Amenities Relations
export const businessAmenitiesRelations = relations(
  businessAmenities,
  ({ one }) => ({
    business: one(business, {
      fields: [businessAmenities.businessId],
      references: [business.id],
    }),
    creator: one(users, {
      fields: [businessAmenities.createdBy],
      references: [users.id],
      relationName: 'createdBusinessAmenities',
    }),
    updater: one(users, {
      fields: [businessAmenities.updatedBy],
      references: [users.id],
      relationName: 'updatedBusinessAmenities',
    }),
    deleter: one(users, {
      fields: [businessAmenities.deletedBy],
      references: [users.id],
      relationName: 'deletedBusinessAmenities',
    }),
  }),
);

export const warrantiesRelations = relations(warranties, ({ one, many }) => ({
  business: one(business, {
    fields: [warranties.businessId],
    references: [business.id],
  }),
  customer: one(customers, {
    fields: [warranties.customerId],
    references: [customers.id],
  }),
  product: one(products, {
    fields: [warranties.productId],
    references: [products.id],
  }),
  productVariant: one(productVariants, {
    fields: [warranties.productVariantId],
    references: [productVariants.id],
  }),
  template: one(warrantyTemplates, {
    fields: [warranties.templateId],
    references: [warrantyTemplates.id],
  }),
  originalWarranty: one(warranties, {
    fields: [warranties.originalWarrantyId],
    references: [warranties.id],
    relationName: 'originalWarrantyRelation',
  }),
  transferredToCustomer: one(customers, {
    fields: [warranties.transferredToCustomerId],
    references: [customers.id],
    relationName: 'transferredWarranties',
  }),
  transferredWarranties: many(warranties, {
    relationName: 'originalWarrantyRelation',
  }),
  creator: one(users, {
    fields: [warranties.createdBy],
    references: [users.id],
    relationName: 'createdWarranties',
  }),
  updater: one(users, {
    fields: [warranties.updatedBy],
    references: [users.id],
    relationName: 'updatedWarranties',
  }),
  deleter: one(users, {
    fields: [warranties.deletedBy],
    references: [users.id],
    relationName: 'deletedWarranties',
  }),
}));

// Bill of Materials Relations
export const billOfMaterialsRelations = relations(
  billOfMaterials,
  ({ one, many }) => ({
    business: one(business, {
      fields: [billOfMaterials.businessId],
      references: [business.id],
    }),
    product: one(products, {
      fields: [billOfMaterials.productId],
      references: [products.id],
    }),
    recipe: one(recipes, {
      fields: [billOfMaterials.recipeId],
      references: [recipes.id],
    }),
    creator: one(users, {
      fields: [billOfMaterials.createdBy],
      references: [users.id],
      relationName: 'createdBillOfMaterials',
    }),

    updater: one(users, {
      fields: [billOfMaterials.updatedBy],
      references: [users.id],
      relationName: 'updatedBillOfMaterials',
    }),
    deleter: one(users, {
      fields: [billOfMaterials.deletedBy],
      references: [users.id],
      relationName: 'deletedBillOfMaterials',
    }),
    bomLines: many(bomLines),
  }),
);

// BOM Lines Relations
export const bomLinesRelations = relations(bomLines, ({ one }) => ({
  business: one(business, {
    fields: [bomLines.businessId],
    references: [business.id],
  }),
  billOfMaterials: one(billOfMaterials, {
    fields: [bomLines.bomId],
    references: [billOfMaterials.id],
  }),
  componentProduct: one(products, {
    fields: [bomLines.componentProductId],
    references: [products.id],
  }),
  componentVariant: one(productVariants, {
    fields: [bomLines.componentVariantId],
    references: [productVariants.id],
  }),
}));
