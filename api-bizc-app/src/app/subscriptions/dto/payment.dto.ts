import {
  IsString,
  IsOptional,
  IsEnum,
  IsDateString,
  IsNumber,
  IsBoolean,
  IsArray,
  Min,
} from 'class-validator';
import { PaymentStatus } from '../../shared/types/common.enum';
import {
  PaymentMethod,
  SubscriptionInvoiceType,
} from '../../drizzle/schema/subscription-payments.schema';

export class CreatePaymentDto {
  @IsString()
  subscriptionId: string;

  @IsString()
  invoiceNumber: string;

  @IsOptional()
  @IsEnum(SubscriptionInvoiceType)
  invoiceType?: SubscriptionInvoiceType;

  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  amount: number;

  @IsOptional()
  @IsString()
  currency?: string;

  @IsOptional()
  @IsEnum(PaymentMethod)
  paymentMethod?: PaymentMethod;

  @IsOptional()
  @IsString()
  transactionId?: string;

  @IsOptional()
  @IsString()
  referenceNumber?: string;

  @IsDateString()
  invoiceDate: string;

  @IsDateString()
  dueDate: string;

  @IsDateString()
  billingPeriodStart: string;

  @IsDateString()
  billingPeriodEnd: string;

  @IsOptional()
  @IsString()
  gatewayProvider?: string;

  @IsOptional()
  @IsString()
  gatewayTransactionId?: string;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  taxAmount?: number;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  processingFee?: number;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  discountAmount?: number;

  @IsOptional()
  @IsString()
  notes?: string;

  @IsOptional()
  @IsString()
  paymentInstructions?: string;
}

export class UpdatePaymentDto {
  @IsOptional()
  @IsEnum(PaymentStatus)
  status?: PaymentStatus;

  @IsOptional()
  @IsEnum(PaymentMethod)
  paymentMethod?: PaymentMethod;

  @IsOptional()
  @IsString()
  transactionId?: string;

  @IsOptional()
  @IsString()
  referenceNumber?: string;

  @IsOptional()
  @IsDateString()
  paidDate?: string;

  @IsOptional()
  @IsString()
  gatewayProvider?: string;

  @IsOptional()
  @IsString()
  gatewayTransactionId?: string;

  @IsOptional()
  @IsString()
  gatewayResponse?: string;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  taxAmount?: number;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  processingFee?: number;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  discountAmount?: number;

  @IsOptional()
  @IsString()
  notes?: string;

  @IsOptional()
  @IsString()
  internalNotes?: string;

  @IsOptional()
  @IsString()
  paymentInstructions?: string;
}

export class ProcessPaymentDto {
  @IsString()
  paymentId: string;

  @IsEnum(PaymentMethod)
  paymentMethod: PaymentMethod;

  @IsOptional()
  @IsString()
  transactionId?: string;

  @IsOptional()
  @IsString()
  referenceNumber?: string;

  @IsOptional()
  @IsString()
  gatewayProvider?: string;

  @IsOptional()
  @IsString()
  gatewayTransactionId?: string;

  @IsOptional()
  @IsString()
  gatewayResponse?: string;

  @IsOptional()
  @IsString()
  notes?: string;
}

export class RefundPaymentDto {
  @IsString()
  paymentId: string;

  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  refundAmount: number;

  @IsString()
  refundReason: string;

  @IsOptional()
  @IsString()
  notes?: string;
}

export class PaymentResponseDto {
  id: string;
  businessId: string;
  subscriptionId: string;
  invoiceNumber: string;
  invoiceType: SubscriptionInvoiceType;
  amount: string;
  currency: string;
  status: PaymentStatus;
  paymentMethod?: PaymentMethod;
  transactionId?: string;
  referenceNumber?: string;
  invoiceDate: string;
  dueDate: string;
  paidDate?: string;
  billingPeriodStart: string;
  billingPeriodEnd: string;
  gatewayProvider?: string;
  gatewayTransactionId?: string;
  taxAmount: string;
  processingFee: string;
  discountAmount: string;
  refundAmount: string;
  refundDate?: string;
  refundReason?: string;
  isOverdue: boolean;
  overdueDate?: string;
  lateFeeAmount: string;
  remindersSent: string[];
  lastReminderDate?: string;
  notes?: string;
  internalNotes?: string;
  paymentInstructions?: string;
  retryAttempts: string[];
  nextRetryDate?: string;
  createdAt: string;
  updatedAt: string;
}

export class PaymentListResponseDto {
  payments: PaymentResponseDto[];
  total: number;
  page: number;
  limit: number;
}

export class PaymentStatusUpdateDto {
  @IsEnum(PaymentStatus)
  status: PaymentStatus;

  @IsOptional()
  @IsString()
  reason?: string;

  @IsOptional()
  @IsDateString()
  paidDate?: string;
}

export class PaymentSummaryDto {
  totalAmount: string;
  paidAmount: string;
  pendingAmount: string;
  overdueAmount: string;
  refundedAmount: string;
  totalPayments: number;
  paidPayments: number;
  pendingPayments: number;
  overduePayments: number;
}
