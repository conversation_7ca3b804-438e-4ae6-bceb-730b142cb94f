import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { AccountStatus } from '../../drizzle/schema/accounts.schema';
import {
  AccountCategory,
  AccountDetailType,
  ChartAccountType,
} from '../../shared/types/account.enum';

export class ExpenseCategoryListDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Expense category ID',
  })
  expenseCategoryId: string;

  @ApiProperty({
    example: 'Office Supplies',
    description: 'Expense category name',
  })
  accountName: string;

  @ApiPropertyOptional({
    example: '5000',
    description: 'Account number',
    nullable: true,
  })
  accountNumber?: string;

  @ApiProperty({
    description: 'Account category',
    enum: AccountCategory,
    enumName: 'AccountCategory',
  })
  accountCategory: AccountCategory;

  @ApiProperty({
    description: 'Chart account type',
    enum: ChartAccountType,
    enumName: 'ChartAccountType',
  })
  accountType: ChartAccountType;

  @ApiProperty({
    description: 'Account detail type',
    enum: AccountDetailType,
    enumName: 'AccountDetailType',
  })
  accountDetailType: AccountDetailType;

  @ApiPropertyOptional({
    description: 'Parent expense category ID',
    nullable: true,
  })
  parentAccountId?: string;


  @ApiProperty({
    description: 'Opening balance for the account',
    example: '0.00',
  })
  openingBalance: string;

  @ApiPropertyOptional({
    description: 'Expense category description',
    nullable: true,
  })
  description?: string;

  @ApiProperty({
    description: 'Whether to use for billable expenses',
    default: false,
  })
  useForBillableExpenses: boolean;

  @ApiProperty({
    description: 'Whether this is a system account',
    default: false,
  })
  isSystemAccount: boolean;

  @ApiProperty({
    example: AccountStatus.ACTIVE,
    enum: AccountStatus,
    enumName: 'AccountStatus',
    description: 'Expense category status',
  })
  status: AccountStatus;
}
