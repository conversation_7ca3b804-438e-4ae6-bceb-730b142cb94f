import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { AccountStatus } from '../../drizzle/schema/accounts.schema';
import {
  AccountCategory,
  AccountDetailType,
  ChartAccountType,
} from '../../shared/types/account.enum';

export class ExpenseCategoryDto {
  @ApiProperty({ description: 'Unique identifier for the expense category' })
  expenseCategoryId: string;

  @ApiProperty({ description: 'Business ID the expense category belongs to' })
  businessId: string;

  @ApiProperty({ description: 'Expense category name' })
  accountName: string;

  @ApiProperty({
    description: 'Account number for the expense category',
    required: false,
    nullable: true,
  })
  accountNumber?: string;

  @ApiProperty({
    description: 'Account category',
    enum: AccountCategory,
    enumName: 'AccountCategory',
  })
  accountCategory: AccountCategory;

  @ApiProperty({
    description: 'Chart account type',
    enum: ChartAccountType,
    enumName: 'ChartAccountType',
  })
  accountType: ChartAccountType;

  @ApiProperty({
    description: 'Account detail type',
    enum: AccountDetailType,
    enumName: 'AccountDetailType',
  })
  accountDetailType: AccountDetailType;

  @ApiProperty({
    description: 'Parent expense category ID',
    required: false,
    nullable: true,
  })
  parentAccountId?: string;


  @ApiProperty({
    description: 'Opening balance for the account',
    example: '0.00',
  })
  openingBalance: string;

  @ApiProperty({
    description: 'Opening balance date',
    required: false,
    nullable: true,
  })
  openingBalanceDate?: string;

  @ApiProperty({
    description: 'Expense category description',
    required: false,
    nullable: true,
  })
  description?: string;

  @ApiProperty({
    description: 'Default tax ID',
    required: false,
    nullable: true,
  })
  defaultTaxId?: string;

  @ApiProperty({
    description: 'Whether to use for billable expenses',
    default: false,
  })
  useForBillableExpenses: boolean;

  @ApiProperty({
    description: 'Income account ID',
    required: false,
    nullable: true,
  })
  incomeAccountId?: string;

  @ApiProperty({
    description: 'Whether this is a system account',
    default: false,
  })
  isSystemAccount: boolean;

  @ApiProperty({
    example: AccountStatus.ACTIVE,
    enum: AccountStatus,
    enumName: 'AccountStatus',
    description: 'Expense category status',
  })
  status: AccountStatus;

  @ApiProperty({
    example: 'John Doe',
    description: 'Name of the user who created the expense category',
  })
  createdBy: string;

  @ApiProperty({
    example: 'Jane Smith',
    description: 'Name of the user who last updated the expense category',
    required: false,
    nullable: true,
  })
  updatedBy?: string;

  @ApiProperty({
    example: '2023-01-01T00:00:00Z',
    description: 'Creation timestamp',
  })
  createdAt: string;

  @ApiProperty({
    example: '2023-01-01T00:00:00Z',
    description: 'Last update timestamp',
  })
  updatedAt: string;
}
