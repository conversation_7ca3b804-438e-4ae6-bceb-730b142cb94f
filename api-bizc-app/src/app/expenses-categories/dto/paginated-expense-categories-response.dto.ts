import { ApiProperty } from '@nestjs/swagger';
import { ExpenseCategoryListDto } from './expense-category-list.dto';
import { PaginationMeta } from '../../shared/dto/pagination-meta.dto';

export class PaginatedExpenseCategoriesResponseDto {
  @ApiProperty({
    type: [ExpenseCategoryListDto],
    description: 'Array of expense categories',
  })
  data: ExpenseCategoryListDto[];

  @ApiProperty({
    type: PaginationMeta,
    description: 'Pagination metadata',
  })
  meta: PaginationMeta;
}
