import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
  IsUUID,
  IsBoolean,
  IsEnum,
  IsDecimal,
  IsDateString,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { AccountStatus } from '../../drizzle/schema/accounts.schema';
import {
  AccountCategory,
  AccountDetailType,
  ChartAccountType,
} from '../../shared/types/account.enum';

export class CreateExpenseCategoryDto {
  @ApiProperty({ description: 'Expense category name', maxLength: 191 })
  @IsNotEmpty()
  @IsString()
  @MaxLength(191)
  accountName: string;

  @ApiPropertyOptional({
    description: 'Account number for the expense category',
    maxLength: 191,
  })
  @IsOptional()
  @IsString()
  @MaxLength(191)
  accountNumber?: string;

  @ApiProperty({
    description: 'Account category',
    enum: AccountCategory,
    enumName: 'AccountCategory',
    default: AccountCategory.EXPENSES,
  })
  @IsEnum(AccountCategory, {
    message: 'Account category must be a valid account category',
  })
  accountCategory: AccountCategory;

  @ApiProperty({
    description: 'Chart account type',
    enum: ChartAccountType,
    enumName: 'ChartAccountType',
  })
  @IsEnum(ChartAccountType, {
    message: 'Account type must be a valid chart account type',
  })
  accountType: ChartAccountType;

  @ApiProperty({
    description: 'Account detail type',
    enum: AccountDetailType,
    enumName: 'AccountDetailType',
  })
  @IsEnum(AccountDetailType, {
    message: 'Account detail type must be a valid account detail type',
  })
  accountDetailType: AccountDetailType;

  @ApiPropertyOptional({
    description: 'Parent expense category ID',
  })
  @IsOptional()
  @IsUUID()
  parentAccountId?: string;


  @ApiPropertyOptional({
    description: 'Opening balance for the account',
    example: '0.00',
  })
  @IsOptional()
  @IsDecimal(
    { decimal_digits: '0,2' },
    { message: 'Opening balance must be a valid decimal' },
  )
  openingBalance?: string;

  @ApiPropertyOptional({
    description: 'Opening balance date',
  })
  @IsOptional()
  @IsDateString()
  openingBalanceDate?: string;

  @ApiPropertyOptional({
    description: 'Expense category description',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Default tax ID',
  })
  @IsOptional()
  @IsUUID()
  defaultTaxId?: string;

  @ApiPropertyOptional({
    description: 'Whether to use for billable expenses',
    default: false,
  })
  @IsBoolean({ message: 'Use for billable expenses must be a boolean' })
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  useForBillableExpenses?: boolean;

  @ApiPropertyOptional({
    description: 'Income account ID',
  })
  @IsOptional()
  @IsUUID()
  incomeAccountId?: string;

  @ApiPropertyOptional({
    enum: AccountStatus,
    enumName: 'AccountStatus',
    description: 'Expense category status',
    default: AccountStatus.ACTIVE,
  })
  @IsEnum(AccountStatus, {
    message: 'Status must be a valid account status',
  })
  @IsOptional()
  status?: AccountStatus;
}
