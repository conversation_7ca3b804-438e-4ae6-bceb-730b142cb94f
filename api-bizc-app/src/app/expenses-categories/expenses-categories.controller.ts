import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ExpenseCategoriesService } from './expenses-categories.service';
import { CreateExpenseCategoryDto } from './dto/create-expense-category.dto';
import { UpdateExpenseCategoryDto } from './dto/update-expense-category.dto';
import { ExpenseCategoryDto } from './dto/expense-category.dto';
import { ExpenseCategorySlimDto } from './dto/expense-category-slim.dto';
import { ExpenseCategoryIdResponseDto } from './dto/expense-category-id-response.dto';
import { BulkExpenseCategoryIdsResponseDto } from './dto/bulk-expense-category-ids-response.dto';
import { BulkCreateExpenseCategoryDto } from './dto/bulk-create-expense-category.dto';
import { DeleteExpenseCategoryResponseDto } from './dto/delete-expense-category-response.dto';
import { BulkDeleteExpenseCategoryDto } from './dto/bulk-delete-expense-category.dto';
import { BulkDeleteExpenseCategoryResponseDto } from './dto/bulk-delete-expense-category-response.dto';
import { PaginatedExpenseCategoriesResponseDto } from './dto/paginated-expense-categories-response.dto';
import { BulkUpdateExpenseCategoryStatusDto } from './dto/bulk-update-expense-category-status.dto';
import { BulkUpdateExpenseCategoryStatusResponseDto } from './dto/bulk-update-expense-category-status-response.dto';
import { ExpenseCategoryHierarchyDto } from './dto/expense-category-hierarchy.dto';
import {
  BulkUpdateExpenseCategoryHierarchyDto,
  BulkUpdateExpenseCategoryHierarchyResponseDto,
} from './dto/bulk-update-expense-category-hierarchy.dto';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { ExpenseCategoryNameAvailabilityResponseDto } from './dto/check-expense-category-name.dto';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { Permission } from '../shared/types/permission.enum';
import { AccountStatus } from '../drizzle/schema/accounts.schema';

@ApiTags('expenses-categories')
@Controller('expenses-categories')
@UseGuards(PermissionsGuard)
export class ExpenseCategoriesController {
  constructor(
    private readonly expenseCategoriesService: ExpenseCategoriesService,
  ) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.EXPENSE_CATEGORY_CREATE)
  @ApiOperation({ summary: 'Create a new expense category' })
  @ApiBody({
    description: 'Expense category creation data',
    type: CreateExpenseCategoryDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Expense category created successfully',
    type: ExpenseCategoryIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Expense category name already exists',
  })
  create(
    @Request() req,
    @Body() createExpenseCategoryDto: CreateExpenseCategoryDto,
  ): Promise<ExpenseCategoryIdResponseDto> {
    return this.expenseCategoriesService.createAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      createExpenseCategoryDto,
    );
  }

  @Post('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.EXPENSE_CATEGORY_CREATE)
  @ApiOperation({ summary: 'Create multiple expense categories in bulk' })
  @ApiBody({
    description: 'Bulk expense category creation data',
    type: BulkCreateExpenseCategoryDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Expense categories created successfully',
    type: BulkExpenseCategoryIdsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Expense category names already exist',
  })
  bulkCreate(
    @Request() req,
    @Body() bulkCreateExpenseCategoryDto: BulkCreateExpenseCategoryDto,
  ): Promise<BulkExpenseCategoryIdsResponseDto> {
    return this.expenseCategoriesService.bulkCreateAndReturnIds(
      req.user.id,
      req.user.activeBusinessId,
      bulkCreateExpenseCategoryDto.expenseCategories,
    );
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.EXPENSE_CATEGORY_READ)
  @ApiOperation({
    summary: 'Get all expense categories for the active business',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'from',
    description: 'Filter from date (YYYY-MM-DD format)',
    required: false,
    type: String,
    example: '2025-05-24',
  })
  @ApiQuery({
    name: 'to',
    description: 'Filter to date (YYYY-MM-DD format)',
    required: false,
    type: String,
    example: '2025-05-24',
  })
  @ApiQuery({
    name: 'accountName',
    description: 'Filter by expense category name or account number',
    required: false,
    type: String,
    example: 'Office Supplies',
  })
  @ApiQuery({
    name: 'accountNumber',
    description: 'Filter by account number',
    required: false,
    type: String,
    example: '5000',
  })
  @ApiQuery({
    name: 'status',
    description:
      'Filter by status (comma-separated for multiple values). Supports URL encoding: status=active%2Cinactive',
    required: false,
    type: String,
    example: 'active,inactive',
  })
  @ApiQuery({
    name: 'accountCategory',
    description:
      'Filter by account category (comma-separated for multiple values). Supports URL encoding: accountCategory=expenses%2Crevenue',
    required: false,
    type: String,
    example: 'expenses,revenue',
  })
  @ApiQuery({
    name: 'filters',
    description:
      'Advanced filters as JSON string with operator support. Supported operators: iLike (contains), notILike (does not contain), eq (is), ne (is not), isEmpty (is empty), isNotEmpty (is not empty)',
    required: false,
    type: String,
    example:
      '[{"id":"accountName","value":"Office Supplies","operator":"iLike","type":"text","rowId":"1"},{"id":"status","value":"active","operator":"eq","type":"select","rowId":"2"}]',
  })
  @ApiQuery({
    name: 'joinOperator',
    description: 'Join operator for advanced filters',
    required: false,
    type: String,
    enum: ['and', 'or'],
    example: 'and',
  })
  @ApiQuery({
    name: 'sort',
    description:
      'Sort configuration as JSON string. Supported fields: accountName, accountNumber, status, createdAt, updatedAt',
    required: false,
    type: String,
    example: '[{"id":"accountName","desc":false}]',
  })
  @ApiResponse({
    status: 200,
    description:
      "Returns all active expense categories for the user's active business with pagination (optimized with only essential fields)",
    type: PaginatedExpenseCategoriesResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async findAll(
    @Request() req,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('accountName') accountName?: string,
    @Query('accountNumber') accountNumber?: string,
    @Query('status') status?: string,
    @Query('accountCategory') accountCategory?: string,
    @Query('filters') filters?: string,
    @Query('joinOperator') joinOperator?: 'and' | 'or',
    @Query('sort') sort?: string,
  ): Promise<PaginatedExpenseCategoriesResponseDto> {
    const result = await this.expenseCategoriesService.findAllOptimized(
      req.user.id,
      req.user.activeBusinessId,
      page ? parseInt(page.toString()) : undefined,
      limit ? parseInt(limit.toString()) : undefined,
      from,
      to,
      accountName,
      accountNumber,
      status,
      accountCategory,
      filters,
      joinOperator,
      sort,
    );

    return {
      data: result.data,
      meta: {
        total: result.meta.total,
        page: result.meta.page,
        totalPages: result.meta.totalPages,
      },
    };
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.EXPENSE_CATEGORY_READ)
  @ApiOperation({
    summary:
      'Get all expense categories in slim format (id, name, status only)',
  })
  @ApiQuery({
    name: 'status',
    description: 'Filter by expense category status',
    required: false,
    enum: AccountStatus,
  })
  @ApiResponse({
    status: 200,
    description: 'Expense categories retrieved successfully',
    type: [ExpenseCategorySlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllSlim(
    @Request() req,
    @Query('status') status?: AccountStatus,
  ): Promise<ExpenseCategorySlimDto[]> {
    return this.expenseCategoriesService.findAllSlim(
      req.user.id,
      req.user.activeBusinessId,
      status,
    );
  }

  @Get('hierarchy')
  @ApiBearerAuth()
  @RequirePermissions(Permission.EXPENSE_CATEGORY_READ)
  @ApiOperation({
    summary: 'Get all expense categories in hierarchical format',
  })
  @ApiResponse({
    status: 200,
    description: 'Expense categories hierarchy retrieved successfully',
    type: [ExpenseCategoryHierarchyDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllHierarchy(@Request() req): Promise<ExpenseCategoryHierarchyDto[]> {
    return this.expenseCategoriesService.findAllHierarchy(
      req.user.id,
      req.user.activeBusinessId,
    );
  }

  @Get('check-name/:accountName')
  @ApiBearerAuth()
  @RequirePermissions(Permission.EXPENSE_CATEGORY_READ)
  @ApiOperation({
    summary: 'Check if an expense category name is available',
  })
  @ApiParam({
    name: 'accountName',
    description: 'Expense category name to check',
    type: String,
  })
  @ApiQuery({
    name: 'excludeId',
    description: 'Expense category ID to exclude from the check (for updates)',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Name availability checked successfully',
    type: ExpenseCategoryNameAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkNameAvailability(
    @Request() req,
    @Param('accountName') accountName: string,
    @Query('excludeId') excludeId?: string,
  ): Promise<ExpenseCategoryNameAvailabilityResponseDto> {
    return this.expenseCategoriesService.checkNameAvailability(
      req.user.id,
      req.user.activeBusinessId,
      accountName,
      excludeId,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.EXPENSE_CATEGORY_READ)
  @ApiOperation({ summary: 'Get an expense category by ID' })
  @ApiParam({
    name: 'id',
    description: 'Expense category ID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Expense category retrieved successfully',
    type: ExpenseCategoryDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Expense category not found',
  })
  findOne(
    @Request() req,
    @Param('id') id: string,
  ): Promise<ExpenseCategoryDto> {
    return this.expenseCategoriesService.findOne(
      req.user.id,
      req.user.activeBusinessId,
      id,
    );
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.EXPENSE_CATEGORY_UPDATE)
  @ApiOperation({ summary: 'Update an expense category' })
  @ApiParam({
    name: 'id',
    description: 'Expense category ID',
    type: String,
  })
  @ApiBody({
    description: 'Expense category update data',
    type: UpdateExpenseCategoryDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Expense category updated successfully',
    type: ExpenseCategoryIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Expense category not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Expense category name already exists',
  })
  update(
    @Request() req,
    @Param('id') id: string,
    @Body() updateExpenseCategoryDto: UpdateExpenseCategoryDto,
  ): Promise<ExpenseCategoryIdResponseDto> {
    return this.expenseCategoriesService.updateAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateExpenseCategoryDto,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.EXPENSE_CATEGORY_DELETE)
  @ApiOperation({ summary: 'Delete an expense category' })
  @ApiParam({
    name: 'id',
    description: 'Expense category ID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Expense category deleted successfully',
    type: DeleteExpenseCategoryResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Cannot delete system expense category',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Expense category not found',
  })
  remove(
    @Request() req,
    @Param('id') id: string,
  ): Promise<DeleteExpenseCategoryResponseDto> {
    return this.expenseCategoriesService.removeAndReturnDetails(
      req.user.id,
      req.user.activeBusinessId,
      id,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.EXPENSE_CATEGORY_DELETE)
  @ApiOperation({ summary: 'Bulk delete expense categories' })
  @ApiBody({
    description: 'Array of expense category IDs to delete',
    type: BulkDeleteExpenseCategoryDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Expense categories deleted successfully',
    type: BulkDeleteExpenseCategoryResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Cannot delete system expense categories',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - No expense categories found to delete',
  })
  bulkDelete(
    @Request() req,
    @Body() bulkDeleteExpenseCategoryDto: BulkDeleteExpenseCategoryDto,
  ): Promise<BulkDeleteExpenseCategoryResponseDto> {
    return this.expenseCategoriesService.bulkDelete(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteExpenseCategoryDto.expenseCategoryIds,
    );
  }

  @Patch('bulk/status')
  @ApiBearerAuth()
  @RequirePermissions(Permission.EXPENSE_CATEGORY_UPDATE)
  @ApiOperation({ summary: 'Bulk update expense category status' })
  @ApiBody({
    description: 'Bulk status update data',
    type: BulkUpdateExpenseCategoryStatusDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Expense categories status updated successfully',
    type: BulkUpdateExpenseCategoryStatusResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - No expense categories found to update',
  })
  bulkUpdateStatus(
    @Request() req,
    @Body()
    bulkUpdateExpenseCategoryStatusDto: BulkUpdateExpenseCategoryStatusDto,
  ): Promise<BulkUpdateExpenseCategoryStatusResponseDto> {
    return this.expenseCategoriesService.bulkUpdateStatus(
      req.user.id,
      req.user.activeBusinessId,
      bulkUpdateExpenseCategoryStatusDto.expenseCategoryIds,
      bulkUpdateExpenseCategoryStatusDto.status,
    );
  }

  @Patch('batch/hierarchy')
  @ApiBearerAuth()
  @RequirePermissions(Permission.EXPENSE_CATEGORY_UPDATE)
  @ApiOperation({ summary: 'Batch update expense category hierarchy' })
  @ApiBody({
    description: 'Array of expense category hierarchy updates',
    type: BulkUpdateExpenseCategoryHierarchyDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Expense category hierarchy has been successfully updated',
    type: BulkUpdateExpenseCategoryHierarchyResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or expense categories not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async updateHierarchy(
    @Request() req,
    @Body() bulkUpdateHierarchyDto: BulkUpdateExpenseCategoryHierarchyDto,
  ): Promise<BulkUpdateExpenseCategoryHierarchyResponseDto> {
    return this.expenseCategoriesService.bulkUpdateExpenseCategoryHierarchy(
      req.user.id,
      req.user.activeBusinessId,
      bulkUpdateHierarchyDto.updates,
    );
  }
}
