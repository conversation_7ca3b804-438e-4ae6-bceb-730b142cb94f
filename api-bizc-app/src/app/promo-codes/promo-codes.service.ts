import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreatePromoCodeDto } from './dto/create-promo-code.dto';
import { UpdatePromoCodeDto } from './dto/update-promo-code.dto';
import { PromoCodeDto } from './dto/promo-code.dto';
import { PromoCodeSlimDto } from './dto/promo-code-slim.dto';
import {
  promoCodes,
  PromoCodeStatus,
} from '../drizzle/schema/promo-codes.schema';
import { DiscountType } from '@app/shared/types/common.enum';
import {
  eq,
  and,
  isNull,
  ilike,
  sql,
  gte,
  lte,
  desc,
  asc,
  inArray,
  or,
} from 'drizzle-orm';
import { users } from '../drizzle/schema/users.schema';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { ActivityLogName } from '../shared/types';
import { UsersService } from '../users/users.service';

@Injectable()
export class PromoCodesService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly usersService: UsersService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createPromoCodeDto: CreatePromoCodeDto,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a promo code with the same code already exists for this business
      const existingCode = await this.db
        .select()
        .from(promoCodes)
        .where(
          and(
            eq(promoCodes.businessId, businessId),
            ilike(promoCodes.code, createPromoCodeDto.code),
            isNull(promoCodes.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (existingCode) {
        throw new ConflictException(
          `A promo code with the code '${createPromoCodeDto.code}' already exists for this business`,
        );
      }

      // Check if a promo code with the same name already exists for this business
      const existingName = await this.db
        .select()
        .from(promoCodes)
        .where(
          and(
            eq(promoCodes.businessId, businessId),
            ilike(promoCodes.name, createPromoCodeDto.name),
            isNull(promoCodes.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (existingName) {
        throw new ConflictException(
          `A promo code with the name '${createPromoCodeDto.name}' already exists for this business`,
        );
      }

      // Validate date range
      const startDate = new Date(createPromoCodeDto.startDate);
      const endDate = new Date(createPromoCodeDto.endDate);

      if (startDate >= endDate) {
        throw new BadRequestException('Start date must be before end date');
      }

      // Insert new promo code
      const [promoCode] = await this.db
        .insert(promoCodes)
        .values({
          businessId,
          code: createPromoCodeDto.code,
          name: createPromoCodeDto.name,
          description: createPromoCodeDto.description,
          discountType: createPromoCodeDto.discountType,
          discountValue: createPromoCodeDto.discountValue,
          maxDiscountAmount: createPromoCodeDto.maxDiscountAmount,
          startDate,
          endDate,
          status: createPromoCodeDto.status ?? PromoCodeStatus.ACTIVE,
          totalUsageLimit: createPromoCodeDto.totalUsageLimit,
          usagePerCustomer: createPromoCodeDto.usagePerCustomer,
          usagePerDay: createPromoCodeDto.usagePerDay,
          usagePerMonth: createPromoCodeDto.usagePerMonth,
          currentUsageCount: 0,
          createdBy: userId,
        })
        .returning();

      // Log the promo code creation activity
      await this.activityLogService.log(
        ActivityLogName.CREATE,
        `Promo code "${createPromoCodeDto.name}" was created`,
        { id: promoCode.id.toString(), type: 'promo-code' },
        { id: userId, type: 'user' },
        { promoCodeId: promoCode.id, businessId },
      );

      return {
        id: promoCode.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create promo code: ${error.message}`,
      );
    }
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
  ): Promise<{
    data: PromoCodeDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      isNull(promoCodes.deletedAt),
      eq(promoCodes.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(promoCodes.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        // Add 23:59:59 to include the entire day
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(promoCodes.createdAt, toDate));
      }
    }

    // Find all promo codes for the user's active business with pagination
    const result = await this.db
      .select()
      .from(promoCodes)
      .where(and(...whereConditions))
      .orderBy(desc(promoCodes.createdAt))
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(promoCodes)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Log the activity
    await this.activityLogService.log(
      ActivityLogName.VIEW,
      'Viewed all promo codes',
      { id: businessId, type: 'business' },
      { id: userId, type: 'user' },
    );

    return {
      data: await Promise.all(
        result.map((promoCode) => this.mapToPromoCodeDto(promoCode)),
      ),
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async checkNameAvailability(
    businessId: string | null,
    name: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if a promo code with the same name already exists for this business
    const existingPromoCode = await this.db
      .select()
      .from(promoCodes)
      .where(
        and(
          eq(promoCodes.businessId, businessId),
          ilike(promoCodes.name, name),
          isNull(promoCodes.deletedAt),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingPromoCode };
  }

  async checkCodeAvailability(
    businessId: string | null,
    code: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if a promo code with the same code already exists for this business
    const existingPromoCode = await this.db
      .select()
      .from(promoCodes)
      .where(
        and(
          eq(promoCodes.businessId, businessId),
          ilike(promoCodes.code, code),
          isNull(promoCodes.deletedAt),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingPromoCode };
  }

  async findOne(userId: string, id: string): Promise<PromoCodeDto> {
    // Get the promo code
    const promoCode = await this.db
      .select()
      .from(promoCodes)
      .where(and(eq(promoCodes.id, id), isNull(promoCodes.deletedAt)))
      .then((results) => results[0]);

    if (!promoCode) {
      throw new NotFoundException(`Promo code with ID ${id} not found`);
    }

    // Get user's activeBusinessId to verify permission
    const user = await this.db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (
      !user ||
      !user.activeBusinessId ||
      user.activeBusinessId !== promoCode.businessId
    ) {
      throw new UnauthorizedException('Access denied to this promo code');
    }

    // Log the activity
    await this.activityLogService.log(
      ActivityLogName.VIEW,
      `Viewed promo code "${promoCode.name}"`,
      { id: id, type: 'promo-code' },
      { id: userId, type: 'user' },
    );

    return await this.mapToPromoCodeDto(promoCode);
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updatePromoCodeDto: UpdatePromoCodeDto,
  ): Promise<PromoCodeDto> {
    // Get the promo code
    const existingPromoCode = await this.db
      .select()
      .from(promoCodes)
      .where(and(eq(promoCodes.id, id), isNull(promoCodes.deletedAt)))
      .then((results) => results[0]);

    if (!existingPromoCode) {
      throw new NotFoundException(`Promo code with ID ${id} not found`);
    }

    // Verify business ownership
    if (businessId !== existingPromoCode.businessId) {
      throw new UnauthorizedException(
        'Access denied to update this promo code',
      );
    }

    // Check for name conflict if name is being updated
    if (
      updatePromoCodeDto.name &&
      updatePromoCodeDto.name !== existingPromoCode.name
    ) {
      const nameConflict = await this.db
        .select()
        .from(promoCodes)
        .where(
          and(
            eq(promoCodes.businessId, businessId),
            ilike(promoCodes.name, updatePromoCodeDto.name),
            isNull(promoCodes.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (nameConflict) {
        throw new ConflictException(
          `A promo code with the name '${updatePromoCodeDto.name}' already exists for this business`,
        );
      }
    }

    // Check for code conflict if code is being updated
    if (
      updatePromoCodeDto.code &&
      updatePromoCodeDto.code !== existingPromoCode.code
    ) {
      const codeConflict = await this.db
        .select()
        .from(promoCodes)
        .where(
          and(
            eq(promoCodes.businessId, businessId),
            ilike(promoCodes.code, updatePromoCodeDto.code),
            isNull(promoCodes.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (codeConflict) {
        throw new ConflictException(
          `A promo code with the code '${updatePromoCodeDto.code}' already exists for this business`,
        );
      }
    }

    // Validate date range if dates are being updated
    if (updatePromoCodeDto.startDate || updatePromoCodeDto.endDate) {
      const startDate = updatePromoCodeDto.startDate
        ? new Date(updatePromoCodeDto.startDate)
        : existingPromoCode.startDate;
      const endDate = updatePromoCodeDto.endDate
        ? new Date(updatePromoCodeDto.endDate)
        : existingPromoCode.endDate;

      if (startDate >= endDate) {
        throw new BadRequestException('Start date must be before end date');
      }
    }

    try {
      // Update the promo code
      const [updatedPromoCode] = await this.db
        .update(promoCodes)
        .set({
          ...updatePromoCodeDto,
          startDate: updatePromoCodeDto.startDate
            ? new Date(updatePromoCodeDto.startDate)
            : undefined,
          endDate: updatePromoCodeDto.endDate
            ? new Date(updatePromoCodeDto.endDate)
            : undefined,
          updatedAt: new Date(),
          updatedBy: userId,
        })
        .where(eq(promoCodes.id, id))
        .returning();

      // Log the activity
      await this.activityLogService.log(
        ActivityLogName.UPDATE,
        `Promo code "${existingPromoCode.name}" was updated`,
        { id: id, type: 'promo-code' },
        { id: userId, type: 'user' },
        {
          promoCodeId: id,
          businessId,
          changes: updatePromoCodeDto,
          previousName: existingPromoCode.name,
        },
      );

      return await this.mapToPromoCodeDto(updatedPromoCode);
    } catch (error) {
      throw new BadRequestException(
        `Failed to update promo code: ${error.message}`,
      );
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<{ success: boolean; message: string }> {
    // Get the promo code
    const existingPromoCode = await this.db
      .select()
      .from(promoCodes)
      .where(and(eq(promoCodes.id, id), isNull(promoCodes.deletedAt)))
      .then((results) => results[0]);

    if (!existingPromoCode) {
      throw new NotFoundException(`Promo code with ID ${id} not found`);
    }

    if (businessId !== existingPromoCode.businessId) {
      throw new UnauthorizedException(
        'Access denied to delete this promo code',
      );
    }

    // Soft delete the promo code
    await this.db
      .update(promoCodes)
      .set({
        deletedBy: userId,
        deletedAt: new Date(),
        updatedAt: new Date(),
      })
      .where(eq(promoCodes.id, id));

    // Log the activity
    await this.activityLogService.log(
      ActivityLogName.DELETE,
      `Promo code "${existingPromoCode.name}" was deleted`,
      { id: id, type: 'promo-code' },
      { id: userId, type: 'user' },
      { promoCodeId: id, businessId },
    );

    return {
      success: true,
      message: `Promo code with ID ${id} has been deleted`,
    };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    promoCodeIds: string[],
  ): Promise<{
    deleted: number;
    message: string;
    deletedIds: string[];
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!promoCodeIds || promoCodeIds.length === 0) {
        throw new BadRequestException(
          'No promo code IDs provided for deletion',
        );
      }

      // Get all promo codes that exist and belong to the business
      const existingPromoCodes = await this.db
        .select({
          id: promoCodes.id,
          name: promoCodes.name,
          businessId: promoCodes.businessId,
        })
        .from(promoCodes)
        .where(
          and(
            inArray(promoCodes.id, promoCodeIds),
            eq(promoCodes.businessId, businessId),
            isNull(promoCodes.deletedAt),
          ),
        );

      if (existingPromoCodes.length === 0) {
        throw new NotFoundException('No valid promo codes found for deletion');
      }

      const deletedIds: string[] = [];
      const currentTime = new Date();

      // Use transaction to ensure all deletions succeed or fail together
      await this.db.transaction(async (tx) => {
        for (const promoCode of existingPromoCodes) {
          // Soft delete the promo code
          await tx
            .update(promoCodes)
            .set({
              deletedBy: userId,
              deletedAt: currentTime,
              updatedAt: currentTime,
            })
            .where(eq(promoCodes.id, promoCode.id));

          deletedIds.push(promoCode.id);

          // Log the activity for each deleted promo code
          await this.activityLogService.log(
            ActivityLogName.DELETE,
            `Promo code "${promoCode.name}" was deleted (bulk)`,
            { id: promoCode.id, type: 'promo-code' },
            { id: userId, type: 'user' },
            { promoCodeId: promoCode.id, businessId },
          );
        }
      });

      return {
        deleted: deletedIds.length,
        message: `Successfully deleted ${deletedIds.length} promo codes`,
        deletedIds,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk delete promo codes: ${error.message}`,
      );
    }
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<PromoCodeSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Find all promo codes with only essential fields
    const promoCodeResults = await this.db
      .select({
        id: promoCodes.id,
        code: promoCodes.code,
        name: promoCodes.name,
      })
      .from(promoCodes)
      .where(
        and(
          isNull(promoCodes.deletedAt),
          eq(promoCodes.status, PromoCodeStatus.ACTIVE),
          eq(promoCodes.businessId, businessId),
        ),
      )
      .orderBy(asc(promoCodes.name), asc(promoCodes.id));

    // Log the activity
    await this.activityLogService.log(
      ActivityLogName.VIEW,
      'Viewed promo code list (slim)',
      { id: businessId, type: 'business' },
      { id: userId, type: 'user' },
    );

    return promoCodeResults.map((promoCode) => ({
      id: promoCode.id.toString(),
      code: promoCode.code,
      name: promoCode.name,
    }));
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createPromoCodeDto: CreatePromoCodeDto,
  ): Promise<{ id: string }> {
    const promoCode = await this.create(userId, businessId, createPromoCodeDto);
    return { id: promoCode.id };
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updatePromoCodeDto: UpdatePromoCodeDto,
  ): Promise<{ id: string }> {
    await this.update(userId, businessId, id, updatePromoCodeDto);
    return { id };
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createPromoCodeDtos: CreatePromoCodeDto[],
  ): Promise<{ ids: string[]; count: number; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!createPromoCodeDtos || createPromoCodeDtos.length === 0) {
        throw new BadRequestException('No promo codes provided for creation');
      }

      const createdIds: string[] = [];

      // Use transaction to ensure all creations succeed or fail together
      await this.db.transaction(async (tx) => {
        for (const createPromoCodeDto of createPromoCodeDtos) {
          // Check for conflicts within the batch and existing data
          const existingCode = await tx
            .select()
            .from(promoCodes)
            .where(
              and(
                eq(promoCodes.businessId, businessId),
                ilike(promoCodes.code, createPromoCodeDto.code),
                isNull(promoCodes.deletedAt),
              ),
            )
            .then((results) => results[0]);

          if (existingCode) {
            throw new ConflictException(
              `A promo code with the code '${createPromoCodeDto.code}' already exists`,
            );
          }

          const existingName = await tx
            .select()
            .from(promoCodes)
            .where(
              and(
                eq(promoCodes.businessId, businessId),
                ilike(promoCodes.name, createPromoCodeDto.name),
                isNull(promoCodes.deletedAt),
              ),
            )
            .then((results) => results[0]);

          if (existingName) {
            throw new ConflictException(
              `A promo code with the name '${createPromoCodeDto.name}' already exists`,
            );
          }

          // Validate date range
          const startDate = new Date(createPromoCodeDto.startDate);
          const endDate = new Date(createPromoCodeDto.endDate);

          if (startDate >= endDate) {
            throw new BadRequestException(
              `Start date must be before end date for promo code '${createPromoCodeDto.name}'`,
            );
          }

          // Insert new promo code
          const [promoCode] = await tx
            .insert(promoCodes)
            .values({
              businessId,
              code: createPromoCodeDto.code,
              name: createPromoCodeDto.name,
              description: createPromoCodeDto.description,
              discountType: createPromoCodeDto.discountType,
              discountValue: createPromoCodeDto.discountValue,
              maxDiscountAmount: createPromoCodeDto.maxDiscountAmount,
              startDate,
              endDate,
              status: createPromoCodeDto.status ?? PromoCodeStatus.ACTIVE,
              totalUsageLimit: createPromoCodeDto.totalUsageLimit,
              usagePerCustomer: createPromoCodeDto.usagePerCustomer,
              usagePerDay: createPromoCodeDto.usagePerDay,
              usagePerMonth: createPromoCodeDto.usagePerMonth,
              currentUsageCount: 0,
              createdBy: userId,
            })
            .returning();

          createdIds.push(promoCode.id);

          // Log the activity for each created promo code
          await this.activityLogService.log(
            ActivityLogName.CREATE,
            `Promo code "${createPromoCodeDto.name}" was created (bulk)`,
            { id: promoCode.id, type: 'promo-code' },
            { id: userId, type: 'user' },
            { promoCodeId: promoCode.id, businessId },
          );
        }
      });

      return {
        ids: createdIds,
        count: createdIds.length,
        message: `Successfully created ${createdIds.length} promo codes`,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk create promo codes: ${error.message}`,
      );
    }
  }

  async findAllOptimized(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    name?: string,
    code?: string,
    status?: string,
    discountType?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: PromoCodeDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      isNull(promoCodes.deletedAt),
      eq(promoCodes.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(promoCodes.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(promoCodes.createdAt, toDate));
      }
    }

    // Add name filtering if provided (searches both name and code)
    if (name) {
      whereConditions.push(
        or(
          ilike(promoCodes.name, `%${name}%`),
          ilike(promoCodes.code, `%${name}%`),
        ),
      );
    }

    // Add code filtering if provided
    if (code) {
      whereConditions.push(ilike(promoCodes.code, `%${code}%`));
    }

    // Add status filtering if provided
    if (status) {
      // Decode URL-encoded commas and split by comma
      const decodedStatus = decodeURIComponent(status);
      const statusArray = decodedStatus
        .split(',')
        .map((s) => s.trim() as PromoCodeStatus);
      whereConditions.push(inArray(promoCodes.status, statusArray));
    }

    // Add discountType filtering if provided
    if (discountType) {
      // Decode URL-encoded commas and split by comma
      const decodedDiscountType = decodeURIComponent(discountType);
      const discountTypeArray = decodedDiscountType
        .split(',')
        .map((dt) => dt.trim() as DiscountType);
      whereConditions.push(inArray(promoCodes.discountType, discountTypeArray));
    }

    // Add advanced filters if provided
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        const filterConditions = [];

        for (const filter of parsedFilters) {
          const { id: fieldId, value, operator } = filter;

          if (fieldId === 'name') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(ilike(promoCodes.name, `%${value}%`));
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(promoCodes.name, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(eq(promoCodes.name, value));
                break;
              case 'ne':
                filterConditions.push(sql`${promoCodes.name} != ${value}`);
                break;
              case 'isEmpty':
                filterConditions.push(
                  sql`${promoCodes.name} IS NULL OR ${promoCodes.name} = ''`,
                );
                break;
              case 'isNotEmpty':
                filterConditions.push(
                  sql`${promoCodes.name} IS NOT NULL AND ${promoCodes.name} != ''`,
                );
                break;
            }
          } else if (fieldId === 'code') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(ilike(promoCodes.code, `%${value}%`));
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(promoCodes.code, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(eq(promoCodes.code, value));
                break;
              case 'ne':
                filterConditions.push(sql`${promoCodes.code} != ${value}`);
                break;
              case 'isEmpty':
                filterConditions.push(
                  sql`${promoCodes.code} IS NULL OR ${promoCodes.code} = ''`,
                );
                break;
              case 'isNotEmpty':
                filterConditions.push(
                  sql`${promoCodes.code} IS NOT NULL AND ${promoCodes.code} != ''`,
                );
                break;
            }
          } else if (fieldId === 'status') {
            switch (operator) {
              case 'eq':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    inArray(promoCodes.status, value as PromoCodeStatus[]),
                  );
                } else {
                  filterConditions.push(eq(promoCodes.status, value));
                }
                break;
              case 'ne':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    sql`${promoCodes.status} NOT IN (${value.map((s) => `'${s}'`).join(',')})`,
                  );
                } else {
                  filterConditions.push(sql`${promoCodes.status} != ${value}`);
                }
                break;
              case 'iLike': // Contains (for backward compatibility)
                if (typeof value === 'string') {
                  const decodedValue = decodeURIComponent(value);
                  const statusValues = decodedValue
                    .split(',')
                    .map((s) => s.trim() as PromoCodeStatus);
                  filterConditions.push(
                    inArray(promoCodes.status, statusValues),
                  );
                } else {
                  filterConditions.push(eq(promoCodes.status, value));
                }
                break;
            }
          } else if (fieldId === 'discountType') {
            switch (operator) {
              case 'eq':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    inArray(promoCodes.discountType, value as DiscountType[]),
                  );
                } else {
                  filterConditions.push(
                    eq(promoCodes.discountType, value as DiscountType),
                  );
                }
                break;
              case 'ne':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    sql`${promoCodes.discountType} NOT IN (${(value as DiscountType[]).map((dt) => `'${dt}'`).join(',')})`,
                  );
                } else {
                  filterConditions.push(
                    sql`${promoCodes.discountType} != ${value as DiscountType}`,
                  );
                }
                break;
              case 'iLike': // Contains (for backward compatibility)
                if (typeof value === 'string') {
                  const decodedValue = decodeURIComponent(value);
                  const discountTypeValues = decodedValue
                    .split(',')
                    .map((dt) => dt.trim() as DiscountType);
                  filterConditions.push(
                    inArray(promoCodes.discountType, discountTypeValues),
                  );
                } else {
                  filterConditions.push(
                    eq(promoCodes.discountType, value as DiscountType),
                  );
                }
                break;
            }
          } else if (fieldId === 'description') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(
                  ilike(promoCodes.description, `%${value}%`),
                );
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(promoCodes.description, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(eq(promoCodes.description, value));
                break;
              case 'ne':
                filterConditions.push(
                  sql`${promoCodes.description} != ${value}`,
                );
                break;
              case 'isEmpty':
                filterConditions.push(
                  sql`${promoCodes.description} IS NULL OR ${promoCodes.description} = ''`,
                );
                break;
              case 'isNotEmpty':
                filterConditions.push(
                  sql`${promoCodes.description} IS NOT NULL AND ${promoCodes.description} != ''`,
                );
                break;
            }
          }
        }

        if (filterConditions.length > 0) {
          if (joinOperator === 'or') {
            whereConditions.push(or(...filterConditions));
          } else {
            whereConditions.push(and(...filterConditions));
          }
        }
      } catch {
        // Invalid JSON, ignore filters
      }
    }

    // Build sort conditions
    // Default sort: created date descending, then by ID for consistent pagination
    let orderBy = [desc(promoCodes.createdAt), asc(promoCodes.id)];

    if (sort) {
      try {
        const parsedSort = JSON.parse(sort);
        if (parsedSort.length > 0) {
          const sortField = parsedSort[0];
          const isDesc = sortField.desc === true;

          switch (sortField.id) {
            case 'name':
              orderBy = [
                isDesc ? desc(promoCodes.name) : asc(promoCodes.name),
                asc(promoCodes.id), // Secondary sort for consistency
              ];
              break;
            case 'code':
              orderBy = [
                isDesc ? desc(promoCodes.code) : asc(promoCodes.code),
                asc(promoCodes.id), // Secondary sort for consistency
              ];
              break;
            case 'status':
              orderBy = [
                isDesc ? desc(promoCodes.status) : asc(promoCodes.status),
                asc(promoCodes.id), // Secondary sort for consistency
              ];
              break;
            case 'discountType':
              orderBy = [
                isDesc
                  ? desc(promoCodes.discountType)
                  : asc(promoCodes.discountType),
                asc(promoCodes.id), // Secondary sort for consistency
              ];
              break;
            case 'discountValue':
              orderBy = [
                isDesc
                  ? desc(promoCodes.discountValue)
                  : asc(promoCodes.discountValue),
                asc(promoCodes.id), // Secondary sort for consistency
              ];
              break;
            case 'startDate':
              orderBy = [
                isDesc ? desc(promoCodes.startDate) : asc(promoCodes.startDate),
                asc(promoCodes.id), // Secondary sort for consistency
              ];
              break;
            case 'endDate':
              orderBy = [
                isDesc ? desc(promoCodes.endDate) : asc(promoCodes.endDate),
                asc(promoCodes.id), // Secondary sort for consistency
              ];
              break;
            case 'currentUsageCount':
              orderBy = [
                isDesc
                  ? desc(promoCodes.currentUsageCount)
                  : asc(promoCodes.currentUsageCount),
                asc(promoCodes.id), // Secondary sort for consistency
              ];
              break;
            case 'createdAt':
              orderBy = [
                isDesc ? desc(promoCodes.createdAt) : asc(promoCodes.createdAt),
                asc(promoCodes.id), // Secondary sort for consistency
              ];
              break;
            case 'updatedAt':
              orderBy = [
                isDesc ? desc(promoCodes.updatedAt) : asc(promoCodes.updatedAt),
                asc(promoCodes.id), // Secondary sort for consistency
              ];
              break;
          }
        }
      } catch {
        // Invalid JSON, use default sort
      }
    }

    // Execute query
    const result = await this.db
      .select()
      .from(promoCodes)
      .where(and(...whereConditions))
      .orderBy(...orderBy)
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(promoCodes)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Log the activity
    await this.activityLogService.log(
      ActivityLogName.VIEW,
      'Viewed promo codes (optimized)',
      { id: businessId, type: 'business' },
      { id: userId, type: 'user' },
    );

    return {
      data: await Promise.all(
        result.map((promoCode) => this.mapToPromoCodeDto(promoCode)),
      ),
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  private async mapToPromoCodeDto(promoCode: any): Promise<PromoCodeDto> {
    // Get creator name
    const createdBy = await this.usersService.getUserName(promoCode.createdBy);

    // Get updater name if exists
    let updatedBy: string | undefined;
    if (promoCode.updatedBy) {
      updatedBy = await this.usersService.getUserName(promoCode.updatedBy);
    }

    return {
      id: promoCode.id.toString(),
      businessId: promoCode.businessId,
      code: promoCode.code,
      name: promoCode.name,
      description: promoCode.description,
      discountType: promoCode.discountType,
      discountValue: promoCode.discountValue,
      maxDiscountAmount: promoCode.maxDiscountAmount,
      startDate: promoCode.startDate,
      endDate: promoCode.endDate,
      status: promoCode.status,
      totalUsageLimit: promoCode.totalUsageLimit,
      usagePerCustomer: promoCode.usagePerCustomer,
      usagePerDay: promoCode.usagePerDay,
      usagePerMonth: promoCode.usagePerMonth,
      currentUsageCount: promoCode.currentUsageCount,
      createdBy,
      updatedBy,
      createdAt: promoCode.createdAt,
      updatedAt: promoCode.updatedAt,
    };
  }
}
