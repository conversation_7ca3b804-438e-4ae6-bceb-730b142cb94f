import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PromoCodeStatus } from '../../drizzle/schema/promo-codes.schema';
import { DiscountType } from '@app/shared/types/common.enum';

export class PromoCodeListDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Promo code ID',
  })
  id: string;

  @ApiProperty({ example: 'SAVE20', description: 'Promo code' })
  code: string;

  @ApiProperty({
    example: 'Save 20% on all items',
    description: 'Promo code name',
  })
  name: string;

  @ApiPropertyOptional({
    example: 'Special discount for new customers',
    description: 'Promo code description',
    nullable: true,
  })
  description?: string;

  @ApiProperty({
    example: DiscountType.PERCENTAGE,
    enum: DiscountType,
    enumName: 'DiscountType',
    description: 'Type of discount',
  })
  discountType: DiscountType;

  @ApiProperty({
    example: '20.00',
    description: 'Discount value',
  })
  discountValue: string;

  @ApiPropertyOptional({
    example: '100.00',
    description: 'Maximum discount amount',
    nullable: true,
  })
  maxDiscountAmount?: string;

  @ApiProperty({
    example: '2024-01-01T00:00:00Z',
    description: 'Start date',
  })
  startDate: Date;

  @ApiProperty({
    example: '2024-12-31T23:59:59Z',
    description: 'End date',
  })
  endDate: Date;

  @ApiProperty({
    example: PromoCodeStatus.ACTIVE,
    enum: PromoCodeStatus,
    enumName: 'PromoCodeStatus',
    description: 'Promo code status',
  })
  status: PromoCodeStatus;

  @ApiPropertyOptional({
    example: 100,
    description: 'Total usage limit',
    nullable: true,
  })
  totalUsageLimit?: number;

  @ApiPropertyOptional({
    example: 1,
    description: 'Usage per customer',
    nullable: true,
  })
  usagePerCustomer?: number;

  @ApiProperty({
    example: 5,
    description: 'Current usage count',
  })
  currentUsageCount: number;

  @ApiProperty({
    example: '2023-01-01T00:00:00Z',
    description: 'Creation timestamp',
  })
  createdAt: Date;

  @ApiPropertyOptional({
    example: '2023-01-02T00:00:00Z',
    description: 'Last update timestamp',
    nullable: true,
  })
  updatedAt?: Date;
}
