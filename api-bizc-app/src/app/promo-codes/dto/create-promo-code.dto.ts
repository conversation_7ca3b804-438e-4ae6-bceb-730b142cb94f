import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
  IsEnum,
  Min,
  IsDateString,
  IsDecimal,
  IsInt,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { PromoCodeStatus } from '../../drizzle/schema/promo-codes.schema';
import { DiscountType } from '@app/shared/types/common.enum';

export class CreatePromoCodeDto {
  @ApiProperty({ description: 'Promo code', maxLength: 191 })
  @IsNotEmpty()
  @IsString()
  @MaxLength(191)
  code: string;

  @ApiProperty({ description: 'Promo code name', maxLength: 191 })
  @IsNotEmpty()
  @IsString()
  @MaxLength(191)
  name: string;

  @ApiPropertyOptional({
    description: 'Promo code description',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    enum: DiscountType,
    enumName: 'DiscountType',
    description: 'Type of discount (percentage or fixed amount)',
  })
  @IsEnum(DiscountType, {
    message: 'Discount type must be either percentage or fixed_amount',
  })
  discountType: DiscountType;

  @ApiProperty({
    description: 'Discount value (percentage or fixed amount)',
    example: '10.00',
  })
  @IsNotEmpty()
  @IsDecimal(
    { decimal_digits: '0,2' },
    {
      message:
        'Discount value must be a valid decimal with up to 2 decimal places',
    },
  )
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value;
    }
    return value?.toString();
  })
  discountValue: string;

  @ApiPropertyOptional({
    description: 'Maximum discount amount (for percentage discounts)',
    example: '100.00',
  })
  @IsOptional()
  @IsDecimal(
    { decimal_digits: '0,2' },
    {
      message:
        'Max discount amount must be a valid decimal with up to 2 decimal places',
    },
  )
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value;
    }
    return value?.toString();
  })
  maxDiscountAmount?: string;

  @ApiProperty({
    description: 'Start date of the promo code validity',
    example: '2024-01-01T00:00:00Z',
  })
  @IsNotEmpty()
  @IsDateString({}, { message: 'Start date must be a valid ISO date string' })
  startDate: string;

  @ApiProperty({
    description: 'End date of the promo code validity',
    example: '2024-12-31T23:59:59Z',
  })
  @IsNotEmpty()
  @IsDateString({}, { message: 'End date must be a valid ISO date string' })
  endDate: string;

  @ApiPropertyOptional({
    enum: PromoCodeStatus,
    enumName: 'PromoCodeStatus',
    description: 'Promo code status',
    default: PromoCodeStatus.ACTIVE,
  })
  @IsEnum(PromoCodeStatus, {
    message: 'Status must be a valid promo code status',
  })
  @IsOptional()
  status?: PromoCodeStatus;

  @ApiPropertyOptional({
    description: 'Total usage limit for the promo code',
    example: 100,
    minimum: 1,
  })
  @IsOptional()
  @IsInt({ message: 'Total usage limit must be an integer' })
  @Min(1, { message: 'Total usage limit must be at least 1' })
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      const parsed = parseInt(value, 10);
      return isNaN(parsed) ? value : parsed;
    }
    return value;
  })
  totalUsageLimit?: number;

  @ApiPropertyOptional({
    description: 'Usage limit per customer',
    example: 1,
    minimum: 1,
  })
  @IsOptional()
  @IsInt({ message: 'Usage per customer must be an integer' })
  @Min(1, { message: 'Usage per customer must be at least 1' })
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      const parsed = parseInt(value, 10);
      return isNaN(parsed) ? value : parsed;
    }
    return value;
  })
  usagePerCustomer?: number;

  @ApiPropertyOptional({
    description: 'Usage limit per day',
    example: 10,
    minimum: 1,
  })
  @IsOptional()
  @IsInt({ message: 'Usage per day must be an integer' })
  @Min(1, { message: 'Usage per day must be at least 1' })
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      const parsed = parseInt(value, 10);
      return isNaN(parsed) ? value : parsed;
    }
    return value;
  })
  usagePerDay?: number;

  @ApiPropertyOptional({
    description: 'Usage limit per month',
    example: 50,
    minimum: 1,
  })
  @IsOptional()
  @IsInt({ message: 'Usage per month must be an integer' })
  @Min(1, { message: 'Usage per month must be at least 1' })
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      const parsed = parseInt(value, 10);
      return isNaN(parsed) ? value : parsed;
    }
    return value;
  })
  usagePerMonth?: number;
}
