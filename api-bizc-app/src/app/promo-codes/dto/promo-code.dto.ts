import { ApiProperty } from '@nestjs/swagger';
import { PromoCodeStatus } from '../../drizzle/schema/promo-codes.schema';
import { DiscountType } from '@app/shared/types/common.enum';

export class PromoCodeDto {
  @ApiProperty({ description: 'Unique identifier for the promo code' })
  id: string;

  @ApiProperty({ description: 'Business ID the promo code belongs to' })
  businessId: string;

  @ApiProperty({ description: 'Promo code' })
  code: string;

  @ApiProperty({ description: 'Promo code name' })
  name: string;

  @ApiProperty({
    description: 'Promo code description',
    required: false,
    nullable: true,
  })
  description?: string;

  @ApiProperty({
    example: DiscountType.PERCENTAGE,
    enum: DiscountType,
    enumName: 'DiscountType',
    description: 'Type of discount (percentage or fixed amount)',
  })
  discountType: DiscountType;

  @ApiProperty({
    example: '10.00',
    description: 'Discount value (percentage or fixed amount)',
  })
  discountValue: string;

  @ApiProperty({
    example: '100.00',
    description: 'Maximum discount amount (for percentage discounts)',
    required: false,
    nullable: true,
  })
  maxDiscountAmount?: string;

  @ApiProperty({
    example: '2024-01-01T00:00:00Z',
    description: 'Start date of the promo code validity',
  })
  startDate: Date;

  @ApiProperty({
    example: '2024-12-31T23:59:59Z',
    description: 'End date of the promo code validity',
  })
  endDate: Date;

  @ApiProperty({
    example: PromoCodeStatus.ACTIVE,
    enum: PromoCodeStatus,
    enumName: 'PromoCodeStatus',
    description: 'Promo code status',
  })
  status: PromoCodeStatus;

  @ApiProperty({
    example: 100,
    description: 'Total usage limit for the promo code',
    required: false,
    nullable: true,
  })
  totalUsageLimit?: number;

  @ApiProperty({
    example: 1,
    description: 'Usage limit per customer',
    required: false,
    nullable: true,
  })
  usagePerCustomer?: number;

  @ApiProperty({
    example: 10,
    description: 'Usage limit per day',
    required: false,
    nullable: true,
  })
  usagePerDay?: number;

  @ApiProperty({
    example: 50,
    description: 'Usage limit per month',
    required: false,
    nullable: true,
  })
  usagePerMonth?: number;

  @ApiProperty({
    example: 0,
    description: 'Current usage count of the promo code',
  })
  currentUsageCount: number;

  @ApiProperty({
    example: 'John Doe',
    description: 'Name of the user who created the promo code',
  })
  createdBy: string;

  @ApiProperty({
    example: 'Jane Smith',
    description: 'Name of the user who last updated the promo code',
    required: false,
    nullable: true,
  })
  updatedBy?: string;

  @ApiProperty({
    example: '2023-01-01T00:00:00Z',
    description: 'Creation timestamp',
  })
  createdAt: Date;

  @ApiProperty({
    example: '2023-01-02T00:00:00Z',
    description: 'Last update timestamp',
    required: false,
    nullable: true,
  })
  updatedAt?: Date;
}
