import { ApiProperty } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  MaxLength,
  IsEnum,
  IsDateString,
  IsDecimal,
  IsInt,
  Min,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { PromoCodeStatus } from '../../drizzle/schema/promo-codes.schema';
import { DiscountType } from '@app/shared/types/common.enum';

export class UpdatePromoCodeDto {
  @ApiProperty({
    description: 'Promo code',
    required: false,
    maxLength: 191,
  })
  @IsOptional()
  @IsString()
  @MaxLength(191)
  code?: string;

  @ApiProperty({
    description: 'Promo code name',
    required: false,
    maxLength: 191,
  })
  @IsOptional()
  @IsString()
  @MaxLength(191)
  name?: string;

  @ApiProperty({
    description: 'Promo code description',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    enum: DiscountType,
    enumName: 'DiscountType',
    description: 'Type of discount (percentage or fixed amount)',
    required: false,
  })
  @IsOptional()
  @IsEnum(DiscountType, {
    message: 'Discount type must be either percentage or fixed_amount',
  })
  discountType?: DiscountType;

  @ApiProperty({
    description: 'Discount value (percentage or fixed amount)',
    required: false,
    example: '10.00',
  })
  @IsOptional()
  @IsDecimal(
    { decimal_digits: '0,2' },
    {
      message:
        'Discount value must be a valid decimal with up to 2 decimal places',
    },
  )
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value;
    }
    return value?.toString();
  })
  discountValue?: string;

  @ApiProperty({
    description: 'Maximum discount amount (for percentage discounts)',
    required: false,
    example: '100.00',
  })
  @IsOptional()
  @IsDecimal(
    { decimal_digits: '0,2' },
    {
      message:
        'Max discount amount must be a valid decimal with up to 2 decimal places',
    },
  )
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value;
    }
    return value?.toString();
  })
  maxDiscountAmount?: string;

  @ApiProperty({
    description: 'Start date of the promo code validity',
    required: false,
    example: '2024-01-01T00:00:00Z',
  })
  @IsOptional()
  @IsDateString({}, { message: 'Start date must be a valid ISO date string' })
  startDate?: string;

  @ApiProperty({
    description: 'End date of the promo code validity',
    required: false,
    example: '2024-12-31T23:59:59Z',
  })
  @IsOptional()
  @IsDateString({}, { message: 'End date must be a valid ISO date string' })
  endDate?: string;

  @ApiProperty({
    description: 'Promo code status',
    required: false,
    enum: PromoCodeStatus,
    enumName: 'PromoCodeStatus',
  })
  @IsOptional()
  @IsEnum(PromoCodeStatus, {
    message: 'Status must be a valid promo code status',
  })
  status?: PromoCodeStatus;

  @ApiProperty({
    description: 'Total usage limit for the promo code',
    required: false,
    example: 100,
    minimum: 1,
  })
  @IsOptional()
  @IsInt({ message: 'Total usage limit must be an integer' })
  @Min(1, { message: 'Total usage limit must be at least 1' })
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      const parsed = parseInt(value, 10);
      return isNaN(parsed) ? value : parsed;
    }
    return value;
  })
  totalUsageLimit?: number;

  @ApiProperty({
    description: 'Usage limit per customer',
    required: false,
    example: 1,
    minimum: 1,
  })
  @IsOptional()
  @IsInt({ message: 'Usage per customer must be an integer' })
  @Min(1, { message: 'Usage per customer must be at least 1' })
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      const parsed = parseInt(value, 10);
      return isNaN(parsed) ? value : parsed;
    }
    return value;
  })
  usagePerCustomer?: number;

  @ApiProperty({
    description: 'Usage limit per day',
    required: false,
    example: 10,
    minimum: 1,
  })
  @IsOptional()
  @IsInt({ message: 'Usage per day must be an integer' })
  @Min(1, { message: 'Usage per day must be at least 1' })
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      const parsed = parseInt(value, 10);
      return isNaN(parsed) ? value : parsed;
    }
    return value;
  })
  usagePerDay?: number;

  @ApiProperty({
    description: 'Usage limit per month',
    required: false,
    example: 50,
    minimum: 1,
  })
  @IsOptional()
  @IsInt({ message: 'Usage per month must be an integer' })
  @Min(1, { message: 'Usage per month must be at least 1' })
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      const parsed = parseInt(value, 10);
      return isNaN(parsed) ? value : parsed;
    }
    return value;
  })
  usagePerMonth?: number;
}
