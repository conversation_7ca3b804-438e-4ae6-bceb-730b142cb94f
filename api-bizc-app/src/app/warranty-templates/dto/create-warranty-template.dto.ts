import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  IsOptional,
  IsEnum,
  IsInt,
  Min,
  ValidateIf,
  MaxLength,
  IsBoolean,
} from 'class-validator';
import { Transform } from 'class-transformer';
import {
  DurationType,
  WarrantyType,
  CoverageType,
} from '../../drizzle/schema/warranties.schema';
import { WarrantyStatus } from '../../shared/types';

export class CreateWarrantyTemplateDto {
  @ApiProperty({
    description: 'Template code for the warranty',
    example: 'ELEC-STD-12M',
    maxLength: 191,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(191)
  templateCode: string;

  @ApiProperty({
    description: 'Name of the warranty template',
    example: 'Standard Electronics Warranty',
    maxLength: 191,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(191)
  templateName: string;

  @ApiProperty({
    description: 'Type of warranty',
    example: WarrantyType.MANUFACTURER,
    enum: WarrantyType,
    enumName: 'WarrantyType',
  })
  @IsNotEmpty()
  @IsEnum(WarrantyType, {
    message: 'Warranty type must be a valid warranty type',
  })
  warrantyType: WarrantyType;

  @ApiPropertyOptional({
    description:
      'Duration value of the warranty (not required for lifetime warranties)',
    example: 12,
    minimum: 1,
  })
  @ValidateIf((o) => o.durationType !== DurationType.LIFETIME)
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      const parsed = parseInt(value, 10);
      return isNaN(parsed) ? value : parsed;
    }
    return value;
  })
  duration?: number;

  @ApiProperty({
    description: 'Duration type of the warranty',
    example: DurationType.MONTHS,
    enum: DurationType,
    enumName: 'DurationType',
  })
  @IsNotEmpty()
  @IsEnum(DurationType, {
    message: 'Duration type must be a valid duration type',
  })
  durationType: DurationType;

  @ApiProperty({
    description: 'Type of coverage',
    example: CoverageType.FULL,
    enum: CoverageType,
    enumName: 'CoverageType',
  })
  @IsNotEmpty()
  @IsEnum(CoverageType, {
    message: 'Coverage type must be a valid coverage type',
  })
  coverageType: CoverageType;

  @ApiPropertyOptional({
    description: 'Details of what is covered',
    example: 'Covers all manufacturing defects and parts replacement',
  })
  @IsOptional()
  @IsString()
  coverageDetails?: string;

  @ApiPropertyOptional({
    description: 'Maximum number of claims allowed',
    example: 3,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  maxClaims?: number;

  @ApiPropertyOptional({
    description: 'Terms and conditions',
    example: 'Warranty void if tampered with or damaged by user',
  })
  @IsOptional()
  @IsString()
  termsConditions?: string;

  @ApiPropertyOptional({
    description: 'Whether warranty is transferable',
    example: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  isTransferable?: boolean;

  @ApiPropertyOptional({
    description: 'Whether to auto-apply this warranty',
    example: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  autoApply?: boolean;

  @ApiPropertyOptional({
    description: 'Applicable categories (comma-separated)',
    example: 'electronics,mobile,laptop',
  })
  @IsOptional()
  @IsString()
  applicableCategories?: string;

  @ApiPropertyOptional({
    description: 'Whether the warranty template is active',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiPropertyOptional({
    enum: WarrantyStatus,
    enumName: 'WarrantyStatus',
    description: 'Warranty status',
    default: WarrantyStatus.ACTIVE,
  })
  @IsEnum(WarrantyStatus, {
    message: 'Status must be a valid warranty status',
  })
  @IsOptional()
  status?: WarrantyStatus;
}
