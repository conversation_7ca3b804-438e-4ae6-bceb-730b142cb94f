import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  DurationType,
  WarrantyType,
  CoverageType,
} from '../../drizzle/schema/warranties.schema';
import { WarrantyStatus } from '../../shared/types';

export class WarrantyTemplateDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Warranty ID',
  })
  id: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440001',
    description: 'Business ID',
  })
  businessId: string;

  @ApiProperty({
    example: 'ELEC-STD-12M',
    description: 'Template code of the warranty',
  })
  templateCode: string;

  @ApiProperty({
    example: 'Standard Electronics Warranty',
    description: 'Name of the warranty template',
  })
  templateName: string;

  @ApiProperty({
    example: WarrantyType.MANUFACTURER,
    enum: WarrantyType,
    enumName: 'WarrantyType',
    description: 'Type of warranty',
  })
  warrantyType: WarrantyType;

  @ApiPropertyOptional({
    example: 12,
    description:
      'Duration value of the warranty (null for lifetime warranties)',
    nullable: true,
  })
  duration?: number;

  @ApiProperty({
    example: DurationType.MONTHS,
    enum: DurationType,
    enumName: 'DurationType',
    description: 'Duration type of the warranty',
  })
  durationType: DurationType;

  @ApiProperty({
    example: CoverageType.FULL,
    enum: CoverageType,
    enumName: 'CoverageType',
    description: 'Type of coverage',
  })
  coverageType: CoverageType;

  @ApiPropertyOptional({
    example: 'Covers all manufacturing defects and parts replacement',
    description: 'Details of what is covered',
    nullable: true,
  })
  coverageDetails?: string;

  @ApiPropertyOptional({
    example: 3,
    description: 'Maximum number of claims allowed',
    nullable: true,
  })
  maxClaims?: number;

  @ApiPropertyOptional({
    example: 'Warranty void if tampered with or damaged by user',
    description: 'Terms and conditions',
    nullable: true,
  })
  termsConditions?: string;

  @ApiProperty({
    example: false,
    description: 'Whether warranty is transferable',
  })
  isTransferable: boolean;

  @ApiProperty({
    example: false,
    description: 'Whether to auto-apply this warranty',
  })
  autoApply: boolean;

  @ApiPropertyOptional({
    example: 'electronics,mobile,laptop',
    description: 'Applicable categories (comma-separated)',
    nullable: true,
  })
  applicableCategories?: string;

  @ApiProperty({
    example: WarrantyStatus.ACTIVE,
    enum: WarrantyStatus,
    enumName: 'WarrantyStatus',
    description: 'Warranty status',
  })
  status: WarrantyStatus;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440004',
    description: 'Created by user ID',
  })
  createdBy: string;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-446655440005',
    description: 'Updated by user ID',
    nullable: true,
  })
  updatedBy?: string;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-446655440006',
    description: 'Deleted by user ID',
    nullable: true,
  })
  deletedBy?: string;

  @ApiPropertyOptional({
    example: '2024-01-01T00:00:00.000Z',
    description: 'Deletion timestamp',
    nullable: true,
  })
  deletedAt?: Date;

  @ApiProperty({
    example: '2024-01-01T00:00:00.000Z',
    description: 'Creation timestamp',
  })
  createdAt: Date;

  @ApiProperty({
    example: '2024-01-01T00:00:00.000Z',
    description: 'Last update timestamp',
  })
  updatedAt: Date;

  @ApiPropertyOptional({
    example: 5,
    description:
      'Number of products and services associated with this warranty',
  })
  productsCount?: number;
}
