import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  IsEnum,
  IsBoolean,
  IsDateString,
} from 'class-validator';
import { PaymentMethod, PaymentStatus } from '../../shared/types/common.enum';

export enum RefundReason {
  CANCELLATION = 'CANCELLATION',
  OVERBOOKING = 'OVERBOOKING',
  SERVICE_ISSUE = 'SERVICE_ISSUE',
  GUEST_REQUEST = 'GUEST_REQUEST',
  FORCE_MAJEURE = 'FORCE_MAJEURE',
  DUPLICATE_PAYMENT = 'DUPLICATE_PAYMENT',
  OTHER = 'OTHER',
}

export class ProcessPaymentDto {
  @ApiProperty({
    description: 'Payment amount',
    example: '500.00',
  })
  @IsNotEmpty()
  @IsString()
  amount: string;

  @ApiProperty({
    description: 'Payment method',
    enum: PaymentMethod,
    example: PaymentMethod.CREDIT_CARD,
  })
  @IsEnum(PaymentMethod)
  @IsNotEmpty()
  paymentMethod: PaymentMethod;

  @ApiProperty({
    description: 'Payment reference/transaction ID',
    required: false,
    example: 'TXN_123456789',
  })
  @IsOptional()
  @IsString()
  paymentReference?: string;

  @ApiProperty({
    description: 'Payment description',
    required: false,
    example: 'Partial payment for accommodation',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Payment date (ISO string)',
    required: false,
    example: '2024-01-15T10:30:00.000Z',
  })
  @IsOptional()
  @IsDateString()
  paymentDate?: string;

  @ApiProperty({
    description: 'Currency code',
    required: false,
    default: 'USD',
    example: 'USD',
  })
  @IsOptional()
  @IsString()
  currency?: string;

  @ApiProperty({
    description: 'Whether to send payment confirmation',
    required: false,
    default: true,
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  sendConfirmation?: boolean;

  @ApiProperty({
    description: 'Additional payment metadata',
    required: false,
    example: { cardLast4: '1234', cardType: 'Visa' },
  })
  @IsOptional()
  metadata?: Record<string, any>;
}

export class ProcessRefundDto {
  @ApiProperty({
    description: 'Refund amount',
    example: '250.00',
  })
  @IsNotEmpty()
  @IsString()
  amount: string;

  @ApiProperty({
    description: 'Refund reason',
    enum: RefundReason,
    example: RefundReason.CANCELLATION,
  })
  @IsEnum(RefundReason)
  @IsNotEmpty()
  reason: RefundReason;

  @ApiProperty({
    description: 'Refund description',
    required: false,
    example: 'Refund due to reservation cancellation',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Original payment reference to refund',
    required: false,
    example: 'TXN_123456789',
  })
  @IsOptional()
  @IsString()
  originalPaymentReference?: string;

  @ApiProperty({
    description: 'Whether to send refund notification',
    required: false,
    default: true,
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  sendNotification?: boolean;

  @ApiProperty({
    description: 'Additional refund metadata',
    required: false,
    example: { processingFee: '5.00' },
  })
  @IsOptional()
  metadata?: Record<string, any>;
}

export class PaymentResultDto {
  @ApiProperty({
    description: 'Payment ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Payment amount',
    example: '500.00',
  })
  amount: string;

  @ApiProperty({
    description: 'Payment method',
    example: 'CREDIT_CARD',
  })
  paymentMethod: string;

  @ApiProperty({
    description: 'Payment status',
    example: 'COMPLETED',
  })
  status: string;

  @ApiProperty({
    description: 'Payment reference/transaction ID',
    example: 'TXN_123456789',
  })
  paymentReference?: string;

  @ApiProperty({
    description: 'Payment date',
    example: '2024-01-15T10:30:00.000Z',
  })
  paymentDate: string;

  @ApiProperty({
    description: 'Currency code',
    example: 'USD',
  })
  currency: string;
}

export class ProcessPaymentResponseDto {
  @ApiProperty({
    description: 'Reservation ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  reservationId: string;

  @ApiProperty({
    description: 'Reservation number',
    example: 'RES-2024-000001',
  })
  reservationNumber: string;

  @ApiProperty({
    description: 'Payment details',
    type: PaymentResultDto,
  })
  payment: PaymentResultDto;

  @ApiProperty({
    description: 'Updated reservation payment status',
    example: 'PARTIAL',
  })
  reservationPaymentStatus: string;

  @ApiProperty({
    description: 'Total amount paid so far',
    example: '750.00',
  })
  totalPaid: string;

  @ApiProperty({
    description: 'Remaining balance',
    example: '650.00',
  })
  remainingBalance: string;

  @ApiProperty({
    description: 'Whether payment confirmation was sent',
    example: true,
  })
  confirmationSent: boolean;

  @ApiProperty({
    description: 'Success message',
    example: 'Payment processed successfully',
  })
  message: string;
}

export class RefundResultDto {
  @ApiProperty({
    description: 'Refund ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Refund amount',
    example: '250.00',
  })
  amount: string;

  @ApiProperty({
    description: 'Refund reason',
    example: 'CANCELLATION',
  })
  reason: string;

  @ApiProperty({
    description: 'Refund status',
    example: 'PROCESSED',
  })
  status: string;

  @ApiProperty({
    description: 'Refund reference/transaction ID',
    example: 'REF_123456789',
  })
  refundReference?: string;

  @ApiProperty({
    description: 'Refund date',
    example: '2024-01-15T10:30:00.000Z',
  })
  refundDate: string;

  @ApiProperty({
    description: 'Expected processing time in business days',
    example: 3,
  })
  expectedProcessingDays: number;
}

export class ProcessRefundResponseDto {
  @ApiProperty({
    description: 'Reservation ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  reservationId: string;

  @ApiProperty({
    description: 'Reservation number',
    example: 'RES-2024-000001',
  })
  reservationNumber: string;

  @ApiProperty({
    description: 'Refund details',
    type: RefundResultDto,
  })
  refund: RefundResultDto;

  @ApiProperty({
    description: 'Updated reservation payment status',
    example: 'REFUNDED',
  })
  reservationPaymentStatus: string;

  @ApiProperty({
    description: 'Total amount refunded',
    example: '250.00',
  })
  totalRefunded: string;

  @ApiProperty({
    description: 'Whether refund notification was sent',
    example: true,
  })
  notificationSent: boolean;

  @ApiProperty({
    description: 'Success message',
    example: 'Refund processed successfully',
  })
  message: string;
}
