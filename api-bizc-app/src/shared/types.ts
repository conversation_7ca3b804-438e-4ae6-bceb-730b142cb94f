export enum SetupStyle {
  THEATER = 'THEATER',
  CLASSROOM = 'CLASSROOM',
  BANQUET = 'BANQUET',
  COCKTAIL = 'COCKTAIL',
  BOARDROOM = 'BOARDROOM',
  U_SHAPE = 'U_SHAPE',
  HOLLOW_SQUARE = 'HOLLOW_SQUARE',
  CABARET = 'CABARET',
  CONFERENCE = 'CONFERENCE',
  RECEPTION = 'RECEPTION',
  CUSTOM = 'CUSTOM',
}

export enum EventSpaceStatus {
  AVAILABLE = 'AVAILABLE',
  BOOKED = 'BOOKED',
  TENTATIVE = 'TENTATIVE',
  SETUP = 'SETUP',
  IN_USE = 'IN_USE',
  BREAKDOWN = 'BREAKDOWN',
  MAINTENANCE = 'MAINTENANCE',
  BLOCKED = 'BLOCKED',
  OUT_OF_ORDER = 'OUT_OF_ORDER',
  INACTIVE = 'INACTIVE',
}

export interface EventSpaceFeatures {
  naturalLight?: boolean;
  blackoutCapability?: boolean;
  soundproof?: boolean;
  climateControl?: boolean;
  builtInAV?: boolean;
  livestreamCapability?: boolean;
  privateEntrance?: boolean;
  loadingDock?: boolean;
  outdoorAccess?: boolean;
  divisible?: boolean;
  combinable?: boolean;
  stage?: boolean;
  danceFloor?: boolean;
  bar?: boolean;
  kitchen?: boolean;
  greenRoom?: boolean;
  storageSpace?: boolean;
  privateBathrooms?: boolean;
  hasOutdoorSpace?: boolean;
  [key: string]: any;
}

export interface AccommodationUnitFeatures {
  // Room amenities
  wifi?: boolean;
  airConditioning?: boolean;
  heating?: boolean;
  television?: boolean;
  telephone?: boolean;
  minibar?: boolean;
  safe?: boolean;
  iron?: boolean;
  hairdryer?: boolean;
  bathrobes?: boolean;
  slippers?: boolean;

  // Bathroom features
  privateBathroom?: boolean;
  bathtub?: boolean;
  shower?: boolean;
  jacuzzi?: boolean;
  toiletries?: boolean;

  // Technology
  smartTV?: boolean;
  streaming?: boolean;
  usbPorts?: boolean;
  workDesk?: boolean;
  wifi6?: boolean;

  // Accessibility
  accessible?: boolean;
  rollInShower?: boolean;
  grabBars?: boolean;

  // Views and location
  balcony?: boolean;
  terrace?: boolean;
  privateEntrance?: boolean;
  groundFloor?: boolean;

  // Additional services
  roomService?: boolean;
  concierge?: boolean;
  dailyHousekeeping?: boolean;
  turndownService?: boolean;

  // Kitchen facilities
  kitchenette?: boolean;
  microwave?: boolean;
  refrigerator?: boolean;
  coffeemaker?: boolean;

  [key: string]: any;
}
