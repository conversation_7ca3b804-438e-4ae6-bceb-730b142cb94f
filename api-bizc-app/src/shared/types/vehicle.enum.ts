/**
 * Vehicle reservation related enums
 */

export enum VehicleReservationStatus {
  INQUIRY = 'INQUIRY',
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  PAID = 'PAID',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  NO_SHOW = 'NO_SHOW',
  REFUNDED = 'REFUNDED',
}

export enum VehiclePickupLocationType {
  BUSINESS_LOCATION = 'BUSINESS_LOCATION',
  CUSTOMER_ADDRESS = 'CUSTOMER_ADDRESS',
  DELIVERY = 'DELIVERY',
}

// PaymentStatus, TaxType, and DiscountType are exported from ../types.ts
// Import them from there to avoid duplication
