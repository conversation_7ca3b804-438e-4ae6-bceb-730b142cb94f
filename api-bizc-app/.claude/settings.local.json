{"permissions": {"allow": ["Bash(yarn db:generate:*)", "Bash(yarn db:migrate:*)", "Bash(yarn lint)", "Bash(yarn build)", "mcp__ide__getDiagnostics", "Bash(yarn lint:*)", "Bash(rm:*)", "Bash(yarn db:push:*)", "Bash(ping:*)", "Bash(npx eslint:*)", "Bash(find:*)", "<PERSON><PERSON>(mv:*)", "Bash(for file in *.dto.ts)", "Bash(do)", "<PERSON><PERSON>(sed:*)", "Bash(done)", "Bash(__NEW_LINE__ mv rental-item-types.service.ts rental-item-categories.service.ts)", "Bash(__NEW_LINE__ mv rental-item-types.controller.ts rental-item-categories.controller.ts)", "Bash(__NEW_LINE__ mv rental-item-types.module.ts rental-item-categories.module.ts)", "Bash(grep:*)", "Bash(yarn build:*)", "Bash(npm run build:*)", "Bash(npx tsc:*)", "Bash(node:*)", "Bash(ls:*)", "Bash(yarn db:drop:*)", "Bash(npx drizzle-kit:*)", "Bash(psql:*)", "Bash(npm run:*)", "<PERSON><PERSON>(pkill:*)"], "deny": []}}